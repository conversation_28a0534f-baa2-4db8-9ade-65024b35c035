import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  json,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getFormattedPhoneNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { Upload } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getBPayOfBusinessAccountExportingExcelFile,
  getListBusinessTransaction,
  getTotalBPayTransaction,
} from '~/services/business-bpay-management.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);

  const [{ sort, rangeDate, search }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['sort', 'rangeDate', 'search'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filterValue = {
    search,
    rangeDate: rangeDate
      ? DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate)
      : {
          from: momentTz().startOf('month').toDate(),
          to: momentTz().endOf('month').toDate(),
        },
  };

  const { isoCode } = await getUserSession({ headers: request.headers });

  const total = await getTotalBPayTransaction({
    filter: filterValue,
    isoCode,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [bPayTransactionData, settingCountry, exportExcelRecords] =
    await Promise.all([
      getListBusinessTransaction({
        skip,
        limit,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
        filter: filterValue,
        isoCode,
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
      getBPayOfBusinessAccountExportingExcelFile({
        filter: filterValue,
        isoCode,
      }),
    ]);

  return json({
    bPayTransactionList: bPayTransactionData?.data || [],
    exportExcelRecords: exportExcelRecords ?? [],
    total,
    filter: filterValue,
    totalRevokeBpayMemberCount:
      bPayTransactionData?.totalAmount[0]?.totalRevokeBpayMemberCount || 0,
    totalTopUpBpayByBtaskeeCount:
      bPayTransactionData?.totalAmount[0]?.totalTopUpBpayByBtaskeeCount || 0,
    totalTopUpBpayMemberCount:
      bPayTransactionData?.totalAmount[0]?.totalTopUpBpayMemberCount || 0,
    currency: settingCountry?.currency?.sign || '',
  });
}, PERMISSIONS.READ_BUSINESS_BPAY);

export default function BPayTransactionListScreen() {
  const { t: tBusinessBPayTransactionManagement } = useTranslation(
    'business-bpay-account-management',
  );
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const {
    currency,
    filter,
    total,
    bPayTransactionList,
    totalRevokeBpayMemberCount,
    totalTopUpBpayByBtaskeeCount,
    totalTopUpBpayMemberCount,
    exportExcelRecords,
  } = useLoaderDataSafely<typeof loader>();

  const permissions = useGlobalStore(store => store.permissions);

  const { exportCsv: exportBPayTransactionList } = useExportFile();
  const confirmExport = useConfirm();

  const onExportExcel = async () => {
    const isConfirm = await confirmExport({
      title: tBusinessBPayTransactionManagement('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {tBusinessBPayTransactionManagement('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tBusinessBPayTransactionManagement('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {total}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tBusinessBPayTransactionManagement('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {format(filter.rangeDate?.from, 'MMMM, yyyy')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tBusinessBPayTransactionManagement('CANCEL'),
      actionButton: tBusinessBPayTransactionManagement('CONFIRM'),
    });

    if (isConfirm) {
      const exportedExcelData = exportExcelRecords?.map(item => ({
        [tBusinessBPayTransactionManagement('BUSINESS_NAME')]:
          item.businessName,
        [tBusinessBPayTransactionManagement('PHONE')]: item.phone,
        [tBusinessBPayTransactionManagement('ADDRESS')]: item.address,
        [tBusinessBPayTransactionManagement('TOTAL_MEMBER')]: item.totalMember,
        [tBusinessBPayTransactionManagement('TOTAL_TOPUP_BPAY_BY_BTASKEE')]:
          item.totalTopUpBpayByBtaskee || 0,
        [tBusinessBPayTransactionManagement('TOTAL_TOPUP_BPAY_MEMBER')]:
          item.totalTopUpBpayMember || 0,
        [tBusinessBPayTransactionManagement('TOTAL_REVOKE_BPAY_MEMBER')]:
          item.totalRevokeBpayMember || 0,
      }));

      const fileName = `${tBusinessBPayTransactionManagement('EXCEL_FILE_NAME')} ${format(
        filter.rangeDate?.from,
        'MMMM, yyyy',
      )}`;

      exportBPayTransactionList({
        fileName,
        arrayJson: exportedExcelData,
      });
    }
  };

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListBusinessTransaction>['data'][0]
    >
  >[] = [
    {
      accessorKey: 'businessName',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('BUSINESS_NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.businessName || ''}</Typography>
      ),
      enableSorting: false,
      size: 80,
    },
    {
      accessorKey: 'phone',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('PHONE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {getFormattedPhoneNumber(row.original?.phone || '')}
        </Typography>
      ),
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'address',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('ADDRESS')}
        />
      ),
      cell: ({ row }) => (
        <Typography
          variant="p"
          className="min-w-[320px] break-words whitespace-pre-wrap">
          {row.original?.address || ''}
        </Typography>
      ),
      enableSorting: false,
      size: 360,
    },
    {
      accessorKey: 'totalMember',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('TOTAL_MEMBER')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.totalMember || '0'}</Typography>
      ),
      enableSorting: false,
      size: 50,
    },
    {
      accessorKey: 'totalTopUpBpayByBtaskee',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement(
            'TOTAL_TOPUP_BPAY_BY_BTASKEE',
          )}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.totalTopUpBpayByBtaskee)}${currency}`}
        </Typography>
      ),
      size: 60,
    },
    {
      accessorKey: 'totalTopUpBpayMember',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('TOTAL_TOPUP_BPAY_MEMBER')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.totalTopUpBpayMember)}${currency}`}
        </Typography>
      ),
      size: 60,
    },
    {
      accessorKey: 'totalRevokeBpayMember',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransactionManagement('TOTAL_REVOKE_BPAY_MEMBER')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.totalRevokeBpayMember)}${currency}`}
        </Typography>
      ),
      size: 60,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="text-center"
          title={tBusinessBPayTransactionManagement('ACTION')}
        />
      ),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted"
              onClick={e => {
                e.stopPropagation();
              }}>
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[140px]">
            <Link
              to={`${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${row.original._id}/${ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BPAY}`}>
              <DropdownMenuItem>
                {tBusinessBPayTransactionManagement('DETAIL')}
              </DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
      size: 50,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tBusinessBPayTransactionManagement('MANAGE_BUSINESS_BPAY_ACCOUNT')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={onExportExcel}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_BUSINESS_BPAY_TRANSACTION)
          }>
          <Upload className="w-5 h-5" />
          {tBusinessBPayTransactionManagement('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={total || 0}
        data={bPayTransactionList || []}
        columns={columns}
        localeAddress="business-bpay-account-management"
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: filter?.search || '',
          name: 'search',
          placeholder: tBusinessBPayTransactionManagement('SEARCH'),
        }}
        filterDate={{
          name: 'rangeDate',
          mode: 'month',
          defaultValue: {
            from: momentTz(filter.rangeDate.from).startOf('month').toDate(),
            to: momentTz(filter.rangeDate.to).endOf('month').toDate(),
          },
        }}
        onClickRow={row =>
          navigate(
            `${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${row._id}/${ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BPAY}`,
          )
        }
        extraContent={
          <div className="flex justify-end gap-6">
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tBusinessBPayTransactionManagement('TOTAL_TOPUP_AMOUNT')}
                </Label>
                <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalTopUpBpayByBtaskeeCount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tBusinessBPayTransactionManagement('TOTAL_USAGE_AMOUNT')}
                </Label>
                <Badge className="bg-secondary text-secondary-foreground rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalTopUpBpayMemberCount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tBusinessBPayTransactionManagement('TOTAL_RESET_AMOUNT')}
                </Label>
                <Badge className="bg-primary-50 text-primary rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalRevokeBpayMemberCount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
          </div>
        }
      />
    </>
  );
}
