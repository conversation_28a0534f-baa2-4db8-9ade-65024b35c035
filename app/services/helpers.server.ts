import { IsoCode, statusOriginal } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage, Types } from 'mongo-connection';
import { getModels } from 'schemas';
import UsersModel from '~/services/model/users.server';
import { getSession } from '~/services/session.server';

export async function getUserId({ request }: { request: Request }) {
  const authSession = await getSession(request.headers.get('cookie'));
  return authSession.get('user')?.userId || '';
}

export const newRecordCommonField = () => ({
  createdAt: momentTz().toDate(),
  _id: new Types.ObjectId().toString(),
  status: statusOriginal.ACTIVE,
});

export async function getUserSession({
  headers,
}: {
  headers: Request['headers'];
}) {
  const authSession = await getSession(headers.get('cookie'));

  // Make sure if not cookie data, redirect login page
  // Just declare for checking by typescript
  const defaultReturnValue = {
    userId: '',
    isSuperUser: false,
    isoCode: '',
    username: '',
    email: '',
  };

  return authSession.get('user') || defaultReturnValue;
}

export async function getCities(isoCode: string) {
  const workingPlaces = await getModels(isoCode)
    .workingPlaces.findOne(
      {
        countryCode: isoCode,
      },
      {
        cities: 1,
      },
    )
    .lean();

  return workingPlaces?.cities?.map(city => city.name) || [];
}

export async function getCitiesByUserId({
  userId,
  isManager,
}: {
  userId: Users['_id'];
  isManager: boolean;
}) {
  const user = await UsersModel.findOne(
    { _id: userId },
    { cities: 1, isoCode: 1 },
  ).lean<Users>();

  if (!user) {
    throw new Error('USER_NOT_FOUND');
  }

  // Manager can access all cities at country by iso code
  if (isManager) {
    if (!user.isoCode) return [];
    return getCities(user.isoCode);
  }

  return user.cities || [];
}

export function getSettingCountryByIsoCode({
  isoCode,
  projection,
}: {
  isoCode: string;
  projection: PipelineStage.Project['$project'];
}) {
  return getModels(isoCode)
    .settingCountry.findOne({ isoCode }, projection)
    .lean();
}

export async function getLanguageByUserId(userId: string) {
  const user = await UsersModel.findById(userId, { language: 1 }).lean<Users>();

  if (!user) {
    return 'en';
  }

  // Default language is English
  return user?.language || 'en';
}

export const getBNPLPaymentMethodCollectionInfoByIsoCode = ({
  isoCode,
}: {
  isoCode: `${IsoCode}`;
}) => {
  if (isoCode === IsoCode.VN) {
    return {
      collectionName: 'vn_paymentMBTransaction',
      transactionIdField: 'ftCode',
    };
  }

  if (isoCode === IsoCode.TH) {
    return {
      collectionName: 'payment2C2PTransaction',
      transactionIdField: 'invoiceNo',
    };
  }

  if (isoCode === IsoCode.ID) {
    return {
      collectionName: 'id_paymentMidtransTransaction',
      transactionIdField: 'transactionId',
    };
  }

  throw new Error(
    `Can not get BNPL payment method information by ${isoCode} IsoCode`,
  );
};
