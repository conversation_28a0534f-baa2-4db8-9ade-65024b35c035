import {
  FA_TRANSACTION_TYPE,
  PAYMENT_METHOD,
  SOURCE_NAME_FOR_FA_TRANSACTION,
  TASK_STATUS,
} from 'btaskee-constants';
import { getAccountingTransactionClosingMongoFilter } from 'btaskee-utils';
import { getModels } from 'schemas';

export async function getListBtaskeeIncome({
  isoCode,
  filter,
}: {
  isoCode: IsoCode;
  filter: {
    rangeDate: { from?: Date; to?: Date };
  };
}) {
  if (!filter?.rangeDate?.from || !filter?.rangeDate?.to) {
    return [];
  }

  const transactions = await getModels(isoCode)
    .FATransaction.find({
      'source.name': SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      type: FA_TRANSACTION_TYPE.CREDIT,
      date: getAccountingTransactionClosingMongoFilter({
        filteredFromDate: filter.rangeDate.from,
        filteredToDate: filter.rangeDate.to,
      }),
    })
    .select({ source: 1, amount: 1, date: 1, userId: 1 })
    .sort({ date: -1 })
    .lean<
      Pick<FATransaction, 'source' | 'amount' | 'date' | '_id' | 'userId'>[]
    >();

  if (!transactions?.length) {
    return [];
  }

  const taskIds =
    transactions.map(transaction => transaction?.source?.value ?? '') ?? [];

  const tasksForTransaction = await getModels(isoCode)
    .task.find({
      _id: { $in: taskIds },
      'payment.method': { $ne: PAYMENT_METHOD.BPAY_BUSINESS },
      status: TASK_STATUS.DONE,
    })
    .select({ acceptedTasker: 1 })
    .lean<Pick<Task, '_id' | 'acceptedTasker'>[]>();

  /**
   * Extracts unique tasker IDs from the acceptedTasker array of each task
   * @description Reduces an array of tasks to a single array of tasker IDs. For each task,
   * it extracts the taskerId from the acceptedTasker array and accumulates them into a single array.
   * If acceptedTasker is undefined or null, it defaults to an empty array.
   * If taskerId is undefined or null, it defaults to an empty string.
   *
   * @example
   * For task collection:
   * [
   *   _id: 'TASK_ID',
   *   {
   *     acceptedTasker: [
   *       { taskerId: 'taskerId1' },
   *       { taskerId: 'taskerId2' }
   *     ]
   *   }
   * ]
   * Returns: taskerIds = ['taskerId1', 'taskerId2']
   */
  const taskerIds =
    tasksForTransaction?.reduce((taskerIds: UserApp['_id'][], currentVal) => {
      if (!currentVal?.acceptedTasker?.length) {
        throw new Error(
          `[Task ID: ${currentVal._id}] Task DONE not have acceptedTasker`,
        );
      }

      const acceptedTaskers =
        currentVal.acceptedTasker.map(tasker => tasker?.taskerId ?? '') ?? [];

      taskerIds.push(...acceptedTaskers);

      return taskerIds;
    }, []) ?? [];

  if (!taskerIds.length) {
    return [];
  }

  const verifiedTransactions: Array<{
    taskId: FATransaction['source']['value'];
    amount: FATransaction['amount'];
    transactionDate: FATransaction['date'];
  }> = [];

  /**
   * Filters all transactions to find ones associated with the TASKER'S ID
   *
   * @throws {Error} When a transaction amount is missing/undefined
   */
  transactions.forEach(transaction => {
    if (!transaction?.amount) {
      throw new Error(
        `[Transaction ID: ${transaction?._id}] Transaction ${transaction?.amount === 0 ? 'Have 0 Value' : 'Not Found'}`,
      );
    }

    if (taskerIds.includes(transaction?.userId ?? '')) {
      verifiedTransactions.push({
        taskId: transaction.source.value,
        amount: transaction.amount,
        transactionDate: transaction.date,
      });
    }
  });

  return verifiedTransactions ?? [];
}
