import {
  type LoaderFunctionArgs,
  type Serialize<PERSON>rom,
  json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import { LIST_TRANSACTION_PAYMENT } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import {
  getCitiesByUserId,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  getPaymentToolKitTransaction,
  getTotalPaymentToolKitTransaction,
} from '~/services/payment-management.server';
import { verifyManager } from '~/services/role-base-access-control.server';

export const willBecomeLoader = async ({ request }: LoaderFunctionArgs) => {
  const { isoCode, userId, isSuperUser } = await getUserSession({
    headers: request.headers,
  });
  const LIST_TRANSACTION_PAYMENT_BY_ISOCODE = LIST_TRANSACTION_PAYMENT.filter(
    payment => payment?.country.indexOf(isoCode) >= 0,
  );
  const url = new URL(request.url);

  const rangeDate =
    url.searchParams.get('createdAt') ||
    JSON.stringify({
      from: momentTz().subtract(1, 'week').startOf('day'),
      to: momentTz().endOf('day'),
    });

  const [{ search, type, paymentMethod, city, sort }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['search', 'type', 'paymentMethod', 'city', 'sort'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filterValue = {
    search,
    type,
    paymentMethod,
    city,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };
  const [total, isManager] = await Promise.all([
    getTotalPaymentToolKitTransaction({
      isoCode,
      filter: filterValue,
    }),
    verifyManager(userId),
  ]);

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [paymentToolkitTransaction, settingCountry, cities] = await Promise.all(
    [
      getPaymentToolKitTransaction({
        isoCode,
        filter: filterValue,
        limit,
        skip,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
      getCitiesByUserId({ userId, isManager: isSuperUser || isManager }),
    ],
  );

  return json({
    LIST_TRANSACTION_PAYMENT_BY_ISOCODE,
    paymentToolkitTransaction,
    total,
    filterValue,
    settingCountry,
    cities,
  });
};

export const useOutletPaymentManagement = () => {
  return useOutletContext<SerializeFrom<typeof willBecomeLoader>>();
};

export const useLoaderPaymentManagement = () => {
  return useLoaderDataSafely<typeof willBecomeLoader>();
};
