import pkg from '@superset-ui/embedded-sdk';
import { <PERSON><PERSON>, <PERSON>ton, Card, Typography } from 'btaskee-ui';
import { useCallback, useEffect, useRef, useState } from 'react';
import { SUPERSET_CONFIG, getDashboardUrl } from '~/config/superset';

const { embedDashboard } = pkg;

export default function SupersetDashboard() {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [useIframe, setUseIframe] = useState(false);
  const dashboardRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Fetch guest token from backend API
  const fetchGuestToken = async () => {
    try {
      console.log('Fetching guest token from API...');
      const response = await fetch('/api/get-superset-token');
      if (!response.ok) {
        throw new Error(`Failed to fetch token: ${response.status}`);
      }
      const { token } = await response.json();
      console.log(
        'Guest token fetched successfully:',
        token ? `Token received: ${token}` : 'No token',
      );
      return token;
    } catch (err) {
      console.error('Error fetching guest token:', err);
      console.log('Falling back to configured token');
      // Fallback to configured token if API fails
      return SUPERSET_CONFIG.GUEST_TOKEN;
    }
  };

  // Embed dashboard using SDK
  const embedSupersetDashboard = useCallback(async () => {
    if (!dashboardRef.current) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('test here')

      await embedDashboard({
        id: 'ab05e976-f7bd-4cec-8e97-f2a2819fa153', // Use the configured dashboard ID
        supersetDomain: 'http://192.168.88.92:8088',
        mountPoint: dashboardRef.current,
        fetchGuestToken: () => {
          return Promise.resolve(
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7InVzZXJuYW1lIjoiR3Vlc3QgVXNlciIsImZpcnN0X25hbWUiOiJHdWVzdCIsImxhc3RfbmFtZSI6IlVzZXIifSwicmVzb3VyY2VzIjpbeyJ0eXBlIjoiZGFzaGJvYXJkIiwiaWQiOiIxIn1dLCJybHNfcnVsZXMiOltdLCJpYXQiOjE3NTM0MjYwMjAsImV4cCI6MTc1MzUxMjQyMCwiYXVkIjoiaHR0cDovL3N1cGVyc2V0X2FwcC8vIiwidHlwZSI6Imd1ZXN0In0.JrLJgXgabdz8WlyZHL3X9quRVKH1hhdf3ndxugH8WhQ',
          );
        },
        // dashboardUiConfig: SUPERSET_CONFIG.DEFAULT_UI_CONFIG,
        referrerPolicy: 'unsafe-url', // Ensure referrer is sent
        debug: true, // Enable debug logging
      });

      setIsLoading(false);
    } catch (err) {
      setError(
        `Embedded mode failed: ${err instanceof Error ? err.message : 'Unknown error'}. Switching to iframe mode.`,
      );
      setUseIframe(true);
      setIsLoading(false);
    }
  }, []);

  // Handle iframe loading
  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError(
      'Failed to load dashboard. Please check your connection and try again.',
    );
  };

  // Retry function
  const handleRetry = () => {
    setIsLoading(true);
    setError(null);

    if (useIframe && iframeRef.current) {
      // Force iframe reload
      const currentSrc = iframeRef.current.src;
      iframeRef.current.src = '';
      setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.src = currentSrc;
        }
      }, 100);
    } else {
      // Retry embedded dashboard
      if (dashboardRef.current) {
        dashboardRef.current.innerHTML = '';
      }
      embedSupersetDashboard();
    }
  };

  // Toggle between embedded and iframe modes
  const toggleMode = () => {
    setUseIframe(!useIframe);
    setIsLoading(true);
    setError(null);

    if (dashboardRef.current) {
      dashboardRef.current.innerHTML = '';
    }
  };

  // Initialize dashboard on component mount
  useEffect(() => {
    if (!useIframe) {
      embedSupersetDashboard();
    } else {
      setIsLoading(false);
    }
  }, [useIframe, embedSupersetDashboard]);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h1">Analytics Dashboard</Typography>
        <div className="flex gap-2">
          <Button onClick={toggleMode} variant="secondary" disabled={isLoading}>
            Switch to {useIframe ? 'Embedded' : 'Iframe'} Mode
          </Button>
          <Button onClick={handleRetry} variant="outline" disabled={isLoading}>
            {isLoading ? 'Loading...' : 'Refresh Dashboard'}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <Typography variant="p">Failed to load dashboard: {error}</Typography>
          <div className="mt-3 space-y-2">
            <Button
              onClick={() => window.open(getDashboardUrl(), '_blank')}
              variant="outline">
              Try Dashboard URL in New Tab
            </Button>
            <Button
              onClick={() => window.open(SUPERSET_CONFIG.HOST, '_blank')}
              variant="outline">
              Open Superset Main Interface
            </Button>
          </div>
        </Alert>
      )}

      <Card className="w-full">
        {useIframe ? (
          <div className="w-full min-h-[800px] relative">
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <Typography variant="p" className="text-blue-800">
                <strong>Iframe Mode:</strong> If the iframe is blocked or shows
                a 404 error, the dashboard ID might be incorrect. Use the
                buttons below to access Superset directly.
              </Typography>
              <div className="mt-2 space-x-2">
                <Button
                  onClick={() => window.open(getDashboardUrl(), '_blank')}
                  variant="outline"
                  size="sm">
                  Try Dashboard URL
                </Button>
                <Button
                  onClick={() => window.open(SUPERSET_CONFIG.HOST, '_blank')}
                  variant="outline"
                  size="sm">
                  Open Superset Main
                </Button>
              </div>
            </div>
            <iframe
              ref={iframeRef}
              src={getDashboardUrl()}
              className="w-full h-[800px] border-0"
              title="Superset Dashboard"
              onLoad={handleIframeLoad}
              onError={handleIframeError}
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-white">
                <div className="text-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <Typography variant="p" className="text-muted-foreground">
                    Loading Superset Dashboard (iframe mode)...
                  </Typography>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full min-h-[800px] relative">
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <Typography variant="p" className="text-green-800">
                <strong>Embedded Mode:</strong> Using Superset Embedded SDK with
                guest token authentication. This provides better integration and
                user experience.
              </Typography>
              <div className="mt-2 space-x-2">
                <Button
                  onClick={() => window.open(SUPERSET_CONFIG.HOST, '_blank')}
                  variant="outline"
                  size="sm">
                  Open Superset Main
                </Button>
              </div>
            </div>
            <div
              ref={dashboardRef}
              className="w-full h-[800px] border-0"
              style={{ minHeight: '800px' }}
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-white">
                <div className="text-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <Typography variant="p" className="text-muted-foreground">
                    Loading Superset Dashboard (embedded mode)...
                  </Typography>
                </div>
              </div>
            )}
          </div>
        )}
      </Card>

      <div className="text-sm text-muted-foreground space-y-2">
        <Typography variant="p" className="text-sm">
          <strong>Dashboard URL:</strong> {getDashboardUrl()}
        </Typography>
        <Typography variant="p" className="text-sm">
          <strong>Dashboard ID:</strong> {SUPERSET_CONFIG.DEFAULT_DASHBOARD_ID}
        </Typography>
        <Typography variant="p" className="text-sm">
          <strong>Superset Host:</strong> {SUPERSET_CONFIG.HOST}
        </Typography>
        <Typography variant="p" className="text-sm">
          <strong>Status:</strong>{' '}
          {isLoading ? 'Loading...' : error ? 'Error' : 'Loaded'} (
          {useIframe ? 'iframe' : 'embedded'} mode)
        </Typography>
      </div>
    </div>
  );
}
