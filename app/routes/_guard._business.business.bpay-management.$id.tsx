import {
  Outlet,
  isRouteErrorResponse,
  json,
  useLocation,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import {
  BUSINESS_ACCOUNT_STATUS,
  BUSINESS_ACCOUNT_STATUS_CLASS,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Separator,
  StatusBadge,
  Tabs,
  TabsList,
  TabsTrigger,
  Typography,
  toast,
} from 'btaskee-ui';
import { formatNumberWithCommas, getFormattedPhoneNumber } from 'btaskee-utils';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getDetailAccountBusinessById } from '~/services/business-bpay-management.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export const handle = {
  breadcrumb: ({
    accountBusinessId,
  }: {
    accountBusinessId: BusinessTransaction['_id'];
  }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${accountBusinessId}/transaction-into-bpay`}
      label="BUSINESS_DETAILS"
    />
  ),
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const [accountBusinessDetail, settingCountry] = await Promise.all([
    getDetailAccountBusinessById({
      isoCode,
      accountBusinessId: params.id || '',
    }),
    getSettingCountryByIsoCode({
      isoCode,
      projection: {
        currency: 1,
      },
    }),
  ]);

  return json({
    accountBusinessDetail,
    accountBusinessId: params.id,
    settingCountry,
  });
}, PERMISSIONS.READ_ACCOUNTING_BUSINESS_ACCOUNT_DETAIL);

export default function AccountBusinessDetailIndex() {
  const { t: tAccountBusinessDetail } = useTranslation(
    'business-bpay-account-detail',
  );
  const {
    accountBusinessDetail,
    accountBusinessId,
    settingCountry,
    error: loaderError,
  } = useLoaderDataSafely<typeof loader>();

  const location = useLocation();

  useEffect(() => {
    if (loaderError) {
      toast({ description: tAccountBusinessDetail(loaderError) });
    }
  }, [loaderError, tAccountBusinessDetail]);

  const navigate = useNavigate();

  const generalInfo = useMemo(() => {
    return [
      {
        label: tAccountBusinessDetail('BUSINESS_NAME'),
        value: accountBusinessDetail?.accountBusiness?.name || '',
      },
      {
        label: tAccountBusinessDetail('PHONE'),
        value: `${getFormattedPhoneNumber(accountBusinessDetail?.askerData?.phone || '')}`,
      },
      {
        label: tAccountBusinessDetail('ADDRESS'),
        value: accountBusinessDetail?.askerData?.address || '',
      },
      {
        label: tAccountBusinessDetail('TOTAL_MEMBERS'),
        value: accountBusinessDetail?.totalMembers || '0',
      },
      {
        label: tAccountBusinessDetail('BALANCE_OF_BPAY'),
        value: `${formatNumberWithCommas(accountBusinessDetail?.accountBusiness?.bPay || 0)} ${settingCountry?.currency?.code || ''}`,
      },
      {
        label: tAccountBusinessDetail('STATUS'),
        value: accountBusinessDetail?.accountBusiness?.status || '',
        isBadge: true,
      },
    ];
  }, [accountBusinessDetail, settingCountry?.currency, tAccountBusinessDetail]);

  return (
    <>
      {[
        ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BPAY,
        ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BUSINESS_BPAY,
        ROUTE_NAME.BUSINESS_BPAY_MEMBER_BPAY,
        'deposit-dialog',
      ].includes(location.pathname.split('/').pop() || '') ? (
        <>
          <div className="mb-6 flex items-center justify-between rounded-md bg-secondary p-4">
            <div className="grid space-y-2">
              <Typography variant="h2" className="capitalize">
                {tAccountBusinessDetail('BUSINESS_DETAILS')}
              </Typography>
              <Breadcrumbs />
            </div>
          </div>
          <div className="rounded-[15px] border border-gray-200 bg-gray-50 p-6">
            <div className="w-fit">
              <Typography className="mb-3 text-xl font-semibold -tracking-[0.5%]">
                {tAccountBusinessDetail('GENERAL_INFORMATION')}
              </Typography>
              <Separator className="mb-6" />
            </div>
            <div className="grid grid-cols-2 gap-6 md:grid-cols-3">
              {generalInfo.map((info, idx) => (
                <div key={idx}>
                  <Typography className="mb-1 text-sm font-normal leading-[18px] text-gray-400 ">
                    {info.label}
                  </Typography>
                  {info.isBadge ? (
                    <StatusBadge
                      status={info.value}
                      statusClasses={BUSINESS_ACCOUNT_STATUS_CLASS}
                      translationKey="business-bpay-account-detail"
                    />
                  ) : (
                    <Typography className="break-words text-lg font-semibold text-gray-600">
                      {info.value}
                    </Typography>
                  )}
                </div>
              ))}
            </div>
          </div>
          {accountBusinessDetail?.accountBusiness?.status ===
          BUSINESS_ACCOUNT_STATUS.ACTIVE ? (
            <div>
              <Separator className="my-8" />
              <Tabs
                value={location.pathname.split('/').pop() ?? ''}
                onValueChange={value =>
                  navigate(
                    `${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${accountBusinessId}/${value}`,
                  )
                }
                className="space-y-4 mb-6">
                <TabsList>
                  <TabsTrigger
                    value={ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BPAY}>
                    {tAccountBusinessDetail('DEPOSIT_TRANSACTIONS')}
                  </TabsTrigger>
                  <TabsTrigger
                    value={ROUTE_NAME.BUSINESS_TRANSACTION_INTO_BUSINESS_BPAY}>
                    {tAccountBusinessDetail('DEPOSIT_INTO_BPAY')}
                  </TabsTrigger>
                  <TabsTrigger value={ROUTE_NAME.BUSINESS_BPAY_MEMBER_BPAY}>
                    {tAccountBusinessDetail('MEMBER_BUSINESS_BPAY')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          ) : null}
        </>
      ) : null}
      <Outlet />
    </>
  );
}
