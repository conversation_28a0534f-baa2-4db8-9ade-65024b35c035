import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { TASKER_BNPL_PROCESS_STATUS } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  Label,
  StatusBadge,
  Typography,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletInstallmentManagement } from '~/hooks/useInstallmentManagement';
import type { getInstallmentManagement } from '~/services/installment-management.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function InstallmentManagementIndex() {
  const { t } = useTranslation('installment-management');

  const [searchParams] = useSearchParams();

  const outletData = useOutletInstallmentManagement();

  const currency = useMemo(() => {
    return outletData?.settingCountry?.currency?.sign || '';
  }, [outletData]);

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getInstallmentManagement>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('DATE')} />
        ),
        cell: ({ row }) => (
          <div>{format(row.getValue('createdAt'), 'dd/MM/yyyy')}</div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.name}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.phone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.phone}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('STATUS')}
            className="w-auto"
          />
        ),
        cell: ({ row }) => (
          <StatusBadge
            statusClasses={{
              PAYING: 'bg-blue-50 text-blue-500 rounded-md text-center',
              DONE: 'bg-green-50 text-green-500 rounded-md text-center',
              OVER_DUE: 'bg-yellow-50 text-yellow-500 rounded-md text-center',
              STOPPED: 'bg-red-50 text-red-500 rounded-md text-center',
            }}
            status={row.original?.status}
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'expiredAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('DEADLINE')} />
        ),
        cell: ({ row }) => (
          <div>{format(row.getValue('expiredAt'), 'dd/MM/yyyy')}</div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('AMOUNT')} />
        ),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.amount ?? 0)}${currency}`}</div>
        ),
        size: 130,
      },
      {
        accessorKey: 'paidAmount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PAID_AMOUNT')} />
        ),
        cell: ({ row }) => {
          const totalAmount = row.original?.amount ?? 0;
          const remainingAmount = row.original?.remainingAmount ?? 0;

          return (
            <Typography variant="p">{`${formatNumberWithCommas(totalAmount - remainingAmount)}${currency}`}</Typography>
          );
        },
        size: 150,
      },
      {
        accessorKey: 'remainingAmount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('AMOUNT_OWED')} />
        ),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.remainingAmount ?? 0)}${currency}`}</div>
        ),
        size: 150,
      },
    ],
    [currency, t],
  );

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">{t('INSTALLMENT_MANAGEMENT')}</Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        data={outletData?.installmentPayment}
        total={outletData?.total}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH_NAME_PHONE'),
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(outletData?.filterValue?.rangeDate?.from).toDate(),
            to: momentTz(outletData?.filterValue?.rangeDate?.to).toDate(),
          },
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.LAST_7_DAYS,
        }}
        filters={[
          {
            placeholder: t('STATUS'),
            name: 'status',
            options: Object.values(TASKER_BNPL_PROCESS_STATUS).map(status => ({
              label: t(status),
              value: status,
            })),
            value: outletData?.filterValue?.status,
          },
        ]}
        columnPinningFromOutSide={{
          right: ['amount', 'paidAmount', 'remainingAmount'],
        }}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(
                  outletData?.totals?.totalAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT_PAID')}
              </Label>
              <Badge className="bg-green-50 text-green-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  outletData?.totals?.totalPaidAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT_OWED')}
              </Label>
              <Badge className="bg-orange-50 text-orange-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  outletData?.totals?.totalRemainingAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
