import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import {
  COMMON_MIN_PHONE_LENGTH,
  PERMISSIONS,
  RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
  TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
  res404,
} from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  Grid,
  Input,
  Label,
  SelectBase,
  Separator,
  SingleMonthPicker,
  SingleYearPicker,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getFormattedPhoneNumber,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { endOfMonth, format } from 'date-fns';
import { Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getTaskIncomeTaxTransactionsByTaskerPhone } from '~/services/tasker-income-tax.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  if (!getEnvFeatureFlag().Income_Tax_Report) {
    throw new Response(null, res404);
  }

  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const [{ date, taskerPhone, filterOption }] = getValuesFromSearchParams(
    url.searchParams,
    {
      keysString: ['date', 'taskerPhone', 'filterOption'],
    },
  );

  const filteredValue = {
    taskerPhone,
    date: date ? momentTz(date).toDate() : new Date(),
    filterOption:
      filterOption || RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH,
    isoCode,
  };

  const exportingData =
    await getTaskIncomeTaxTransactionsByTaskerPhone(filteredValue);

  return json({
    exportingData,
    ...(exportingData?.warningMessage
      ? { warningMessage: exportingData.warningMessage }
      : {}),
    filteredValue,
  });
}, PERMISSIONS.EXPORT_INCOME_TAX_REPORT_DETAIL);

export default function IncomeTaxReportScreen() {
  const { t: tIncomeTaxReport } = useTranslation('income-tax-report-detail');
  const permissions = useGlobalStore(store => store?.permissions);
  const { exportCsvFromArray: exportReportIncomeTax } = useExportFile();
  const [selectedRangeDateMode, setSelectedRangeDateMode] = useState<string>(
    RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH,
  );
  const confirmExport = useConfirm();
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    error: loaderError,
    exportingData,
    warningMessage,
    filteredValue,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: tIncomeTaxReport(loaderError) });
  }, [loaderError, tIncomeTaxReport]);

  useEffect(() => {
    if (filteredValue?.taskerPhone && warningMessage)
      toast({
        variant: 'warning',
        description: tIncomeTaxReport(warningMessage),
      });
  }, [filteredValue, warningMessage, tIncomeTaxReport]);

  const onExportExcel = async () => {
    const formatDate =
      selectedRangeDateMode ===
      RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH
        ? 'MM.yyyy'
        : 'yyyy';
    const filteredTimeValue = format(
      filteredValue?.date || new Date(),
      formatDate,
    );

    function getAmountExportingCompensation({
      incomeTaxable,
      incomeTax,
    }: MustBeAny) {
      const amountInfo: MustBeAny = {
        deductMoneyToMainAccount: '',
        addMoneyToMainAccount: '',
        deductMoneyToSubAccount: '',
        addMoneyToSubAccount: '',
      };

      if (
        incomeTaxable?.accountType ===
        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT
      ) {
        amountInfo.addMoneyToMainAccount =
          incomeTaxable?.amount?.toLocaleString() ?? '';
      }

      if (
        incomeTaxable?.accountType ===
        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.PROMOTION_ACCOUNT
      ) {
        amountInfo.addMoneyToSubAccount =
          incomeTaxable?.amount?.toLocaleString() ?? '';
      }

      if (
        incomeTax?.accountType ===
        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT
      ) {
        amountInfo.deductMoneyToMainAccount =
          incomeTax?.amount?.toLocaleString() ?? '';
      }

      if (
        incomeTax?.accountType ===
        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.PROMOTION_ACCOUNT
      ) {
        amountInfo.deductMoneyToSubAccount =
          incomeTax?.amount?.toLocaleString() ?? '';
      }

      return amountInfo;
    }

    const isConfirm = await confirmExport({
      title: tIncomeTaxReport('TITLE_EXPORT_INCOME_TAX'),
      body: (
        <div>
          <Typography variant="p">
            {tIncomeTaxReport('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tIncomeTaxReport('REPORT_COMPENSATION_INCOME_TAX')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {(exportingData?.reportingCompensations?.length ?? 0) +
                (exportingData?.reportingReductionTransactions?.length ?? 0)}
            </Typography>
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tIncomeTaxReport('REPORT_TASK_INCOME_TAX')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {exportingData?.reportingTasks?.length ?? 0}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tIncomeTaxReport('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {`${filteredTimeValue}`}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tIncomeTaxReport('CANCEL'),
      actionButton: tIncomeTaxReport('CONFIRM'),
    });

    if (isConfirm) {
      const compensations =
        exportingData?.reportingCompensations?.map((transactionInfo, index) => {
          const amountInfo = getAmountExportingCompensation({
            incomeTaxable: transactionInfo?.incomeTaxableTransaction,
            incomeTax: transactionInfo?.incomeTaxTransaction,
          });

          return [
            index + 1,
            transactionInfo?.incomeTaxableTransaction?.createdAt
              ? format(
                  transactionInfo?.incomeTaxableTransaction?.createdAt,
                  'HH:mm dd/MM/yyyy',
                )
              : '',
            transactionInfo?.incomeTaxableTransaction?.source?.reason ??
              transactionInfo?.incomeTaxableTransaction?.source?.name ??
              '',
            transactionInfo?.incomeTaxableTransaction?.source?.taskId ?? '',
            transactionInfo?.incomeTaxableTransaction?._id ?? '',
            amountInfo.addMoneyToMainAccount?.toLocaleString() ?? '',
            amountInfo.deductMoneyToMainAccount?.toLocaleString() ?? '',
            amountInfo.addMoneyToSubAccount?.toLocaleString() ?? '',
            amountInfo.deductMoneyToSubAccount?.toLocaleString() ?? '',
          ];
        }) ?? [];

      const deductions = exportingData?.reportingReductionTransactions.map(
        (transaction, index) => [
          index + 1,
          transaction?.createdAt
            ? format(transaction?.createdAt, 'HH:mm dd/MM/yyyy')
            : '',
          transaction?.source?.name,
          transaction?.source?.taskId ?? '',
          transaction?._id ?? '',
          '',
          transaction?.amount?.toLocaleString() ?? '',
          '',
          '',
        ],
      );

      exportReportIncomeTax({
        fileName: `${tIncomeTaxReport('INCOME_TAX_REPORT_FILE_NAME')} - ${exportingData?.taskerInfo?.phone ?? ''} - ${filteredTimeValue}.xlsx`,
        sheets: [
          {
            sheetName: tIncomeTaxReport('REPORT_COMPENSATION_INCOME_TAX'),
            data: [
              [
                tIncomeTaxReport('ORDER_NUMBER'),
                tIncomeTaxReport('TIME'),
                tIncomeTaxReport('REASON'),
                tIncomeTaxReport('TASK_ID'),
                tIncomeTaxReport('TRANSACTION_ID'),
                tIncomeTaxReport('ADD_MONEY_TO_MAIN_ACCOUNT'),
                tIncomeTaxReport('DEDUCT_MONEY_TO_MAIN_ACCOUNT'),
                tIncomeTaxReport('ADD_MONEY_TO_SUB_ACCOUNT'),
                tIncomeTaxReport('DEDUCT_MONEY_TO_SUB_ACCOUNT'),
              ],
              ...compensations
                .concat(deductions)
                .sort((a, b) => b?.[1] - a?.[1]),
            ],
          },
          {
            sheetName: tIncomeTaxReport('REPORT_TASK_INCOME_TAX'),
            data: [
              [
                tIncomeTaxReport('ORDER_NUMBER'),
                tIncomeTaxReport('TASK_ID'),
                tIncomeTaxReport('DONE_TASK_DATE'),
                tIncomeTaxReport('80_PERCENTAGE_TASKER_INCOME_AMOUNT'),
                tIncomeTaxReport('INCOME_TAX_AMOUNT'),
                tIncomeTaxReport('ACTUAL_AMOUNT'),
              ],
              ...(exportingData?.reportingTasks?.map((transaction, index) => [
                index + 1,
                transaction?.taskId ?? '',
                transaction?.doneTaskDate
                  ? format(transaction?.doneTaskDate, 'HH:mm dd/MM/yyyy')
                  : '',
                transaction?.taskerIncomeTaxableAmount?.toLocaleString(),
                transaction?.taskerIncomeTaxAmount?.toLocaleString(),
                (transaction?.taskerActualIncomeAmount ?? 0).toLocaleString(),
              ]) ?? []),
            ],
          },
        ],
      });
    }
  };

  const { control, handleSubmit, watch } = useForm<{ searchText: string }>({
    defaultValues: {
      searchText: filteredValue.taskerPhone,
    },
  });

  const onSubmitSearchByTaskerPhone = ({
    taskerPhone,
  }: {
    taskerPhone: string;
  }) => {
    setSearchParams(params => {
      params.set('taskerPhone', taskerPhone);

      return params;
    });
  };

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tIncomeTaxReport('INCOME_TAX_REPORT_DETAIL')}
          </Typography>
        </Grid>
      </div>
      <Grid className="flex gap-4">
        <SelectBase
          defaultValue={selectedRangeDateMode}
          backgroundColor="h-8 w-36"
          onValueChange={option => {
            setSearchParams(params => {
              params.delete('date');
              params.set('filterOption', option);

              return params;
            });

            setSelectedRangeDateMode(option);
          }}
          options={Object.values(
            RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
          ).map(rangeDateMode => ({
            label: tIncomeTaxReport(rangeDateMode),
            value: rangeDateMode,
          }))}
        />
        <div className="col-span-5">
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH ? (
            <SingleMonthPicker
              onMonthSelect={value => {
                setSearchParams(params => {
                  params.set('date', endOfMonth(value).toString());

                  return params;
                });
              }}
              defaultMonth={new Date(filteredValue.date)}
            />
          ) : null}
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_YEAR ? (
            <SingleYearPicker
              onYearSelect={value => {
                setSearchParams(params => {
                  params.set('date', endOfMonth(value).toString());

                  return params;
                });
              }}
              defaultYear={new Date(filteredValue.date)}
            />
          ) : null}
        </div>
        <form
          className="flex gap-1"
          onSubmit={handleSubmit(data =>
            onSubmitSearchByTaskerPhone({
              taskerPhone: data?.searchText ?? '',
            }),
          )}>
          <Controller
            control={control}
            name="searchText"
            rules={{
              required: tIncomeTaxReport('THIS_FIELD_IS_REQUIRED'),
            }}
            render={({ field: { onChange, value } }) => (
              <Input
                type="search"
                defaultValue={value}
                placeholder={tIncomeTaxReport('SEARCH_PLACEHOLDER')}
                onChange={event => {
                  const target = event.target as HTMLInputElement;
                  onChange(target?.value ?? '');
                }}
                onKeyDown={event => {
                  if (event.key === 'Enter') {
                    onSubmitSearchByTaskerPhone({
                      taskerPhone: value,
                    });
                  }
                }}
                className="block h-8 w-[150px] pl-8 lg:w-[250px]"
              />
            )}
          />
          <Button
            disabled={watch('searchText').length < COMMON_MIN_PHONE_LENGTH}
            className="h-8"
            type="submit">
            <Search />
          </Button>
        </form>
        <Button
          disabled={
            !permissions.includes(
              PERMISSIONS.EXPORT_INCOME_TAX_REPORT_DETAIL,
            ) ||
            !(
              (exportingData?.reportingCompensations?.length ?? 0) +
              (exportingData?.reportingReductionTransactions?.length ?? 0) +
              (exportingData?.reportingTasks?.length ?? 0)
            )
          }
          className="h-8"
          onClick={onExportExcel}>
          {tIncomeTaxReport('EXPORT_EXCEL_FILE')}
        </Button>
      </Grid>
      <div className="flex gap-2">
        <Card className="w-80 mt-6">
          <CardContent className="p-2">
            <Typography
              variant="p"
              className="text-gray-800 text-base font-bold"
              affects="removePMargin">
              {tIncomeTaxReport('REPORT_COMPENSATION_INCOME_TAX')}
            </Typography>
            <Typography className="text-green-500" variant="h4" affects="large">
              {(exportingData?.reportingCompensations?.length ?? 0) +
                (exportingData?.reportingReductionTransactions?.length ?? 0)}
            </Typography>
          </CardContent>
        </Card>
        <Card className="w-[300px] mt-6">
          <CardContent className="p-2">
            <Typography
              variant="p"
              className="text-gray-800 text-base font-bold"
              affects="removePMargin">
              {tIncomeTaxReport('REPORT_TASK_INCOME_TAX')}
            </Typography>
            <Typography className="text-green-500" variant="h4" affects="large">
              {exportingData?.reportingTasks?.length ?? 0}
            </Typography>
          </CardContent>
        </Card>
      </div>
      {searchParams.get('taskerPhone') ? (
        <Card className="w-80 mt-4">
          <CardContent className="p-2">
            <Typography
              variant="p"
              className="text-gray-800 text-base font-bold"
              affects="removePMargin">
              {tIncomeTaxReport('TASKER_INFORMATION')}
            </Typography>
            {exportingData?.taskerInfo?.phone ? (
              <div className="flex gap-3 mt-3">
                <div>
                  <Typography variant="p" affects="removePMargin">
                    {tIncomeTaxReport('TASKER_NAME')}
                  </Typography>
                  <Typography variant="p" affects="removePMargin">
                    {tIncomeTaxReport('TASKER_PHONE')}
                  </Typography>
                  <Typography variant="p" affects="removePMargin">
                    {tIncomeTaxReport('TASKER_STATUS')}
                  </Typography>
                </div>
                <div>
                  <Typography variant="p" affects="removePMargin">
                    {exportingData?.taskerInfo.name ?? ''}
                  </Typography>
                  <Typography variant="p" affects="removePMargin">
                    {getFormattedPhoneNumber(
                      exportingData?.taskerInfo.phone ?? '',
                    )}
                  </Typography>
                  <Typography variant="p" affects="removePMargin">
                    {tIncomeTaxReport(exportingData?.taskerInfo.status ?? '')}
                  </Typography>
                </div>
              </div>
            ) : (
              <Typography
                variant="p"
                className="text-red-500 mt-2"
                affects="removePMargin">
                {tIncomeTaxReport('TASKER_NOT_FOUND')}
              </Typography>
            )}
          </CardContent>
        </Card>
      ) : null}
    </>
  );
}
