import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  BTASKEE_LANGUAGE,
  DATE_RANGE_OPTIONS,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  Badge,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Grid,
  InfoBlock,
  InfoCardWrapper,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getFormattedPhoneNumber,
  getRangeByPresetOptions,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import i18next from 'i18next';
import { Upload } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import useGetMemberTransactionForSalesReport from '~/hooks/useGetMemberTransactionForSalesReport';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getSaleReportDetail } from '~/services/sales-report.server';
import { getServices } from '~/services/service.server';

export const handle = {
  breadcrumb: ({ businessId }: { businessId: AccountBusiness['_id'] }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.SALES_REPORT_IN_ACCOUNTING}/${businessId ?? ''}`}
      label="SALES_REPORT_DETAIL"
    />
  ),
  i18n: 'sales-report-detail',
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const url = new URL(request.url);

  const { isoCode } = await getUserSession({ headers: request.headers });

  const [{ rangeDate, searchText, serviceIds }] = getValuesFromSearchParams(
    url.searchParams,
    {
      keysString: ['rangeDate', 'searchText', 'serviceIds'],
      keysNumber: ['pageSize', 'pageIndex'],
    },
  );

  const filteredRangeDate =
    rangeDate && rangeDate !== 'null' && rangeDate !== 'undefined'
      ? {
          from: momentTz(JSON.parse(rangeDate).from).toDate(),
          to: momentTz(JSON.parse(rangeDate).to).toDate(),
        }
      : getRangeByPresetOptions(DATE_RANGE_OPTIONS.THIS_MONTH);

  const filteredSaleReport = {
    ...(filteredRangeDate ? { rangeDate: filteredRangeDate } : {}),
    searchText,
    serviceIds,
  };

  const [salesReportDetail, settingCountry, services] = await Promise.all([
    getSaleReportDetail({
      isoCode,
      filter: filteredSaleReport,
      businessId: params?.id ?? '',
    }),
    getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    }),
    getServices({
      isoCode,
      projection: { text: 1 },
    }),
  ]);

  return json({
    salesReportDetail,
    businessId: params?.id ?? '',
    filteredSaleReport,
    settingCountry,
    services,
  });
}, PERMISSIONS.READ_SALES_REPORT);

export default function SaleReportDetail() {
  const { t: tReportDetail } = useTranslation('sales-report-detail');
  const { salesReportDetail, filteredSaleReport, settingCountry, services } =
    useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);
  const [searchParams] = useSearchParams();
  const { exportCsv: exportSaleReport } = useExportFile();
  const confirmExport = useConfirm();
  const { transactions } = useGetMemberTransactionForSalesReport({
    transactions: salesReportDetail?.businessMemberTransactions ?? [],
  });

  const reportInfos = useMemo(() => {
    return {
      totalPrice: transactions.reduce(
        (acc, currentVal) => acc + currentVal.taskPrice,
        0,
      ),
      totalValueOfPromotion: transactions.reduce(
        (acc, currentVal) => acc + currentVal.promotionValue,
        0,
      ),
      totalCustomerPay: transactions.reduce(
        (acc, currentVal) => acc + currentVal.customerPay,
        0,
      ),
      totalCollectionFee: transactions.reduce(
        (acc, currentVal) => acc + currentVal.collectionFee,
        0,
      ),
      totalMainAccountTasker: transactions.reduce(
        (acc, currentVal) => acc + currentVal.mainAccountTasker,
        0,
      ),
      totalPromotionAccount: transactions.reduce(
        (acc, currentVal) => acc + currentVal.promotionAccountTasker,
        0,
      ),
    };
  }, [transactions]);

  const columns: ColumnDef<
    ReturnType<typeof useGetMemberTransactionForSalesReport>['transactions'][0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('TRANSACTION_DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p">
              {momentTz(row.original.createdAt).format('HH:mm - DD/MM/YYYY')}
            </Typography>
          ) : null,
        enableSorting: false,
      },
      {
        accessorKey: 'taskDoneAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('DONE_TASK_DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.transactionDate ? (
            <Typography variant="p">
              {momentTz(row.original.transactionDate).format(
                'HH:mm - DD/MM/YYYY',
              )}
            </Typography>
          ) : null,
        enableSorting: false,
      },
      {
        accessorKey: 'customerName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('CUSTOMER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.asker?.name ?? ''}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'phoneNumber',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('PHONE_NUMBER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {getFormattedPhoneNumber(row.original?.asker?.phone ?? '')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'service',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('SERVICE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original?.task?.serviceText?.[
              i18next.language ?? BTASKEE_LANGUAGE.EN
            ] ?? ''}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'promotionCode',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('PROMOTION_CODE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original?.task?.promotion?.code ?? ''}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original?.tasker?.name ?? ''}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerPhoneNumber',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('TASKER_PHONE_NUMBER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {getFormattedPhoneNumber(row.original?.tasker?.phone ?? '')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'price',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('PRICE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.taskPrice ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'valueOfPromotion',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('VALUE_OF_PROMOTION')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.promotionValue ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'customerPay',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('CUSTOMER_PAY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.customerPay ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'collectionFee',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('COLLECTION_FEE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.collectionFee ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'mainAccountTasker',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('MAIN_ACCOUNT_TASKER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.mainAccountTasker ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'promotionAccountTasker',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tReportDetail('PROMOTION_ACCOUNT_TASKER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {`${formatNumberWithCommas(row.original?.promotionAccountTasker ?? 0)}${settingCountry?.currency?.sign}`}
          </Typography>
        ),
        enableSorting: false,
      },
    ],
    [settingCountry?.currency?.sign, tReportDetail],
  );

  const onExportExcel = async () => {
    const exportedExcelData = transactions?.map(transaction => ({
      [tReportDetail('TRANSACTION_DATE')]: transaction?.createdAt
        ? momentTz(transaction.createdAt).format('HH:mm - DD/MM/YYYY')
        : '',
      [tReportDetail('DONE_TASK_DATE')]: transaction?.transactionDate
        ? momentTz(transaction.transactionDate).format('HH:mm - DD/MM/YYYY')
        : '',
      [tReportDetail('TASK_ID')]: transaction?.task?._id ?? '',
      [tReportDetail('CUSTOMER_NAME')]: transaction?.asker?.name ?? '',
      [tReportDetail('PHONE_NUMBER')]: transaction?.asker?.phone ?? '',
      [tReportDetail('SERVICE')]:
        transaction?.task?.serviceText?.[
          i18next.language ?? BTASKEE_LANGUAGE.EN
        ] ?? '',
      [tReportDetail('PROMOTION_CODE')]:
        transaction?.task?.promotion?.code ?? '',
      [tReportDetail('TASKER_NAME')]: transaction?.tasker?.name ?? '',
      [tReportDetail('TASKER_PHONE_NUMBER')]: getFormattedPhoneNumber(
        transaction.tasker?.phone ?? '',
      ),
      [tReportDetail('PRICE')]: transaction?.taskPrice ?? 0,
      [tReportDetail('VALUE_OF_PROMOTION')]: transaction?.promotionValue ?? 0,
      [tReportDetail('CUSTOMER_PAY')]: transaction?.customerPay ?? 0,
      [tReportDetail('COLLECTION_FEE')]: transaction?.collectionFee ?? 0,
      [tReportDetail('MAIN_ACCOUNT_TASKER')]:
        transaction?.mainAccountTasker ?? 0,
      [tReportDetail('PROMOTION_ACCOUNT_TASKER')]:
        transaction?.promotionAccountTasker ?? 0,
    }));

    const fileName = `List ${salesReportDetail?.business?.name} Business Sale Report from ${momentTz(filteredSaleReport?.rangeDate?.from).format('DD/MM/YYYY')} to ${momentTz(filteredSaleReport?.rangeDate?.to).format('DD/MM/YYYY')}`;

    const isConfirm = await confirmExport({
      title: tReportDetail('EXPORT_TO_EXCEL'),
      body: tReportDetail('ARE_YOU_SURE_EXPORT_TO_EXCEL'),
      cancelButton: tReportDetail('CANCEL'),
      actionButton: tReportDetail('CONFIRM'),
    });

    if (isConfirm) {
      exportSaleReport({
        fileName,
        arrayJson: exportedExcelData,
      });
    }
  };

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tReportDetail('SALES_REPORT_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={onExportExcel}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_LIST_SALES_REPORT)
          }>
          <Upload className="w-5 h-5" />
          {tReportDetail('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <InfoCardWrapper className="col-span-2 mb-10">
        <div className="w-fit">
          <Typography className="mb-3 pr-20" affects="large" variant="h4">
            {tReportDetail('GENERAL_INFORMATION')}
          </Typography>
          <Separator />
        </div>
        <div className="grid grid-cols-2 gap-6">
          <InfoBlock
            label={tReportDetail('BUSINESS_NAME')}
            value={salesReportDetail?.business?.name ?? ''}
          />
          <InfoBlock
            label={tReportDetail('BUSINESS_PHONE_NUMBER')}
            value={getFormattedPhoneNumber(
              salesReportDetail?.businessFromUser?.phone ?? '',
            )}
          />
        </div>
      </InfoCardWrapper>
      <DataTableBasic
        columns={columns}
        data={transactions ?? []}
        searchInput={{
          defaultValue: searchParams.get('searchText') || '',
          name: 'searchText',
          placeholder: tReportDetail('SEARCH_BY_ASKER_OR_TASKER_PHONE'),
        }}
        translationKey="sales-report-detail"
        initialFilterDate={{
          name: 'rangeDate',
          defaultValue: {
            from: momentTz(filteredSaleReport?.rangeDate?.from).toDate(),
            to: momentTz(filteredSaleReport?.rangeDate?.to).toDate(),
          },
        }}
        pinColumns={{
          right: [
            'price',
            'valueOfPromotion',
            'customerPay',
            'collectionFee',
            'mainAccountTasker',
            'promotionAccountTasker',
          ],
        }}
        initialFilters={[
          {
            name: 'serviceIds',
            placeholder: tReportDetail('SERVICE'),
            options: services.map(service => ({
              label:
                service?.text?.[i18next.language ?? BTASKEE_LANGUAGE.EN] ?? '',
              value: service?._id ?? '',
            })),
            value: filteredSaleReport?.serviceIds ?? '',
          },
        ]}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_PRICE')}
              </Label>
              <Badge className="bg-red-50 text-red-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalPrice ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_VALUE_OF_PROMOTION')}
              </Label>
              <Badge className="text-secondary-foreground bg-secondary rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalValueOfPromotion ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_CUSTOMER_PAY')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalCustomerPay ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_COLLECTION_FEE')}
              </Label>
              <Badge className="bg-yellow-50 text-yellow-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalCollectionFee ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_MAIN_ACCOUNT_TASKER')}
              </Label>
              <Badge className="bg-primary-50 text-primary rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalMainAccountTasker ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tReportDetail('TOTAL_PROMOTION_ACCOUNT')}
              </Label>
              <Badge className="text-secondary-foreground bg-secondary rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  reportInfos?.totalPromotionAccount ?? 0,
                )}${settingCountry?.currency?.sign ?? ''}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
