import type { PipelineStage } from 'mongoose';
import { getModels } from 'schemas';

import { getBNPLPaymentMethodCollectionInfoByIsoCode } from './helpers.server';

export interface PaymentToolKitTransactionList {
  filter: {
    search: string;
    type: string;
    paymentMethod: string;
    city: string;
    rangeDate: {
      from: Date;
      to: Date;
    };
  };
  isoCode: IsoCode;
  sort: PipelineStage.Sort['$sort'];
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
}

function getPaymentToolKitTransactionMatcher({
  filter,
}: Pick<PaymentToolKitTransactionList, 'filter'>) {
  const matcher: PipelineStage.Match['$match'] = {};

  if (filter.search) {
    matcher.$or = [
      { 'tasker.name': { $regex: filter.search, $options: 'i' } },
      { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
      { bankTransactionId: { $regex: filter.search, $options: 'i' } },
    ];
  }

  if (filter.rangeDate.from && filter.rangeDate.to) {
    matcher.createdAt = {
      $gte: filter.rangeDate.from,
      $lt: filter.rangeDate.to,
    };
  }

  if (filter.type) {
    matcher.type = { $in: filter.type.split(',') };
  }

  if (filter.paymentMethod) {
    matcher['payment.method'] = { $in: filter.paymentMethod.split(',') };
  }

  if (filter.city) {
    matcher['tasker.city'] = { $in: filter.city.split(',') };
  }

  return matcher;
}

/**
 * @documentation
 * This function will $lookup to users (big collection)
 * Then $facet to 2 sub pipelines
 * This can significantly impact performance.
 */
export async function getPaymentToolKitTransaction({
  sort,
  skip,
  limit,
  filter,
  isoCode,
}: {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  sort: PipelineStage.Sort['$sort'];
  filter: {
    search: string;
    type: string;
    paymentMethod: string;
    city: string;
    rangeDate: { from: Date; to: Date };
  };
  isoCode: IsoCode;
}) {
  const { collectionName: paymentMethodCollectionName, transactionIdField } =
    getBNPLPaymentMethodCollectionInfoByIsoCode({
      isoCode,
    });

  const data = await getModels(isoCode)
    .paymentToolKitTransaction.aggregate<{
      data: Array<{
        tasker: {
          _id: string;
          name: string;
          phone: string;
          city: string;
        };
        type: string;
        amount: number;
        createdAt: Date;
        payment: {
          transactionId: string;
          method: string;
        };
        bankTransactionId: string;
      }>;
      totalAmount: Array<{
        totalAmount: number;
      }>;
    }>([
      {
        $match: {
          'payment.transactionId': { $exists: true },
          'payment.status': 'PAID',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: paymentMethodCollectionName,
          localField: 'payment.transactionId',
          foreignField: '_id',
          as: 'transaction',
        },
      },
      {
        $unwind: {
          path: '$transaction',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tasker: {
            _id: 1,
            name: 1,
            phone: 1,
            city: { $arrayElemAt: ['$tasker.workingPlaces.city', 0] },
          },

          type: 1,
          amount: 1,
          createdAt: 1,
          payment: {
            transactionId: 1,
            method: 1,
          },
          bankTransactionId: `$transaction.${transactionIdField}`,
        },
      },
      { $match: getPaymentToolKitTransactionMatcher({ filter }) },
      {
        $facet: {
          data: [
            { $sort: { ...sort, _id: 1 } },
            { $skip: skip },
            { $limit: limit },
          ],
          totalAmount: [
            {
              $group: {
                _id: null,
                totalAmount: { $sum: '$amount' },
              },
            },
          ],
        },
      },
    ])
    .exec();

  return {
    data: data?.[0]?.data || [],
    totalAmount: data[0].totalAmount[0]?.totalAmount || 0,
  };
}

export async function getTotalPaymentToolKitTransaction({
  filter,
  isoCode,
}: {
  filter: {
    search: string;
    type: string;
    paymentMethod: string;
    city: string;
    rangeDate: { from: Date; to: Date };
  };
  isoCode: string;
}) {
  const data = await getModels(isoCode)
    .paymentToolKitTransaction.aggregate([
      {
        $match: {
          'payment.transactionId': { $exists: true },
          'payment.status': 'PAID',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tasker: {
            _id: 1,
            name: 1,
            phone: 1,
            city: { $arrayElemAt: ['$tasker.workingPlaces.city', 0] },
          },
          type: 1,
          amount: 1,
          createdAt: 1,
          payment: {
            transactionId: 1,
            method: 1,
          },
        },
      },
      { $match: getPaymentToolKitTransactionMatcher({ filter }) },
      { $count: 'total' },
    ])
    .exec();

  return data?.[0]?.total || 0;
}
