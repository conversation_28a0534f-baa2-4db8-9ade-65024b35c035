import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
  IsoCode,
  PAYMENT_METHOD,
  TASK_STATUS,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
  getListSaleReportWithoutPagination,
  getMemberTransactionsReportByBusinessIds,
  getSaleReportDetail,
} from '~/services/sales-report.server';

describe('Sales Report', () => {
  const isoCode = IsoCode.VN;
  const MOCK_TASK_ID_FOR_BUSINESS_MEMBER = 'mock-task-id-for-business-member';
  const BUSINESS_ID = 'business-id';
  const MEMBER_OF_BUSINESS_ID = 'member-of-business-id';
  const USER_OF_MEMBER_BUSINESS_ID = 'user-of-member-business-id';
  const TASKER_WORKING_FOR_TRANSACTION_ID = 'tasker-working-for-transaction-id';
  const MOCK_SERVICE_ID = 'mock-service-id';

  const TASK_FOR_BUSINESS_MEMBER = {
    _id: MOCK_TASK_ID_FOR_BUSINESS_MEMBER,
    status: TASK_STATUS.DONE,
    date: momentTz().toDate(),
    duration: 2,
    payment: {
      method: PAYMENT_METHOD.BPAY_BUSINESS,
    },
    costDetail: {
      finalCost: 100000,
      decreasedReasons: [],
    },
    promotion: {
      code: 'promotion-code',
    },
    newCostDetail: {
      finalCost: 100000,
    },
    serviceText: {
      vi: 'Dọn dẹp nhà',
      en: 'Home Cleaning',
      th: 'Home Cleaning',
    },
    acceptedTasker: [
      {
        taskerId: TASKER_WORKING_FOR_TRANSACTION_ID,
      },
    ],
    serviceId: MOCK_SERVICE_ID,
  };

  const USER_OF_BUSINESS = {
    _id: BUSINESS_ID,
    phone: '**********',
    fAccountId: 'mock-f-account-id',
    language: 'vi',
    status: 'ACTIVE',
    createdAt: momentTz().toDate(),
  };
  const USER_OF_MEMBER_BUSINESS = {
    _id: USER_OF_MEMBER_BUSINESS_ID,
    name: 'user_of_member_name',
    phone: '*********',
    fAccountId: 'mock-f-account-id2',
    language: 'vi',
    status: 'ACTIVE',
    createdAt: momentTz().toDate(),
  };
  const TASKER_WORKING_FOR_TRANSACTION = {
    _id: TASKER_WORKING_FOR_TRANSACTION_ID,
    name: 'tasker-name',
    phone: '**********',
    fAccountId: 'mock-f-account-id3',
    language: 'vi',
    status: 'ACTIVE',
    createdAt: momentTz().toDate(),
  };

  const BUSINESS = {
    _id: BUSINESS_ID,
    name: 'business-member-name',
    status: 'ACTIVE',
    businessSize: 'UNDER_50',
    sector: 'E_COMMERCE',
    email: '<EMAIL>',
    taxCode: 'taxCode',
  };

  const MEMBER_OF_BUSINESS = {
    _id: MEMBER_OF_BUSINESS_ID,
    userId: USER_OF_MEMBER_BUSINESS_ID,
    status: 'ACTIVE',
    businessId: BUSINESS._id,
  };

  const FA_TRANSACTION_OF_MEMBER_TRANSACTION = {
    _id: 'fa-transaction-id',
    source: {
      value: MOCK_TASK_ID_FOR_BUSINESS_MEMBER,
    },
    amount: 20000,
    type: 'D',
    __v: 0,
  };

  const MOCK_BUSINESS_MEMBER_TRANSACTION = {
    _id: 'mock-business-member-transaction-id',
    name: BUSINESS_TRANSACTION_NAME.TASK,
    createdAt: momentTz().toDate(),
    businessId: BUSINESS_ID,
    memberId: MEMBER_OF_BUSINESS_ID,
    userId: MEMBER_OF_BUSINESS_ID,
    reason: 'reason',
    taskId: MOCK_TASK_ID_FOR_BUSINESS_MEMBER,
    amount: 3000,
    type: BUSINESS_TRANSACTION_TYPE.D,
  };

  beforeAll(async () => {
    await Promise.all([
      getModels(isoCode).FATransaction.create(
        FA_TRANSACTION_OF_MEMBER_TRANSACTION,
      ),
      getModels(isoCode).task.create(TASK_FOR_BUSINESS_MEMBER),
      getModels(isoCode).users.insertMany([
        USER_OF_BUSINESS,
        USER_OF_MEMBER_BUSINESS,
        TASKER_WORKING_FOR_TRANSACTION,
      ]),
      getModels(isoCode).businessMember.create(MEMBER_OF_BUSINESS),
      getModels(isoCode).businessMemberTransaction.create(
        MOCK_BUSINESS_MEMBER_TRANSACTION,
      ),
      getModels(isoCode).business.create(BUSINESS),
    ]);
  });
  afterAll(async () => {
    await Promise.all([
      getModels(isoCode).FATransaction.findByIdAndDelete(
        FA_TRANSACTION_OF_MEMBER_TRANSACTION._id,
      ),
      getModels(isoCode).task.findByIdAndDelete(TASK_FOR_BUSINESS_MEMBER._id),
      getModels(isoCode).users.deleteMany({
        _id: {
          $in: [
            USER_OF_BUSINESS._id,
            USER_OF_MEMBER_BUSINESS._id,
            TASKER_WORKING_FOR_TRANSACTION._id,
          ],
        },
      }),
      getModels(isoCode).businessMember.findByIdAndDelete(
        MEMBER_OF_BUSINESS._id,
      ),
      getModels(isoCode).businessMemberTransaction.findByIdAndDelete(
        MOCK_BUSINESS_MEMBER_TRANSACTION._id,
      ),
      getModels(isoCode).business.findByIdAndDelete(BUSINESS._id),
    ]);
  });

  describe('getListSaleReportWithoutPagination', () => {
    it('Should return the list sales report without navigation', async () => {
      const mockParams = {
        filter: {
          rangeDate: {
            from: momentTz().subtract(2, 'days').toDate(),
            to: momentTz().add(2, 'days').toDate(),
          },
        },
        isoCode,
      };

      const result = await getListSaleReportWithoutPagination(mockParams);

      const MOCK_RESULT_TRANSACTIONS = [
        {
          _id: MOCK_BUSINESS_MEMBER_TRANSACTION._id,
          createdAt: MOCK_BUSINESS_MEMBER_TRANSACTION.createdAt,
          businessId: BUSINESS._id,
          task: {
            _id: TASK_FOR_BUSINESS_MEMBER._id,
            costDetail: TASK_FOR_BUSINESS_MEMBER.costDetail,
          },
          businessUser: {
            phone: USER_OF_BUSINESS.phone,
          },
          business: {
            _id: BUSINESS._id,
            name: BUSINESS.name,
          },
          faTransaction: [
            {
              source: FA_TRANSACTION_OF_MEMBER_TRANSACTION.source,
              amount: FA_TRANSACTION_OF_MEMBER_TRANSACTION.amount,
              type: FA_TRANSACTION_OF_MEMBER_TRANSACTION.type,
            },
          ],
        },
      ];

      expect(result).toHaveLength(MOCK_RESULT_TRANSACTIONS.length);
      expect(result).toStrictEqual(MOCK_RESULT_TRANSACTIONS);
    });
  });
  describe('getSaleReportDetail', () => {
    it('Should return the sales report detail', async () => {
      const mockParams = {
        businessId: BUSINESS._id,
        isoCode,
        filter: {
          serviceIds: [MOCK_SERVICE_ID].join(','),
          rangeDate: {
            from: momentTz().subtract(2, 'days').toDate(),
            to: momentTz().add(2, 'days').toDate(),
          },
        },
      };

      const result = await getSaleReportDetail(mockParams);
      const MOCK_RESULT_BUSINESS_MEMBER_TRANSACTIONS = [
        {
          _id: MOCK_BUSINESS_MEMBER_TRANSACTION._id,
          task: {
            _id: TASK_FOR_BUSINESS_MEMBER._id,
            costDetail: TASK_FOR_BUSINESS_MEMBER.costDetail,
            serviceText: TASK_FOR_BUSINESS_MEMBER.serviceText,
          },
          faTransaction: [FA_TRANSACTION_OF_MEMBER_TRANSACTION],
          asker: {
            name: USER_OF_MEMBER_BUSINESS.name,
            phone: USER_OF_MEMBER_BUSINESS.phone,
          },
          tasker: {
            name: TASKER_WORKING_FOR_TRANSACTION.name,
            phone: TASKER_WORKING_FOR_TRANSACTION.phone,
          },
          createdAt: MOCK_BUSINESS_MEMBER_TRANSACTION.createdAt,
        },
      ];

      expect(result).toStrictEqual({
        business: {
          _id: BUSINESS._id,
          name: BUSINESS.name,
        },
        businessFromUser: {
          _id: USER_OF_BUSINESS._id,
          phone: USER_OF_BUSINESS.phone,
        },
        businessMemberTransactions: MOCK_RESULT_BUSINESS_MEMBER_TRANSACTIONS,
      });
    });
  });
  describe('getMemberTransactionsReportByBusinessIds', () => {
    it('Should member transactions report by business id successfully', async () => {
      const mockParams = {
        isoCode,
        businessIds: [BUSINESS_ID],
        rangeDate: {
          from: momentTz().subtract(2, 'days').toDate(),
          to: momentTz().add(2, 'days').toDate(),
        },
      };

      const result = await getMemberTransactionsReportByBusinessIds(mockParams);

      const MOCK_RESULT_TRANSACTIONS = [
        {
          _id: MOCK_BUSINESS_MEMBER_TRANSACTION._id,
          task: {
            _id: TASK_FOR_BUSINESS_MEMBER._id,
            costDetail: TASK_FOR_BUSINESS_MEMBER.costDetail,
            serviceText: TASK_FOR_BUSINESS_MEMBER.serviceText,
            date: TASK_FOR_BUSINESS_MEMBER.date,
            duration: TASK_FOR_BUSINESS_MEMBER.duration,
          },
          asker: {
            name: USER_OF_MEMBER_BUSINESS.name,
            phone: USER_OF_MEMBER_BUSINESS.phone,
          },
          tasker: {
            name: TASKER_WORKING_FOR_TRANSACTION.name,
            phone: TASKER_WORKING_FOR_TRANSACTION.phone,
          },
          business: {
            _id: BUSINESS._id,
            name: BUSINESS.name,
          },
          businessUser: {
            phone: USER_OF_BUSINESS.phone,
          },
          faTransaction: [FA_TRANSACTION_OF_MEMBER_TRANSACTION],
          createdAt: MOCK_BUSINESS_MEMBER_TRANSACTION.createdAt,
        },
      ];

      expect(result).toHaveLength(MOCK_RESULT_TRANSACTIONS.length);
      expect(result).toStrictEqual(MOCK_RESULT_TRANSACTIONS);
    });
  });
});
