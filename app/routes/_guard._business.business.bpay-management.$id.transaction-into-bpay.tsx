import { json } from '@remix-run/node';
import {
  Outlet,
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  Label,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertSortString,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getListDepositBusinessTransactionIntoBPayByBusinessId,
  getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId,
} from '~/services/deposit-business-transaction-into-bpay.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getSession } from '~/services/session.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const [
    { sort: sortString, createdAt: rangeDate, search, paymentMethod },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(url.searchParams, {
    keysString: ['sort', 'createdAt', 'search', 'paymentMethod'],
    keysNumber: ['pageSize', 'pageIndex'],
  });

  const parsedRangeDate = rangeDate ? JSON.parse(rangeDate) : null;

  const filterValue = {
    paymentMethod,
    search,
    rangeDate:
      parsedRangeDate?.from && parsedRangeDate?.to
        ? {
            from: momentTz(parsedRangeDate?.from).startOf('day').toDate(),
            to: momentTz(parsedRangeDate?.to).endOf('day').toDate(),
          }
        : {
            from: momentTz(parsedRangeDate?.from).startOf('month').toDate(),
            to: momentTz(parsedRangeDate?.to).endOf('month').toDate(),
          },
  };

  const total =
    await getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId({
      filter: filterValue,
      isoCode,
      businessId: params.id || '',
    });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const depositBusinessTransactionIntoBPay =
    await getListDepositBusinessTransactionIntoBPayByBusinessId({
      filter: filterValue,
      businessId: params.id || '',
      isoCode,
      skip,
      limit,
      sort: convertSortString({
        sortString,
        defaultValue: { createdAt: -1 },
      }),
    });

  const settingCountry = await getSettingCountryByIsoCode({
    isoCode,
    projection: { currency: 1 },
  });

  const session = await getSession(request.headers.get('cookie'));
  const flashMessage = await session.get('flashMessage');

  return json({
    total,
    data: depositBusinessTransactionIntoBPay.data || [],
    totalAmount: depositBusinessTransactionIntoBPay.totalAmount,
    filterValue,
    currency: settingCountry?.currency?.sign,
    flashMessage,
  });
}, PERMISSIONS.READ_DEPOSIT_BUSINESS_TRANSACTION_INTO_BPAY);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function TransactionIntoBPay() {
  const [openDialogDeposit, setOpenDialogDeposit] = useState(true);
  const permissions = useGlobalStore(state => state.permissions);

  const { t: tTransactionIntoBPay } = useTranslation(
    'deposit-business-transaction-into-bpay',
  );
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const {
    error: loaderError,
    total,
    data,
    totalAmount,
    filterValue,
    currency,
    flashMessage,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  useEffect(() => {
    if (flashMessage) {
      toast({
        variant: 'success',
        description: tTransactionIntoBPay(flashMessage),
      });
    }
  }, [flashMessage, tTransactionIntoBPay]);

  const columns: ColumnDef<
    ReturnValueIgnorePromise<
      typeof getListDepositBusinessTransactionIntoBPayByBusinessId
    >['data'][0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'payment.referenceCode',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('PAYMENT_REFERENCE_CODE')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original?.payment?.referenceCode}
          </Typography>
        ),
        size: 260,
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('CREATED_AT')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800">
              {row?.original?.createdAt
                ? format(row?.original?.createdAt, 'HH:mm - dd/MM/yyyy')
                : null}
            </Typography>
          );
        },
        size: 200,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('AMOUNT')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {`${formatNumberWithCommas(row.original?.amount || 0)}${row.original?.currency?.sign}`}
          </Typography>
        ),
        size: 260,
      },
      {
        accessorKey: 'createdBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('CREATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original?.createdBy}
          </Typography>
        ),
        size: 260,
        enableSorting: false,
      },
      {
        accessorKey: 'paymentMethod',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('PAYMENT_METHOD')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {tTransactionIntoBPay(row.original?.payment?.method)}
          </Typography>
        ),
        size: 260,
        enableSorting: false,
      },
      {
        accessorKey: 'requester',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('REQUESTER')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original?.requester || '-'}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'reason',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBPay('REASON')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original?.reason || '-'}
          </Typography>
        ),
        enableSorting: false,
      },
    ],
    [tTransactionIntoBPay],
  );

  return (
    <>
      <BTaskeeTable
        isShowClearButton
        toolbarAction={
          <div>
            <Button
              className="h-[30px]"
              disabled={
                !permissions?.includes(
                  PERMISSIONS.WRITE_DEPOSIT_BUSINESS_TRANSACTION_INTO_BPAY,
                )
              }
              onClick={() => {
                navigate(`deposit-dialog`, {
                  replace: true,
                  preventScrollReset: true,
                });
                setOpenDialogDeposit(true);
              }}>
              {tTransactionIntoBPay('DEPOSIT')}
            </Button>
          </div>
        }
        columns={columns}
        data={data.map(item => ({
          ...item,
          createdAt: momentTz(item.createdAt).toDate(),
        }))}
        total={total || 0}
        pagination={getPageSizeAndPageIndex({
          total,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="deposit-business-transaction-into-bpay"
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filterValue?.rangeDate?.from).toDate(),
            to: momentTz(filterValue?.rangeDate?.to).toDate(),
          },
        }}
        filters={[
          {
            placeholder: tTransactionIntoBPay('PAYMENT_METHOD'),
            name: 'paymentMethod',
            options: [
              {
                label: tTransactionIntoBPay('BANK_TRANSFER'),
                value: 'BANK_TRANSFER',
              },
            ],
            value: filterValue?.paymentMethod,
          },
        ]}
        search={{
          placeholder: tTransactionIntoBPay('SEARCH_BY_CODE_USER'),
          defaultValue: filterValue.search || '',
          name: 'search',
        }}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tTransactionIntoBPay('TOTAL_VALUE')}
              </Label>
              <Badge className="bg-orange-50 text-orange-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(totalAmount || 0)}${currency}`}
              </Badge>
            </div>
          </div>
        }
      />

      <Outlet
        context={{
          openDialogDeposit,
          onOpenChange: setOpenDialogDeposit,
          onClose: () => {
            return navigate('');
          },
        }}
      />
    </>
  );
}
