import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  BtaskeeResponseError,
  Button,
  ErrorMessageBase,
  Grid,
  Input,
  Label,
  LoadingSpinner,
  Typography,
  toast,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction } from '~/hoc/remix';
import { setupPasswordForExpiredLinkOfNewUser } from '~/services/settings.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export async function loader() {
  return json({});
}

export const action = hocAction(async ({ request }) => {
  const formData = await request.clone().formData();

  await setupPasswordForExpiredLinkOfNewUser({
    email: formData.get('email')?.toString() || '',
  });

  return json({ isSent: true });
});

export default function ResetPasswordForExpiredToken() {
  const { t: tResetPassword } = useTranslation(
    'reset-password-for-expired-token',
  );
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const { state } = useNavigation();
  const submit = useSubmit();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const { handleSubmit, formState, register } = useForm<{ email: string }>();

  const onSubmit = (data: { email: string }) => {
    const formData = new FormData();

    formData.append('email', data.email);

    submit(formData, { method: 'post' });
  };

  return (
    <>
      <div className="flex flex-col space-y-2 text-start">
        <Typography variant="h3">
          {tResetPassword('PASSWORD_SETUP_LINK_EXPIRED')}
        </Typography>
        <Typography variant="p" affects="removePMargin">
          {tResetPassword('PASSWORD_SETUP_LINK_EXPIRED_TEXT_HELPER')}
        </Typography>
      </div>
      <div className="grid gap-6">
        {actionData?.isSent ? (
          tResetPassword('CHECK_YOUR_EMAIL')
        ) : (
          <Grid>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid gap-6">
                <div className="grid gap-2">
                  <Label>{tResetPassword('EMAIL')}</Label>
                  <Input
                    {...register('email' as const, {
                      required: tResetPassword('THIS_FIELD_IS_REQUIRED'),
                    })}
                    placeholder="<EMAIL>"
                  />
                  <ErrorMessageBase name="email" errors={formState.errors} />
                </div>
                <Button>
                  {state !== 'idle' ? (
                    <LoadingSpinner />
                  ) : (
                    tResetPassword('SEND')
                  )}
                </Button>
              </div>
            </form>
          </Grid>
        )}
      </div>
    </>
  );
}
