import {
  BUSINESS_TRANSACTION_NAME,
  PAYMENT_METHOD,
  TASK_STATUS,
} from 'btaskee-constants';
import { getAccountingTransactionClosingMongoFilter } from 'btaskee-utils';
import {
  type EnumIsoCode,
  getCollectionNameByIsoCode,
  getModels,
} from 'schemas';

type GettingSalesReport = {
  isoCode: `${EnumIsoCode}`;
  filter: {
    rangeDate?: {
      from: Date;
      to: Date;
    };
    searchText?: string;
  };
};

type GettingSalesReportDetail = {
  businessId: string;
  isoCode: `${EnumIsoCode}`;
  filter: {
    rangeDate?: {
      from: Date;
      to: Date;
    };
    serviceIds: string;
    searchText?: string;
  };
};

export async function getListSaleReportWithoutPagination({
  filter,
  isoCode,
}: GettingSalesReport) {
  const business = await getModels(isoCode).businessMemberTransaction.aggregate<
    BusinessMemberTransaction & {
      task: Pick<Task, 'newCostDetail' | 'costDetail' | '_id'>;
      faTransaction: Array<FATransaction>;
      business: Pick<AccountBusiness, '_id' | 'name'>;
      businessUser: Pick<UserApp, 'phone'>;
    }
  >([
    {
      $match: {
        name: BUSINESS_TRANSACTION_NAME.TASK,
        ...(filter?.rangeDate
          ? {
              createdAt: getAccountingTransactionClosingMongoFilter({
                filteredFromDate: filter.rangeDate.from,
                filteredToDate: filter.rangeDate.to,
              }),
            }
          : {}),
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).task,
        localField: 'taskId',
        foreignField: '_id',
        as: 'task',
      },
    },
    {
      $unwind: {
        path: '$task',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        'task.status': TASK_STATUS.DONE,
        'task.payment.method': PAYMENT_METHOD.BPAY_BUSINESS,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'businessId',
        foreignField: '_id',
        as: 'businessUser',
      },
    },
    {
      $unwind: {
        path: '$businessUser',
        preserveNullAndEmptyArrays: true,
      },
    },
    ...(filter?.searchText
      ? [{ $match: { 'businessUser.phone': filter.searchText } }]
      : []),
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).business,
        localField: 'businessId',
        foreignField: '_id',
        as: 'business',
      },
    },
    {
      $unwind: {
        path: '$business',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).FATransaction,
        localField: 'task._id',
        foreignField: 'source.value',
        as: 'faTransaction',
      },
    },
    {
      $project: {
        createdAt: 1,
        businessId: 1,
        'task._id': 1,
        'task.newCostDetail': 1,
        'task.costDetail': 1,
        'business.name': 1,
        'business._id': 1,
        'businessUser.phone': 1,
        'faTransaction.source': 1,
        'faTransaction.amount': 1,
        'faTransaction.type': 1,
      },
    },
  ]);

  return business ?? [];
}

export async function getSaleReportDetail({
  businessId,
  isoCode,
  filter,
}: GettingSalesReportDetail) {
  const [
    businessFound,
    businessFromUsersCollection,
    businessMemberTransactions,
  ] = await Promise.all([
    getModels(isoCode).business.findById(businessId).select({ name: 1 }).lean(),
    getModels(isoCode).users.findById(businessId).select({ phone: 1 }).lean(),
    getModels(isoCode).businessMemberTransaction.aggregate<
      Pick<BusinessMemberTransaction, '_id' | 'createdAt'> & {
        task: Pick<
          Task,
          '_id' | 'costDetail' | 'newCostDetail' | 'promotion' | 'serviceText'
        >;
        faTransaction: Array<FATransaction>;
        asker: Pick<UserApp, 'name' | 'phone'>;
        tasker: Pick<UserApp, 'name' | 'phone'>;
        taskDoneAt: Date;
      }
    >([
      {
        $match: {
          name: BUSINESS_TRANSACTION_NAME.TASK,
          businessId,
          ...(filter?.rangeDate
            ? {
                createdAt: getAccountingTransactionClosingMongoFilter({
                  filteredFromDate: filter.rangeDate.from,
                  filteredToDate: filter.rangeDate.to,
                }),
              }
            : {}),
        },
      },
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).task,
          localField: 'taskId',
          foreignField: '_id',
          as: 'task',
        },
      },
      {
        $unwind: {
          path: '$task',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'task.status': TASK_STATUS.DONE,
          'task.payment.method': PAYMENT_METHOD.BPAY_BUSINESS,
          ...(filter?.serviceIds
            ? { 'task.serviceId': { $in: filter.serviceIds.split(',') } }
            : {}),
        },
      },
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).businessMember,
          localField: 'memberId',
          foreignField: '_id',
          as: 'businessMember',
        },
      },
      {
        $unwind: {
          path: '$businessMember',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'businessMember.userId',
          foreignField: '_id',
          as: 'asker',
        },
      },
      {
        $unwind: {
          path: '$asker',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'task.acceptedTasker.0.taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(filter?.searchText
        ? [
            {
              $match: {
                $or: [
                  { 'asker.phone': filter.searchText },
                  { 'tasker.phone': filter.searchText },
                ],
              },
            },
          ]
        : []),
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).FATransaction,
          localField: 'task._id',
          foreignField: 'source.value',
          as: 'faTransaction',
        },
      },
      {
        $project: {
          faTransaction: 1,
          createdAt: 1,
          'asker.name': 1,
          'asker.phone': 1,
          'tasker.name': 1,
          'tasker.phone': 1,
          'task._id': 1,
          'task.promotion': 1,
          'task.costDetail': 1,
          'task.serviceText': 1,
          'task.newCostDetail': 1,
        },
      },
    ]),
  ]);

  return {
    business: businessFound,
    businessFromUser: businessFromUsersCollection,
    businessMemberTransactions,
  };
}

export async function getMemberTransactionsReportByBusinessIds({
  isoCode,
  businessIds,
  rangeDate,
}: {
  isoCode: IsoCode;
  businessIds: Array<AccountBusiness['_id']>;
  rangeDate?: {
    from: Date;
    to: Date;
  };
}) {
  const transactions = await getModels(
    isoCode,
  ).businessMemberTransaction.aggregate<
    Pick<BusinessMemberTransaction, '_id' | 'createdAt'> & {
      task: Pick<
        Task,
        | '_id'
        | 'costDetail'
        | 'newCostDetail'
        | 'promotion'
        | 'serviceText'
        | 'date'
        | 'duration'
      >;
      faTransaction: Array<FATransaction>;
      business: Pick<AccountBusiness, '_id' | 'name'>;
      businessUser: Pick<UserApp, 'phone'>;
      asker: Pick<UserApp, 'name' | 'phone'>;
      tasker: Pick<UserApp, 'name' | 'phone'>;
    }
  >([
    {
      $match: {
        name: BUSINESS_TRANSACTION_NAME.TASK,
        businessId: { $in: businessIds ?? [] },
        ...(rangeDate
          ? {
              createdAt: getAccountingTransactionClosingMongoFilter({
                filteredFromDate: rangeDate.from,
                filteredToDate: rangeDate.to,
              }),
            }
          : {}),
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).task,
        localField: 'taskId',
        foreignField: '_id',
        as: 'task',
      },
    },
    {
      $unwind: {
        path: '$task',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        'task.status': TASK_STATUS.DONE,
        'task.payment.method': PAYMENT_METHOD.BPAY_BUSINESS,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).businessMember,
        localField: 'memberId',
        foreignField: '_id',
        as: 'businessMember',
      },
    },
    {
      $unwind: {
        path: '$businessMember',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'businessMember.userId',
        foreignField: '_id',
        as: 'asker',
      },
    },
    {
      $unwind: {
        path: '$asker',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'task.acceptedTasker.0.taskerId',
        foreignField: '_id',
        as: 'tasker',
      },
    },
    {
      $unwind: {
        path: '$tasker',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).business,
        localField: 'businessId',
        foreignField: '_id',
        as: 'business',
      },
    },
    {
      $unwind: {
        path: '$business',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'business._id',
        foreignField: '_id',
        as: 'businessUser',
      },
    },
    {
      $unwind: {
        path: '$businessUser',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).FATransaction,
        localField: 'task._id',
        foreignField: 'source.value',
        as: 'faTransaction',
      },
    },
    {
      $project: {
        createdAt: 1,
        faTransaction: 1,
        'asker.name': 1,
        'asker.phone': 1,
        'tasker.name': 1,
        'tasker.phone': 1,
        'business._id': 1,
        'business.name': 1,
        'task._id': 1,
        'task.date': 1,
        'task.duration': 1,
        'task.promotion': 1,
        'task.costDetail': 1,
        'task.serviceText': 1,
        'task.newCostDetail': 1,
        'businessUser.phone': 1, // Phone register of business
      },
    },
  ]);

  return transactions ?? [];
}
