import { Link, Outlet, useLocation } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BreadcrumbsLink,
  Grid,
  Tabs,
  TabsList,
  TabsTrigger,
  Typography,
  cn,
} from 'btaskee-ui';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        disabled
        to=""
        onClick={e => e.preventDefault()}
        label="BNPL_NAV"
      />
    );
  },
};

export default function PromotionScreen() {
  const { t } = useTranslation('bnpl');
  const { pathname } = useLocation();
  const globalData = useGlobalStore(state => state);

  const [selectedTab, setSelectedTab] = useState(pathname);
  const handleTabChange = (newTab: string) => {
    setSelectedTab(newTab);
  };

  const navigation = useMemo(
    () => [
      {
        title: 'PAY_LATER',
        href: ROUTE_NAME.PAY_LATER,
        permission: PERMISSIONS.READ_BNPL_PAY_LATER,
      },
      {
        title: 'PAY_NOW',
        href: ROUTE_NAME.PAY_NOW,
        permission: PERMISSIONS.READ_BNPL_PAY_NOW,
      },
    ],
    [],
  );

  const currentNav = useMemo(() => {
    return navigation.find(
      nav =>
        globalData.permissions.includes(nav.permission) &&
        nav.href === pathname,
    );
  }, [globalData.permissions, navigation, pathname]);

  return (
    <>
      <Grid
        className={cn(
          'bg-secondary p-4 rounded-xl mb-6',
          pathname !== currentNav?.href ? 'hidden' : null,
        )}>
        <div className="flex items-center justify-between">
          <Grid className="gap-3">
            <Typography variant="h2">{t('BUY_NOW_PAY_LATER')}</Typography>
            <Tabs
              defaultValue={selectedTab}
              onValueChange={handleTabChange}
              className="space-y-4">
              <TabsList>
                {navigation.map(item => (
                  <Link to={item.href} key={item.href}>
                    {globalData.permissions?.includes(item.permission) ? (
                      <TabsTrigger value={item.href}>
                        {t(item.title)}
                      </TabsTrigger>
                    ) : null}
                  </Link>
                ))}
              </TabsList>
            </Tabs>
          </Grid>
        </div>
      </Grid>
      <Outlet />
    </>
  );
}
