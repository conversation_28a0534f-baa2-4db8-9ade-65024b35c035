import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getListBusinessMemberExportingExcelFile,
  getListBusinessMemberTransaction,
  getTotalBPayTransaction,
} from '~/services/business-bpay-transaction.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.BUSINESS_BPAY_TRANSACTION}
        label="BPAY_TRANSACTION"
      />
    );
  },
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);

  const [{ sort, rangeDate, search }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['sort', 'rangeDate', 'search'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filter = {
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
    search,
  };

  const filterValue = {
    search,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };

  const { isoCode } = await getUserSession({ headers: request.headers });

  const total = await getTotalBPayTransaction({
    filter: filterValue,
    isoCode,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [bPayTransactionData, countrySetting, exportExcelRecords] =
    await Promise.all([
      getListBusinessMemberTransaction({
        skip,
        limit,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
        filter: filterValue,
        isoCode,
        projection: {
          _id: 1,
          createdAt: 1,
          amount: 1,
          reason: 1,
          phone: 1,
          name: 1,
          currency: 1,
        },
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
      getListBusinessMemberExportingExcelFile({
        filter: filterValue,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
        isoCode,
        projection: {
          createdAt: 1,
          amount: 1,
          name: 1,
          reason: 1,
          phone: 1,
          currency: 1,
          taskId: 1,
        },
      }),
    ]);

  return json({
    bPayTransactionList: bPayTransactionData?.data || [],
    exportExcelRecords,
    total,
    filter,
    totalAmount: bPayTransactionData?.totalAmount[0]?.count || 0,
    currency: countrySetting?.currency?.sign || '',
  });
}, PERMISSIONS.READ_BUSINESS_BPAY_TRANSACTION);

export default function BPayTransactionListScreen() {
  const { t: tBusinessBPayTransaction } = useTranslation(
    'business-bpay-transaction',
  );

  const [searchParams] = useSearchParams();

  const {
    currency,
    filter,
    total,
    bPayTransactionList,
    totalAmount,
    exportExcelRecords,
  } = useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);

  const fromDate = momentTz(filter?.rangeDate?.from);
  const toDate = momentTz(filter?.rangeDate?.to);

  const { exportCsv: exportBPayTransactionList } = useExportFile();
  const confirmExport = useConfirm();

  const onExportExcel = async () => {
    const exportedExcelData = exportExcelRecords?.map(item => ({
      [tBusinessBPayTransaction('ASKER_NAME')]: item.askerName,
      [tBusinessBPayTransaction('BUSINESS_NAME')]: item.businessName,
      [tBusinessBPayTransaction('PHONE')]: item.phone,
      [tBusinessBPayTransaction('CREATED_AT')]: momentTz(item.createdAt).format(
        'HH:mm - DD/MM/YYYY',
      ),
      [tBusinessBPayTransaction('AMOUNT')]: item.amount || 0,
      [tBusinessBPayTransaction('REASON')]: item.reason,
      [tBusinessBPayTransaction('_ID')]: item._id,
      [tBusinessBPayTransaction('TASK_ID')]: item.taskId,
    }));

    const fileName = `List of business bpay transaction from ${fromDate.format('DD/MM/YYYY')} to ${toDate.format('DD/MM/YYYY')}`;

    const isConfirm = await confirmExport({
      title: tBusinessBPayTransaction('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {tBusinessBPayTransaction('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tBusinessBPayTransaction('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {total}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tBusinessBPayTransaction('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {fromDate.format('DD/MM/YYYY')} - {toDate.format('DD/MM/YYYY')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tBusinessBPayTransaction('CANCEL'),
      actionButton: tBusinessBPayTransaction('CONFIRM'),
    });

    if (isConfirm) {
      exportBPayTransactionList({
        fileName,
        arrayJson: exportedExcelData,
      });
    }
  };

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<
        typeof getListBusinessMemberTransaction
      >['data'][0]
    >
  >[] = [
    {
      accessorKey: 'askerName',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('ASKER_NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.askerName || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'businessName',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('BUSINESS_NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.businessName || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'phone',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('PHONE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.phone || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('CREATED_AT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.createdAt
            ? momentTz(row.original?.createdAt).format('HH:mm - DD/MM/YYYY')
            : ''}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${formatNumberWithCommas(row.original?.amount ?? 0)}${currency}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'reason',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tBusinessBPayTransaction('REASON')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {tBusinessBPayTransaction(row.original?.name ?? '')}
        </Typography>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tBusinessBPayTransaction('BPAY_TRANSACTION')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={() => onExportExcel()}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_BUSINESS_BPAY_TRANSACTION)
          }>
          <Upload className="w-5 h-5" />
          {tBusinessBPayTransaction('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={total || 0}
        data={bPayTransactionList || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="business-bpay-transaction"
        search={{
          defaultValue: filter?.search || '',
          name: 'search',
          placeholder: tBusinessBPayTransaction('SEARCH'),
          className: 'w-[310px] lg:w-[310px]',
        }}
        filterDate={{
          name: 'rangeDate',
          mode: 'range-date',
          defaultValue: {
            from: fromDate.startOf('day').toDate(),
            to: toDate.endOf('day').toDate(),
          },
        }}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {tBusinessBPayTransaction('TOTAL_AMOUNT')}
              </Label>
              <Badge className="bg-primary-50 text-primary rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                {`${formatNumberWithCommas(totalAmount || 0)}${currency}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
