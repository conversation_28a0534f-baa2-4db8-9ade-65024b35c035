import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  getListMemberBusinessBPay,
  getTotalMemberBusinessBPay,
} from '~/services/business-bpay-management.server';

describe('business bpay management', () => {
  const isoCode = 'VN';

  const mockBusinessMemberTransactions = [
    {
      _id: 'transaction-1',
      createdAt: momentTz().toDate(),
      amount: 100000,
      name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BUSINESS,
      type: BUSINESS_TRANSACTION_TYPE.D,
      reason: 'Top up BPAY member',
      memberId: 'member-1',
      userId: 'business-1',
      businessId: 'business-1',
      taskId: 'task-1',
    },
    {
      _id: 'transaction-2',
      createdAt: momentTz().subtract(1, 'day').toDate(),
      amount: 100000,
      name: BUSINESS_TRANSACTION_NAME.REFUND_EXPIRED_TASK,
      type: BUSINESS_TRANSACTION_TYPE.C,
      reason: 'Top up BPAY member',
      memberId: 'member-2',
      userId: 'member-2',
      businessId: 'business-1',
      taskId: 'task-2',
    },
    {
      _id: 'transaction-3',
      createdAt: momentTz().subtract(2, 'day').toDate(),
      amount: 100000,
      name: BUSINESS_TRANSACTION_NAME.REVOKE_BPAY_BY_BUSINESS,
      type: BUSINESS_TRANSACTION_TYPE.C,
      reason: 'Revoke BPAY member',
      userId: 'member-3',
      memberId: 'member-3',
      businessId: 'business-1',
      taskId: 'task-3',
    },
  ];

  const mockBusinessMembers = [
    {
      _id: 'member-1',
      userId: 'user-1',
      name: 'Member 1',
      levelId: 'level-1',
      businessId: 'business-1',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      phone: '090xxxxx1',
      bPay: 100,
    },
    {
      _id: 'member-2',
      userId: 'user-2',
      name: 'Member 2',
      levelId: 'level-1',
      businessId: 'business-1',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      phone: '090xxxxx2',
      bPay: 100,
    },
    {
      _id: 'member-3',
      userId: 'user-3',
      levelId: 'level-1',
      name: 'Member 3',
      businessId: 'business-1',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      phone: '090xxxxx3',
      bPay: 100,
    },
  ];

  const mockUsers = [
    {
      _id: 'user-1',
      name: 'User 1',
      phone: '090xxxxx1',
      fAccountId: 'fAccountId-1',
      language: 'vn',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      address: 'Ho Chi Minh',
    },
    {
      _id: 'user-2',
      name: 'User 2',
      phone: '090xxxxx2',
      fAccountId: 'fAccountId-2',
      language: 'vn',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      address: 'Ho Chi Minh',
    },
    {
      _id: 'user-3',
      name: 'User 3',
      phone: '090xxxxx3',
      fAccountId: 'fAccountId-3',
      language: 'vn',
      status: 'ACTIVE',
      createdAt: momentTz().toDate(),
      address: 'Ho Chi Minh',
    },
  ];

  beforeEach(async () => {
    await getModels(isoCode).businessMemberTransaction.insertMany(
      mockBusinessMemberTransactions,
    );
    await getModels(isoCode).businessMember.insertMany(mockBusinessMembers);
    await getModels(isoCode).users.insertMany(mockUsers);
  });

  afterEach(async () => {
    await getModels(isoCode).businessMemberTransaction.deleteMany({});
    await getModels(isoCode).businessMember.deleteMany({});
    await getModels(isoCode).users.deleteMany({});
  });

  describe('getListMemberBusinessBPay', () => {
    it('should return a list of business member transactions', async () => {
      const result = await getListMemberBusinessBPay({
        isoCode,
        businessId: 'business-1',
        skip: 0,
        limit: 10,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          level: 'level-1',
          search: '090xxxxx', // search by phone
          rangeDate: {
            from: momentTz().subtract(2, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
      });

      expect(result.totalAmount?.[0]?.totalDepositedAmount).toEqual(100000);
      expect(result.totalAmount?.[0]?.totalUsageAmount).toEqual(100000);
      expect(result.totalAmount?.[0]?.totalResetedAmount).toEqual(100000);

      expect(result.data).toHaveLength(mockBusinessMemberTransactions.length);
      expect(result.data?.[0]?.name).toEqual(mockUsers[0].name);
      expect(result.data?.[0]?.phone).toEqual(mockUsers[0].phone);
      expect(result.data?.[1]?.name).toEqual(mockUsers[1].name);
      expect(result.data?.[1]?.phone).toEqual(mockUsers[1].phone);
      expect(result.data?.[2]?.name).toEqual(mockUsers[2].name);
      expect(result.data?.[2]?.phone).toEqual(mockUsers[2].phone);
    });
  });

  describe('getTotalMemberBusinessBPay', () => {
    it('should return the total amount of business member transactions', async () => {
      const result = await getTotalMemberBusinessBPay({
        isoCode,
        businessId: 'business-1',
        filter: {
          level: 'level-1',
          search: '090xxxxx', // search by phone
          rangeDate: {
            from: momentTz().subtract(2, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
      });

      expect(result).toEqual(mockBusinessMemberTransactions.length);
    });
  });
});
