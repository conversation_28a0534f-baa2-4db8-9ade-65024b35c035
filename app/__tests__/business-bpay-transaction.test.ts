import { BUSINESS_TRANSACTION_NAME } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
  getListBusinessMemberTransaction,
  getTotalBPayTransaction,
} from '~/services/business-bpay-transaction.server';

describe('Business Member Transaction Service', () => {
  const mockTransactionId1 = 'transaction-id-1';
  const mockTransactionId2 = 'transaction-id-2';
  const mockId_1 = 'partner-account-business-id-1';

  const mockUsers = [
    {
      _id: mockId_1,
      phone: '**********',
      name: '<PERSON>',
      fAccountId: 'fAccountId-1',
      language: 'vn',
      status: 'ACTIVE',
      createdAt: new Date('2016-04-20T07:25:26.359+00:00'),
      address: 'Ho Chi Minh',
    },
  ];

  const mockBusinesses = [
    {
      _id: 'business-id-1',
      name: '<PERSON><PERSON>or<PERSON>',
      status: 'ACTIVE',
      businessSize: 'UNDER_250',
      sector: 'INFORMATION_TECHNOLOGY',
      email: '<EMAIL>',
      taxCode: '*********',
      userId: mockId_1,
    },
  ];

  const mockBusinessMember = [
    {
      _id: 'member-id-1',
      businessId: 'business-id-1',
      userId: mockId_1,
      levelId: 'lvl789',
      phone: '+***********',
      bPay: 1200.5,
      status: 'ACTIVE',
      createdAt: '2024-09-15T10:23:00.000Z',
      changeHistories: [
        {
          changedBy: 'user789',
          changeDate: '2024-10-01T08:12:45.000Z',
          changes: 'Updated salary from 1000 to 1200.50',
        },
        {
          changedBy: 'user456',
          changeDate: '2024-10-15T15:45:30.000Z',
          changes: 'Status changed from INACTIVE to ACTIVE',
        },
      ],
    },
  ];

  const mockTransactions = [
    {
      _id: mockTransactionId1,
      amount: 500,
      userId: 'member-id-1',
      memberId: 'member-id-1',
      businessId: 'business-id-1',
      createdAt: momentTz('2024-01-15T10:20:00.000Z').toDate(),
      taskId: 'task-id-1',
      reason: 'Task payment',
      name: BUSINESS_TRANSACTION_NAME.TASK,
      type: 'C',
    },
    {
      _id: mockTransactionId2,
      amount: 300,
      memberId: 'member-id-1',
      userId: 'member-id-1',
      businessId: 'business-id-1',
      createdAt: momentTz('2024-01-20T11:20:00.000Z').toDate(),
      taskId: 'task-id-2',
      reason: 'Task payment',
      name: BUSINESS_TRANSACTION_NAME.TASK,
      type: 'C',
    },
  ];

  beforeEach(async () => {
    await getModels('VN').businessMemberTransaction.insertMany(
      mockTransactions,
    );
    await getModels('VN').businessMember.insertMany(mockBusinessMember);
    await getModels('VN').users.insertMany(mockUsers);
    await getModels('VN').business.insertMany(mockBusinesses);
  });

  afterEach(async () => {
    await getModels('VN').businessMemberTransaction.deleteMany({});
    await getModels('VN').businessMember.deleteMany({});
    await getModels('VN').users.deleteMany({});
    await getModels('VN').business.deleteMany({});
  });

  describe('getListBusinessMemberTransaction', () => {
    const filter = {
      search: 'John',
      rangeDate: {
        from: momentTz('2024-01-01T00:00:00.000Z').toDate(),
        to: momentTz('2025-01-01T23:59:59.999Z').toDate(),
      },
    };

    it('should return a list of transactions with pagination and projections', async () => {
      const skip = 0;
      const limit = 10;

      const result = await getListBusinessMemberTransaction({
        skip,
        limit,
        projection: {
          _id: 1,
          createdAt: 1,
          amount: 1,
          reason: 1,
          phone: 1,
          currency: 1,
        },
        isoCode: 'VN',
        filter,
        sort: { createdAt: -1 },
      });

      expect(result.data).toHaveLength(2);
      expect(result.totalAmount[0].count).toEqual(800); // 500 + 300
      expect(result.data[0]).toHaveProperty('askerName', 'John Doe');
      expect(result.data[0]).toHaveProperty('businessName', 'TechCorp');
    });

    it('should return an empty result when no transactions match the filter', async () => {
      const result = await getListBusinessMemberTransaction({
        skip: 0,
        limit: 10,
        projection: { amount: 1 },
        isoCode: 'VN',
        filter: {
          ...filter,
          search: 'Nonexistent',
        },
        sort: { createdAt: -1 },
      });

      expect(result.data).toHaveLength(0);
      expect(result.totalAmount).toHaveLength(0);
    });
  });

  describe('getTotalBPayTransaction', () => {
    it('should return the correct total count for matching transactions', async () => {
      const result = await getTotalBPayTransaction({
        filter: {
          search: 'John',
          rangeDate: {
            from: new Date('2024-01-01T00:00:00.000Z'),
            to: new Date('2025-01-01T23:59:59.999Z'),
          },
        },
        isoCode: 'VN',
      });

      expect(result).toEqual(2);
    });

    it('should return zero when no transactions match the filter', async () => {
      const result = await getTotalBPayTransaction({
        filter: {
          search: 'Nonexistent',
          rangeDate: {
            from: new Date('2024-01-01T00:00:00.000Z'),
            to: new Date('2025-01-01T23:59:59.999Z'),
          },
        },
        isoCode: 'VN',
      });

      expect(result).toEqual(0);
    });
  });
});
