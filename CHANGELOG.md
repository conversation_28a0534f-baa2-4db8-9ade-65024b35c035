## [1.6.14](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.13...v1.6.14) (2025-03-21)


### Bug Fixes

* add the initial value for state handling the confirm dialog ([7b87187](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7b871871701adc58790020b1ac04c69c1943f809))
* add transaction date column into sales report ([18aabf9](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/18aabf9655d7f6c38f2f187976468d0c9a1ff1ac))
* apply the accounting transaction closing time into the filter date ([7e12d3f](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7e12d3f556caa312719670949b4c74f503abfeb4))
* transaction date and done task date on sales report ([6125dac](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/6125dacdbe7a6893c2a9f342802afd90176c3c12))
* update latest version for all of mini repos ([385dff9](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/385dff9b78dbb28e87dc66b62a4d6729f2c7a2aa))
* update the mini repos version related to btaskee income ([0cadc5c](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/0cadc5c88e096d621a4418f92703c4d4ba29d3b9))


### Features

* additional field export sale report ([fea7a3f](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/fea7a3f32a4120734cc8fadd02727cd127b413c9))
* daily income and monthly income in individual tab ([8ca02c0](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/8ca02c0709aa471aee40acd97fe1111689abde32))
* replace trans date to the task completed date on sales report ([b75d06c](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/b75d06c5daca7103c9465658ec120e397b7d39a3))
* update btaskee income excel file, format date for sales report ([1cf77f0](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/1cf77f07d65dd951acca3a30d33841e39426ccd7))

## [1.6.13](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.12...v1.6.13) (2025-03-11)


### Bug Fixes

* unhide the close icon of exporting file modal of sale report ([434ce62](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/434ce62d834c48778c9f85f31cf48c8f4c581281))
* uninstall the unnessary file-saver package ([063f547](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/063f5479bb2f8ab50b1c79c5322f62fb7770e956))


### Features

* add the business, member of business option for exporting sales report ([57d40dd](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/57d40dd6d6ffd2448888620e3e58c5e511a97f4e))
* improve adding user to group in rbac ([0404f90](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/0404f905817c66a74b74baa5c645d8ba4fd5a204))
* update exporting multi excel file for a export action on the sales report ([cc2074b](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/cc2074b373dab7b34f5b53102baeb23699ebc058))

## [1.6.12](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.11...v1.6.12) (2025-02-28)


### Features

* format number when export file excel ([5f1ad6d](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/5f1ad6dae9cf4b5a3899e37bf42bdec4f057c252))

## [1.6.11](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.10...v1.6.11) (2025-02-20)


### Bug Fixes

* total collection fee in sales report page ([6304022](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/6304022b53eabb5a79f621a546c9bc67776de882))

## [1.6.10](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.9...v1.6.10) (2025-02-14)


### Bug Fixes

* export sale report detail with task id col, fix task price in sale report ([ddf949a](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/ddf949adb92b0a177175b5b8d129c2f13560d546))
* filter payment method in sale report query ([230d509](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/230d5098af314647aabf0c73f852be43c5a64a84))
* gitlab ci corepack ([0290c78](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/0290c789fc5f2636de55b4aacc4bad25b765288d))
* gitlab ci corepack ([6720ce5](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/6720ce5f926db3c68fb3c9388cf57f6d3699f86b))
* issue from newest version of corepack was updated ([ea6b748](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/ea6b7482c616a9927e9aec762d59c15666489e06))
* search by asker phone on sale report page ([1cb9b9c](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/1cb9b9c67d352691881b243a8e5ce650c6184327))
* update paid amount in installment management page ([2dd3d6e](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/2dd3d6ece4f45d1e7d049638c7bc16f803d99e9b))
* update paid amount in installment management page ([3508137](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/3508137d20ea1f82be8620f2e955afd51e810f43))


### Features

* export the list sale report for business ([0c779e1](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/0c779e100c31703ccb50dcfcee159c71960d53be))
* improve sale report page and export excel file, add sale report detail ([81991ba](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/81991ba56e54d6d2ccfef917482a91811a2cbf30))
* query  getting sale report for business ([7717ffa](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7717ffaad244568d4aca08783cb195f78700ced4))
* show reason and requester at transaction topup business ([dccef63](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/dccef63f20f4d5ce3e05d4e7265171dfb01dd158))
* ui for sales report page ([f0ea1ab](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/f0ea1ab06073e99db64593c2266022c9bf8e0c64))

## [1.6.9](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.8...v1.6.9) (2025-01-21)


### Bug Fixes

* add permission at children group not need manager permission ([b342ac4](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/b342ac430cc75436ef7f5c007edda917138273f0))


### Features

* add task id col when export business bpay transaction ([93b9247](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/93b9247bbcdf80ab83792d5738fa5d447b58d6d1))
* display user name on setup password email ([33ce99d](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/33ce99d16f00dccf6d1ae7e708f0365b3035861b))
* reset password for expired link of new user ([dfab38d](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/dfab38d61ddf114462be2aa427c89afde6932070))

## [1.6.8](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.7...v1.6.8) (2025-01-07)


### Bug Fixes

* move hooks using ui component to btaskee ui repos ([2abf8eb](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/2abf8eb1a152e89e6cf58312a3356b5979a78d4d))
* not display user information of bemployee ([a209292](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/a209292b8f39c224aca4804ec657e1c74c94dbdf))

## [1.6.7](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.6...v1.6.7) (2024-12-31)


### Bug Fixes

* update bpay transaction name constants ([1d4afa6](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/1d4afa6b52c1ab22a86a246c2f6a66fe45ba0845))


### Features

* **bpay:** add user search in deposit transaction dialog ([c6ca5fd](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/c6ca5fd495a50e5935a31b7b192d3be3f9f54d6d))
* clone semgrep from marketing repos ([7fb6f4f](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7fb6f4f8a1dc8658bc1054098cbf84d0fa2624f4))

## [1.6.6](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.5...v1.6.6) (2024-12-16)


### Reverts

* Merge branch feat[#49](https://gitlab.com/btaskee/btaskee-ops/accounting/issues/49)-user-profile into staging ([c284d2b](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/c284d2b9133ab73ebcff6060621239e3de5d1ccb))

# [1.6.1](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.6.0...v1.7.0) (2024-10-10)

### Bug Fixes

- update luci react version ([e29225b](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/e29225b9420b5dceff943a42c0fcc216abdedaa5))

### Features

- apply env sandbox ([fae15ff](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/fae15ff3d563863997a4bea6ca2a1f666bc4838b))
- change bcrypt into bcryptjs ([4ecc3e9](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/4ecc3e9f2e4b87316347ab63b8f1df36f841ecb5))

# [1.6.0](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.5.0...v1.6.0) (2024-09-30)

### Bug Fixes

- change filter from updated at to created at payment management feature ([561c8cd](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/561c8cd99121af60c319859058893bb5950804eb))
- change pathname to use package ([df9dd05](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/df9dd05d6fc6c8b43d71a578f4c2e503ae34823d))
- unit test with right structure schema user ([5664bca](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/5664bca5933f1dae4fbec222f01bcd044585711a))

### Features

- export data subscription to excel ([af45d95](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/af45d95acd0652ed949a9a2dba20c980382bf461))
- implement asker balance page ([3af2d4c](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/3af2d4c73fd0f27873e471b27f6240bac89ba003))
- implement subscription page ([28e63b3](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/28e63b314b1fd980400c500ac4b7252b513fef77))
- implement tasker balance page ([9a900c2](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/9a900c222be1008c324a2701815711669527353a))
- implement ui for asker balance page ([5554d47](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/5554d4706ad9abc3f117f5427583dc81f20d5a2d))
- implement ui for asker balance page ([76a7c7a](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/76a7c7a671b76d1ba6ecc6646082fd7956256e18))
- implement ui for tasker balance page ([2b2d2c3](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/2b2d2c3a3a2b273cd0d9e53efd6124b39d92ba8b))

# [1.5.0](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.4.0...v1.5.0) (2024-09-13)

### Features

- update docker with pnpm for faster ([7caf562](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7caf562d8638ae0ddd8c6dd5536c3209720085ed))
- update docker with pnpm for faster ([57b411f](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/57b411fb7182d68ca9b39b717f6aa987cb7b7219))

# [1.3.0](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.2.3...v1.3.0) (2024-09-13)

### Bug Fixes

- ci test ([83a169c](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/83a169c665d2023b17204c9693fc3a3d6bd82c33))
- error currency sign display ([2180fdc](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/2180fdc1ae43ffe6dacc10208d560a2e55955f59))
- pnpm and lint rules work error with peers deps ([7d46d6a](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/7d46d6ac9461ceced0739c4fb252611b19cf01bb))
- reset amount value ([a7e0868](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/a7e0868231da698fdb7bccc44ac99be8180f255d))

### Features

- accounting installment magement ([983733f](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/983733f5cfd9051269be3105a0680e5ed431dfd1))
- implement bBay management ([76ddbc9](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/76ddbc94cf6fbffdf768df037ee61ef99eab7d69))
- implement bPay management page ([ea6a699](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/ea6a6997b291827fbe025d755b14484fbd047682))
- migrate from npm into pnpm ([effbbdc](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/effbbdc9e586bb4c67737470570fac2e178037f5))
- payment management toolkit ([03f5064](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/03f50641339fe5a33ce1038bbf4c7b2ce3be999d))

## [1.2.3](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.2.2...v1.2.3) (2024-08-16)

### Bug Fixes

- create new page for add new user, fix ui ([bbdbfb5](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/bbdbfb502bdf42b424ebabbe3bb889af436f5427))

## [1.2.2](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.2.1...v1.2.2) (2024-08-16)

### Bug Fixes

- update ui for rbac ([6e052a7](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/6e052a7c740d23f2a721f5785912fd6ba6095b82))

## [1.2.1](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.2.0...v1.2.1) (2024-08-15)

### Bug Fixes

- change login background and title ([cfea1cf](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/cfea1cfef06b2f2fb4efb2590f617dec8aa3e029))
- improve ui for rbac, tree view for group management ([0b85ad8](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/0b85ad8071282da0c1294376abaff9121cce9e7f))

# [1.2.0](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.1.0...v1.2.0) (2024-07-23)

### Features

- apply new collection name workingplace ([236bbb5](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/236bbb5e294304bf24ce5d7828a685195a11f2bc))

# [1.1.0](https://gitlab.com/btaskee/btaskee-ops/accounting/compare/v1.0.0...v1.1.0) (2024-07-22)

### Bug Fixes

- add stage for auto release notes ([d9b5236](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/d9b5236ab3ac89b865b9e29d377b01a4b2d9733a))
- multi language pattern ([5a8c6fa](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/5a8c6fa03dcd7133a25874a73f4eea4608540183))
- update package lock ([2143f06](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/2143f061420bc00063528b89290d0f178d7032f3))

### Features

- auth flow responsive ([de1a405](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/de1a4057f5857f0fd47c2a07c9988a329f2dc0a8))
- remove importing type from bundle ([032f21d](https://gitlab.com/btaskee/btaskee-ops/accounting/commit/032f21d14d328b8a5f75eacad79106c7b8addac7))

#### 2024-03-12

##### Chores

- remove head (651a7a75)
- update ui for roles (54d39ac1)
- update ui for roles (fc673caa)
- roles base ui updated (30204c35)
- get more detail roles (9e26eed0)
- complete feature update group (b1056cc9)
- update create role feature (0443c28a)
- remove button add group (ed7d0baa)
- add link to reset password (15f14cd8)

##### New Features

- update query and roles ui (3f7d2539)
- create genealogy for group (b116b52a)
- query get role detail (074931d7)
- update group feature (6f691782)
- update ui groups page (df7de1b7)
- insert role (f1a50167)
- update ui groups page (d9693ec9)
- multi select async, complete insert children groups (d5942f38)
- config prettier cspell commitlint with husky (8e1ff4ad)
- roles management page (15593cf0)
- add hocLoader (66958c41)
- create group, flow for root permission (21497c02)
- update ui roles page (36ed300d)
- save action permission with hoc action remix (15c187b5)
- create middleware for action history modified (2b588bc3)
- improve ui for roles and permission (dcc5eaef)
- apply controller hook form into open ref (e74a2608)
- create new user (e5617590)
- remix table configuration (dc94d613)
- ui for permission (17ef5cfd)
- use react hook form with submit remix (0e6cb16f)
- loading with useNavigation (e19502ea)
- add zod to verify env variables (8d6aed6f)
- create layout for auth and non auth (afc4d8ca)
- reset password and logout, add navbar (cc1342ff)
- complete full flow authentication with pass and verification code (a32afde0)
- complete session flow (42262818)
- setup i18n and basic auth flow (1c34f6a5)
- connect mongo, setup shacdn, taiwindcss, config express (004f165e)

##### Bug Fixes

- push null value (89554503)
- fix conflict (6cc38af1)

##### Other Changes

- leminh.nguyen/remixrun-boilerplate (b042d3f0)
- leminh.nguyen/remixrun-boilerplate (70592e92)
- leminh.nguyen/remixrun-boilerplate (57419a4b)
- leminh.nguyen/remixrun-boilerplate (2176c9f2)
- leminh.nguyen/remixrun-boilerplate (2d828afa)
- leminh.nguyen/remixrun-boilerplate (6ab9385b)
- from remix tutorial (c972ae4d)
