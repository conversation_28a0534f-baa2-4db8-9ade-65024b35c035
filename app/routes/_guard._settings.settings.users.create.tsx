import { zodResolver } from '@hookform/resolvers/zod';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  useActionData,
  useLoaderData,
  useNavigate,
  useSubmit,
} from '@remix-run/react';
import { ACTION_NAME, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  MultiSelectAdvance,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getCities, getUserSession } from '~/services/helpers.server';
import { getAllChildrenGroupOfUser } from '~/services/role-base-access-control.server';
import { createNewUserWithGroups } from '~/services/settings.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.USER_SETTING}/create`}
      label="CREATE_USER"
    />
  ),
  i18n: 'user-settings',
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { userId: managerId, isoCode } = await getUserSession({
    headers: request.headers,
  });

  const [groups, cities] = await Promise.all([
    getAllChildrenGroupOfUser(managerId),
    getCities(isoCode),
  ]);

  return json({ cities, groups });
};

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();

    const username = formData.get('username')?.toString() || '';
    const email = formData.get('email')?.toString() || '';
    const cities = JSON.parse(formData.get('cities')?.toString() || '') || [];
    const groupIds =
      JSON.parse(formData.get('groupIds')?.toString() || '') || [];
    const name = formData.get('name')?.toString() || '';

    const { isoCode } = await getUserSession({ headers: request.headers });

    const newUser = await createNewUserWithGroups({
      username,
      name,
      email,
      isoCode,
      cities,
      groupIds,
    });

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_USER,
      dataRelated: { userId: newUser?._id },
    });

    const t = await i18next.getFixedT(request, 'user-settings');

    return json({ message: t('CREATE_USER_SUCCESSFUL') });
  },
  PERMISSIONS.MANAGER,
);

export default function UserCreationScreen() {
  const { t } = useTranslation('user-settings');

  const submit = useSubmit();
  const navigate = useNavigate();
  const loaderData = useLoaderData<typeof loader>();
  const confirm = useConfirm();

  const FormDataSchema = z.object({
    email: z.string().email(t('INVALID_EMAIL_ADDRESS')),
    cities: z.array(z.any()).min(1, t('MUST_HAVE_AT_LEAST_ONE_CITY')),
    username: z.string().min(1, t('USERNAME_IS_REQUIRED')),
    groupIds: z.array(z.any()).min(1, t('MUST_HAVE_AT_LEAST_ONE_GROUP')),
    name: z.string().min(1, t('MEMBER_NAME_IS_REQUIRED')),
  });

  const form = useForm<z.infer<typeof FormDataSchema>>({
    resolver: zodResolver(FormDataSchema),
    defaultValues: {
      email: '',
      cities: [],
      username: '',
      groupIds: [],
      name: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof FormDataSchema>) => {
    const formData = new FormData();

    formData.append('email', data.email);
    formData.append('cities', JSON.stringify(data.cities));
    formData.append('username', data.username);
    formData.append('groupIds', JSON.stringify(data.groupIds));
    formData.append('name', data.name);

    const isConfirm = await confirm({
      title: t('CREATE'),
      body: t('ARE_YOU_SURE_CREATE_NEW_RECORD'),
      actionButton: t('CONFIRM'),
      cancelButton: t('CANCEL'),
    });
    if (isConfirm) {
      submit(formData, { method: 'post' });
    }
  };

  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
    if (actionData?.message) {
      toast({ description: actionData.message, variant: 'success' });
      form.reset();
    }
  }, [actionData, form]);

  return (
    <>
      <div className="grid space-y-2 rounded-2xl bg-secondary p-4">
        <Typography variant="h3">{t('CREATE_USER')}</Typography>
        <Breadcrumbs />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('USERNAME')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('USERNAME')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('MEMBER_NAME')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('ENTER_MEMBER_NAME')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('EMAIL')}</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder={t('EMAIL')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="cities"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel>{t('CITIES')}</FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      options={loaderData.cities?.map(city => ({
                        label: city,
                        value: city,
                      }))}
                      onValueChange={onChange}
                      defaultValue={value}
                      placeholder={t('CHOOSE_CITY')}
                      variant="blue"
                      maxCount={7}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="groupIds"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel>{t('GROUPS')}</FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      options={loaderData.groups?.map(group => ({
                        label: group.name,
                        value: group._id,
                      }))}
                      onValueChange={onChange}
                      defaultValue={value}
                      placeholder={t('CHOOSE_GROUP')}
                      variant="blue"
                      maxCount={6}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Separator className="my-6" />
          <div className="mt-6 flex justify-end gap-4">
            <Button
              variant="ghost"
              className="border border-primary text-primary"
              onClick={() => navigate(-1)}
              type="button">
              {t('CANCEL')}
            </Button>
            <Button type="submit">{t('SUBMIT')}</Button>
          </div>
        </form>
      </Form>
    </>
  );
}
