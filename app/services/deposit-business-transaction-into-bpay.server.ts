import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import { newRecordC<PERSON><PERSON>Field } from '~/services/helpers.server';

interface GetListDepositBusinessTransactionIntoBPayParams {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  sort: PipelineStage.Sort['$sort'];
  businessId: BusinessTransaction['businessId'];
  isoCode: string;
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
    paymentMethod: string;
  };
}

function getMatcher({
  filter,
  businessId,
}: Pick<
  GetListDepositBusinessTransactionIntoBPayParams,
  'filter' | 'businessId'
>) {
  const matcher: PipelineStage.Match['$match'] = {
    type: BUSINESS_TRANSACTION_TYPE.D,
    name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
    businessId,
  };

  if (filter.search) {
    matcher.$or = [
      { createdBy: { $regex: filter.search, $options: 'i' } },
      { 'payment.referenceCode': { $regex: filter.search, $options: 'i' } },
    ];
  }

  if (filter.rangeDate?.from && filter.rangeDate?.to) {
    matcher.createdAt = {
      $gte: filter.rangeDate.from,
      $lte: filter.rangeDate.to,
    };
  }

  if (filter.paymentMethod) {
    matcher['payment.method'] = { $in: filter.paymentMethod.split(',') };
  }

  return matcher;
}

export async function getListDepositBusinessTransactionIntoBPayByBusinessId({
  skip,
  limit,
  sort,
  isoCode,
  filter,
  businessId,
}: GetListDepositBusinessTransactionIntoBPayParams) {
  const result = await getModels(isoCode)
    .businessTransaction.find(getMatcher({ filter, businessId }))
    .sort({ ...sort, _id: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  // Calculate total amount using aggregation
  const [totalAmount] = await getModels(isoCode).businessTransaction.aggregate([
    { $match: getMatcher({ filter, businessId }) },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' },
      },
    },
  ]);

  return {
    data: result || [],
    totalAmount: totalAmount?.total || 0,
  };
}

export async function getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId({
  isoCode,
  filter,
  businessId,
}: Pick<
  GetListDepositBusinessTransactionIntoBPayParams,
  'isoCode' | 'filter' | 'businessId'
>) {
  return getModels(isoCode)
    .businessTransaction.countDocuments(getMatcher({ filter, businessId }))
    .lean();
}

export async function getBusinessCityAndBPayInfo({
  isoCode,
  businessId,
}: {
  isoCode: IsoCode;
  businessId: AccountBusiness['_id'];
}) {
  const asker = await getModels(isoCode).users.findById(businessId).lean();

  const business = await getModels(isoCode)
    .business.findById(businessId)
    .lean();

  return {
    bPay: business?.bPay,
    city: asker?.cities?.[0]?.city || '',
    businessName: business?.name,
  };
}

/**
 * Generates a unique business transaction code for BPAY deposits
 * @param {Object} params - The parameters object
 * @param {IsoCode} params.isoCode - The ISO code for the country/region
 * @returns {string} A formatted transaction code (e.g., "BIS-AUS-X7Kp9q")
 */
export async function getInvoiceCodeDepositBusinessTransactionIntoBPay({
  isoCode,
}: {
  isoCode: IsoCode;
}) {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const randomChars = Array.from(
    { length: 6 },
    () => characters[Math.floor(Math.random() * characters.length)],
  ).join('');

  return `BIS-${isoCode}-${randomChars}`;
}

export async function depositBusinessTransactionIntoBPay({
  isoCode,
  data,
}: {
  isoCode: IsoCode;
  data: FormDepositBusinessTransactionIntoBPay & {
    businessId: AccountBusiness['_id'];
    payment: { invoice: string };
    currency?: {
      sign: string;
      code: string;
    };
    createdBy: string;
  };
}) {
  // Check for duplicate payment reference
  const existingTransaction = await getModels(isoCode)
    .businessTransaction.findOne({
      type: BUSINESS_TRANSACTION_TYPE.D,
      name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
      'payment.referenceCode': data.payment.referenceCode,
    })
    .lean();

  if (existingTransaction) {
    throw new Error('DUPLICATE_PAYMENT_REFERENCE_CODE');
  }

  await getModels(isoCode)
    .business.findByIdAndUpdate(data.businessId, {
      $inc: { bPay: data.amount },
    })
    .lean();

  return getModels(isoCode).businessTransaction.create({
    _id: newRecordCommonField()._id,
    createdAt: newRecordCommonField().createdAt,
    type: BUSINESS_TRANSACTION_TYPE.D,
    name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
    ...data,
  });
}
