// Superset authentication utilities
import { SUPERSET_CONFIG, getLoginUrl, getCsrfTokenUrl } from '~/config/superset';

export interface LoginResponse {
  access_token: string;
  refresh_token?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

/**
 * Authenticate with Superset using username/password
 * This is a helper function for future implementation
 */
export async function authenticateWithSuperset(
  credentials: LoginCredentials = SUPERSET_CONFIG.ADMIN_USER
): Promise<LoginResponse | null> {
  try {
    // First, get CSRF token
    const csrfResponse = await fetch(getCsrfTokenUrl(), {
      method: 'GET',
      credentials: 'include',
    });

    if (!csrfResponse.ok) {
      throw new Error('Failed to get CSRF token');
    }

    const csrfData = await csrfResponse.json();
    const csrfToken = csrfData.result;

    // Then, login with credentials
    const loginResponse = await fetch(getLoginUrl(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken,
      },
      credentials: 'include',
      body: JSON.stringify({
        username: credentials.username,
        password: credentials.password,
        provider: 'db',
        refresh: true,
      }),
    });

    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }

    const loginData = await loginResponse.json();
    return loginData;
  } catch (error) {
    console.error('Authentication failed:', error);
    return null;
  }
}

/**
 * Check if user is authenticated with Superset
 */
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const response = await fetch(`${SUPERSET_CONFIG.HOST}/api/v1/me/`, {
      credentials: 'include',
    });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Logout from Superset
 */
export async function logoutFromSuperset(): Promise<boolean> {
  try {
    const response = await fetch(`${SUPERSET_CONFIG.HOST}/logout/`, {
      method: 'POST',
      credentials: 'include',
    });
    return response.ok;
  } catch {
    return false;
  }
}
