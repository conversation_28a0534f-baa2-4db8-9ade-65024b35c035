import { BUSINESS_TRANSACTION_NAME } from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface BusinessMemberTransactionProps {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  projection: PipelineStage.Project['$project'];
  sort: PipelineStage.Sort['$sort'];
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
  };
  isoCode: string;
}

function businessNameMatcher({
  filter,
}: Pick<
  BusinessMemberTransactionProps,
  'filter'
>): PipelineStage.Match['$match'] {
  const matcher: PipelineStage.Match['$match'] = {};
  matcher.type = { $in: ['C'] };

  // Date range filter
  if (filter.rangeDate?.from && filter.rangeDate?.to) {
    matcher.createdAt = {
      $gte: filter.rangeDate.from,
      $lte: filter.rangeDate.to,
    };
  }

  // Search filter
  if (filter.search) {
    matcher.$or = [
      { _id: { $regex: filter.search, $options: 'i' } },
      { 'askerData.name': { $regex: filter.search, $options: 'i' } },
    ];
  }

  return matcher;
}

function getFilterPipeline({
  filter,
  isoCode,
}: Pick<BusinessMemberTransactionProps, 'filter' | 'isoCode'> & {
  projection?: PipelineStage.Project['$project'];
}): PipelineStage[] {
  const matcher = businessNameMatcher({ filter });

  const pipeline: PipelineStage[] = [
    {
      $match: {
        name: {
          $in: [
            BUSINESS_TRANSACTION_NAME.CANCEL_TASK,
            BUSINESS_TRANSACTION_NAME.PAY_SUBSCRIPTION,
            BUSINESS_TRANSACTION_NAME.TASK,
            BUSINESS_TRANSACTION_NAME.COMBO_VOUCHER,
            BUSINESS_TRANSACTION_NAME.UPDATE_TASK,
          ],
        },
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).businessMember,
        localField: 'memberId', // Field in BusinessMemberTransaction
        foreignField: '_id', // Field in businessMember
        as: 'businessMemberData',
      },
    },
    {
      $unwind: {
        path: '$businessMemberData',
        preserveNullAndEmptyArrays: true,
      },
    },

    {
      $lookup: {
        from: 'users',
        localField: 'businessMemberData.userId', // Field in businessMember referencing User
        foreignField: '_id',
        as: 'askerData',
      },
    },
    {
      $unwind: {
        path: '$askerData',
        preserveNullAndEmptyArrays: true,
      },
    },

    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).business,
        localField: 'businessId',
        foreignField: '_id',
        as: 'businessData',
      },
    },
    {
      $unwind: {
        path: '$businessData',
        preserveNullAndEmptyArrays: true,
      },
    },

    {
      $match: matcher,
    },
  ];

  return pipeline;
}

export async function getTotalBPayTransaction({
  filter,
  isoCode,
}: Pick<
  BusinessMemberTransactionProps,
  'filter' | 'isoCode'
>): Promise<number> {
  const pipeline = getFilterPipeline({ filter, isoCode });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    { $count: 'total' },
  ]);

  return result[0]?.total || 0;
}

export async function getListBusinessMemberTransaction({
  skip,
  limit,
  projection,
  isoCode,
  filter,
  sort,
}: BusinessMemberTransactionProps): Promise<{
  data: Array<
    BusinessMemberTransaction &
      Pick<UserApp, 'phone'> & {
        askerName: UserApp['name'];
        businessName: AccountBusiness['name'];
        transactionId: BusinessMemberTransaction['_id'];
      }
  >;
  totalAmount: { count: BusinessMemberTransaction['amount'] }[];
}> {
  const pipeline = getFilterPipeline({ filter, isoCode });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    {
      $facet: {
        totalAmount: [
          {
            $group: {
              _id: null,
              count: { $sum: '$amount' },
            },
          },
        ],
        data: [
          { $sort: { ...sort, _id: -1 } },
          { $skip: skip },
          { $limit: limit },
          {
            $project: {
              ...projection,
              phone: '$askerData.phone',
              askerName: '$askerData.name',
              businessName: '$businessData.name',
            },
          },
        ],
      },
    },
  ]);

  return result[0] || {};
}

export async function getListBusinessMemberExportingExcelFile({
  isoCode,
  filter,
  sort,
  projection,
}: Pick<
  BusinessMemberTransactionProps,
  'filter' | 'isoCode' | 'sort' | 'projection'
>) {
  const pipelines = getFilterPipeline({
    filter,
    isoCode,
  });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipelines,
    { $sort: { ...sort, _id: -1 } },
    {
      $project: {
        ...projection,
        phone: '$askerData.phone',
        askerName: '$askerData.name',
        businessName: '$businessData.name',
      },
    },
  ]);

  return result ?? [];
}
