// Superset types for better type safety

export interface SupersetDashboardConfig {
  id: string;
  supersetDomain: string;
  mountPoint: HTMLElement;
  fetchGuestToken: () => Promise<string>;
  dashboardUiConfig?: DashboardUiConfig;
}

export interface DashboardUiConfig {
  hideTitle?: boolean;
  hideTab?: boolean;
  hideChartControls?: boolean;
  filters?: {
    expanded?: boolean;
    visible?: boolean;
  };
}

export interface GuestTokenRequest {
  user: {
    username: string;
    first_name: string;
    last_name: string;
  };
  resources: Array<{
    type: 'dashboard' | 'chart';
    id: string;
  }>;
  rls: Array<any>; // Row Level Security rules
}

export interface GuestTokenResponse {
  token: string;
}

export interface SupersetError {
  message: string;
  error_type?: string;
  level?: 'error' | 'warning' | 'info';
}
