import {
  FA_TRANSACTION_KEY_VALUE,
  IsoCode,
  TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  getEmployeeDepartments,
  getEmployeeLevels,
  getListBEmployee,
  getTotalBEmployee,
} from '~/services/bEmployee.server';

describe('Employee bPay Management', () => {
  const isoCode = IsoCode.VN;
  const userIds = ['userId1', 'userId2', 'userId3'];
  const commonEmployeeBPayTransactionInfo = {
    type: TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
    accountType: 'C',
    source: {
      value: FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_TOPUP,
      name: 'source name',
    },
  };
  const commonTransactionInfo = {
    type: TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
    accountType: 'C',
    source: {
      name: 'source name',
      value: 'source value',
    },
  };

  const transactions = [
    {
      _id: 'transactionId1',
      userId: userIds[0],
      ...commonEmployeeBPayTransactionInfo,
      date: momentTz().toDate(),
      amount: 12000,
    },
    {
      _id: 'transactionId2',
      userId: userIds[0],
      ...commonEmployeeBPayTransactionInfo,
      date: momentTz().subtract(1, 'month').toDate(),
      amount: 12000,
    },
    {
      _id: 'transactionId3',
      userId: userIds[1],
      ...commonEmployeeBPayTransactionInfo,
      date: momentTz().toDate(),
      amount: 2000,
    },
    {
      _id: 'transactionId4',
      userId: userIds[1],
      ...commonEmployeeBPayTransactionInfo,
      date: momentTz().subtract(1, 'month').toDate(),
      amount: 5000,
    },
    {
      _id: 'transactionId5',
      userId: userIds[2],
      ...commonEmployeeBPayTransactionInfo,
      date: momentTz().toDate(),
      amount: 7000,
    },
    {
      _id: 'transactionId6',
      userId: userIds[0],
      ...commonTransactionInfo,
      date: momentTz().add(2, 'day').toDate(),
      amount: 3000,
    },
    {
      _id: 'transactionId7',
      userId: userIds[1],
      date: momentTz().add(2, 'day').toDate(),
      ...commonTransactionInfo,
      amount: 50000,
    },
    {
      _id: 'transactionId8',
      userId: userIds[2],
      ...commonTransactionInfo,
      date: momentTz().add(2, 'day').toDate(),
      amount: 7000,
    },
    {
      _id: 'transactionId9',
      userId: userIds[0],
      ...commonTransactionInfo,
      date: momentTz().subtract(1, 'month').add(2, 'day').toDate(),
      amount: 7000,
    },
  ];
  const employees = userIds.map(userId => ({
    _id: `employee_${userId}`,
    name: userId,
    level: 'A1',
    team: 'Dev',
    phone: userId,
  }));
  const users = userIds.map(userId => ({
    _id: userId,
    phone: userId,
    name: `user_name_${userId}`,
    workingPlaces: [
      {
        country: 'VN',
        city: 'Hồ Chí Minh',
      },
    ],
    fAccountId: 'fAccountId_1',
    language: 'en',
    status: 'ACTIVE',
    createdAt: new Date(),
  }));
  const employeeSetting = {
    _id: 'employeeSettingId',
    level: [
      { name: 'A1', quota: 10000 },
      { name: 'A2', quota: 20000 },
      { name: 'A3', quota: 30000 },
    ],
  };

  beforeAll(async () => {
    await getModels(isoCode).FATransaction.insertMany(transactions);
    await getModels(isoCode).users.insertMany(users);
    await getModels(isoCode).bEmployee.insertMany(employees);
    await getModels(isoCode).bEmployeeSetting.create(employeeSetting);
  });
  afterAll(async () => {
    await getModels(isoCode).FATransaction.deleteMany({
      _id: { $in: transactions.map(transaction => transaction._id) },
    });
    await getModels(isoCode).users.deleteMany({ _id: { $in: userIds } });
    await getModels(isoCode).bEmployee.deleteMany({
      _id: { $in: employees.map(employee => employee._id) },
    });
    await getModels(isoCode).bEmployeeSetting.deleteOne({
      _id: employeeSetting._id,
    });
  });

  describe('getListBEmployee', () => {
    it('Should get list bEmployee successfully', async () => {
      const params = {
        isoCode,
        rangeMonth: {
          from: momentTz(new Date()).subtract(1, 'month').toDate(),
          to: momentTz(new Date()).add(2, 'day').toDate(),
        },
        sort: {
          topUpAmount: -1,
        } as PipelineStage.Sort['$sort'],
        skip: 0,
        limit: 10,
      };
      const bPayEmployees = await getListBEmployee(params);

      const MOCK_TOTAL_TRANSACTIONS = 5;

      expect(bPayEmployees.transactions).toHaveLength(MOCK_TOTAL_TRANSACTIONS);
    });
  });
  describe('getTotalBEmployee', () => {
    it('Should get total bEmployee bpay transactions successfully', async () => {
      const params = {
        isoCode,
        rangeMonth: {
          from: momentTz(new Date()).subtract(2, 'months').toDate(),
          to: momentTz(new Date()).add(2, 'months').toDate(),
        },
        sort: {
          topUpAmount: -1,
        } as PipelineStage.Sort['$sort'],
        skip: 0,
        limit: 10,
      };
      const totalTransactions = await getTotalBEmployee(params);

      const MOCK_TOTAL_TRANSACTIONS = 5;

      expect(totalTransactions).toEqual(MOCK_TOTAL_TRANSACTIONS);
    });
  });
  describe('getEmployeeDepartments', () => {
    it('Should get department in employees by distinct mongoose method successfully', async () => {
      const departments = await getEmployeeDepartments({ isoCode });

      const expectationDepartmentsFromDataMock: string[] = [];

      employees.forEach(mockEmployee => {
        if (!expectationDepartmentsFromDataMock.includes(mockEmployee.team)) {
          expectationDepartmentsFromDataMock.push(mockEmployee.team);
        }
      });

      expect(departments).toHaveLength(
        expectationDepartmentsFromDataMock.length,
      );
      departments.forEach(department => {
        expect(expectationDepartmentsFromDataMock).toContainEqual(department);
      });
    });
  });
  describe('getEmployeeLevels', () => {
    it('Should get levels from setting successfully', async () => {
      const levels = await getEmployeeLevels({ isoCode });

      expect(levels).toHaveLength(employeeSetting.level.length);
    });
  });
});
