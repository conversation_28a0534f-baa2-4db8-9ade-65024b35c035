import { IsoCode as EnumIsoCode } from 'btaskee-constants';

import id from './id.server';
import my from './my.server';
import th from './th.server';
import vn from './vn.server';

export enum API_KEY {
  WEBSOCKET_SEND_MESSAGE_NOTIFICATION = 'WEBSOCKET_SEND_MESSAGE_NOTIFICATION',
  PUSH_NOTIFICATION = 'PUSH_NOTIFICATION',
}

export type ApiUrl = {
  [k in keyof typeof API_KEY]: string;
};

const storageApi: {
  [key in EnumIsoCode]: ApiUrl;
} = {
  [EnumIsoCode.VN]: vn,
  [EnumIsoCode.MY]: my,
  [EnumIsoCode.TH]: th,
  [EnumIsoCode.ID]: id,
};

function getRestApiByMultiRegion({
  apiKey,
  isoCode,
}: {
  apiKey: keyof typeof API_KEY;
  isoCode: string;
}) {
  if (isoCode === 'VN') {
    return storageApi.VN[apiKey];
  }
  if (isoCode === 'TH') {
    return storageApi.TH[apiKey];
  }
  if (isoCode === 'ID') {
    return storageApi.ID[apiKey];
  }
  if (isoCode === 'MY') {
    if (!storageApi.MY?.[apiKey]) {
      throw new Error('Feature not support in Malaysia');
    }

    return storageApi.MY[apiKey];
  }

  throw new Error('Iso code not is VN or TH or ID or MY');
}

export default getRestApiByMultiRegion;
