rules:
  - id: changes-with-when-never
    pattern: |
      - changes:
        - ...
        when: never
    message: "This Gitlab CI YAML will never run on default branches due to a
      `changes` rule with `when:never`. To fix this, make sure the triggering
      event is a push event. You can do this with `if: '$CI_PIPELINE_SOURCE ==
      \"push\"'`. See
      https://docs.gitlab.com/ee/ci/yaml/index.html#ruleschanges"
    metadata:
      category: correctness
      technology:
        - gitlab
        - gitlab-ci
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    languages:
      - yaml
    severity: WARNING
