rules:

  - id: useless-cat
    pattern-either:
      - pattern: |
          cat | ...
      - patterns:
          - pattern: |
              cat $ARG | ...
          - pattern-not: |
              cat ${$SEVERAL_FILES} | ...
      - pattern: |
          ... | cat
      - pattern: |
          ... | cat | ...
    message: Useless call to 'cat' in a pipeline. Use '<' and '>' for any command to
      read from a file or write to a file.
    metadata:
      references:
        - https://github.com/koalaman/shellcheck/wiki/SC2002
      category: best-practice
      technology:
        - bash
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    severity: WARNING
    languages:
      - bash
