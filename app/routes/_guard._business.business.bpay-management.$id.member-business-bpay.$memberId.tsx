import { Outlet } from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';

export const loader = hocLoader(async ({ params }) => {
  return params.id || '';
});

export const handle = {
  breadcrumb: (id: string) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${id}/member-business-bpay`}
        label="MEMBER_BUSINESS_BPAY"
      />
    );
  },
};

export default function MemberBusinessBPayDetailScreen() {
  return <Outlet />;
}
