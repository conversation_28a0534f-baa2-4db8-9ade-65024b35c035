import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Separator,
  Typography,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getFormattedPhoneNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getListMemberBPayTransactionsHistory,
  getTotalMemberBPayTransactionsHistory,
  getUserInfo,
} from '~/services/business-bpay-management.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const handle = {
  breadcrumb: (data: {
    businessId: BusinessTransaction['_id'];
    memberId: BusinessTransaction['_id'];
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${data.businessId}/member-business-bpay/${data.memberId}`}
        label="BUSINESS_BPAY_HISTORY"
      />
    );
  },
};

export const loader = hocLoader(async ({ request, params }) => {
  const url = new URL(request.url);

  const [{ sort, rangeDate, search, name, type }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['sort', 'rangeDate', 'search', 'name', 'type'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filterValue = {
    search,
    name,
    type,
    rangeDate: rangeDate
      ? DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate)
      : {
          from: momentTz().startOf('month').toDate(),
          to: momentTz().endOf('month').toDate(),
        },
  };

  const { isoCode } = await getUserSession({ headers: request.headers });
  const { id, memberId } = params;

  const total = await getTotalMemberBPayTransactionsHistory({
    businessId: id || '',
    memberId: memberId || '',
    filter: filterValue,
    isoCode,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [memberInfo, listBusinessBPayHistory, settingCountry] =
    await Promise.all([
      getUserInfo({ isoCode, memberId: memberId || '' }),
      getListMemberBPayTransactionsHistory({
        businessId: id || '',
        memberId: memberId || '',
        filter: filterValue,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
        ...getSkipAndLimit({ pageSize, pageIndex }),
        isoCode,
        limit,
        skip,
        projection: {
          createdAt: 1,
        },
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

  return json({
    total,
    memberInfo,
    filter: filterValue,
    listBusinessBPayHistory,
    businessId: params.id || '',
    memberId: params.memberId || '',
    currency: settingCountry?.currency?.sign || '',
  });
});

export default function MemberBusinessBPayHistoryScreen() {
  const { t: tMemberBusinessBPayHistory } = useTranslation(
    'member-business-bpay-history',
  );
  const [searchParams] = useSearchParams();

  const { memberInfo, currency, listBusinessBPayHistory, total, filter } =
    useLoaderDataSafely<typeof loader>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListMemberBPayTransactionsHistory>[0]
    >
  >[] = [
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPayHistory('CREATED_AT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
        </Typography>
      ),
    },
    {
      accessorKey: 'type',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPayHistory('TYPE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.type || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPayHistory('AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${formatNumberWithCommas(row.original?.amount)}${currency || ''}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'reason',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPayHistory('REASON')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.name
            .split('_')
            .join(' ')
            .toLowerCase()
            .replace(/^\w/, c => c.toUpperCase()) || ''}
        </Typography>
      ),
      enableSorting: false,
    },
  ];

  const generalInfo = useMemo(() => {
    return [
      {
        label: tMemberBusinessBPayHistory('NAME'),
        value: memberInfo?.name || '',
      },
      {
        label: tMemberBusinessBPayHistory('PHONE'),
        value: `${getFormattedPhoneNumber(memberInfo?.phone || '')}`,
      },
      {
        label: tMemberBusinessBPayHistory('LEVEL'),
        value: memberInfo?.level || '',
      },
      {
        label: tMemberBusinessBPayHistory('BALANCE_OF_BPAY'),
        value: `${formatNumberWithCommas(memberInfo?.bPay || 0)} ${currency || ''}`,
      },
    ];
  }, [memberInfo, currency, tMemberBusinessBPayHistory]);

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-md bg-secondary p-4">
        <div className="grid space-y-2">
          <Typography variant="h2">
            {tMemberBusinessBPayHistory('BUSINESS_BPAY_HISTORY')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <div className="rounded-[15px] border border-gray-200 bg-gray-50 p-6">
        <div className="w-fit">
          <Typography className="mb-3 text-xl font-semibold -tracking-[0.5%]">
            {tMemberBusinessBPayHistory('GENERAL_INFORMATION')}
          </Typography>
          <Separator className="mb-6" />
        </div>
        <div className="grid grid-cols-2 gap-6 md:grid-cols-3">
          {generalInfo.map((info, idx) => (
            <div key={idx}>
              <Typography className="mb-1 text-sm font-normal leading-[18px] text-gray-400 ">
                {info.label}
              </Typography>
              <Typography className="break-words text-lg font-semibold text-gray-600">
                {info.value}
              </Typography>
            </div>
          ))}
        </div>
      </div>
      <Separator className="my-6" />
      <BTaskeeTable
        isShowClearButton
        total={total || 0}
        data={listBusinessBPayHistory || []}
        columns={columns}
        localeAddress="business-bpay-account-management"
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: filter?.search || '',
          name: 'search',
          placeholder: tMemberBusinessBPayHistory('SEARCH'),
        }}
        filterDate={{
          name: 'rangeDate',
          mode: 'range-date',
          defaultValue: {
            from: momentTz(filter.rangeDate.from).toDate(),
            to: momentTz(filter.rangeDate.to).toDate(),
          },
        }}
        filters={[
          {
            name: 'name',
            options: Object.values(BUSINESS_TRANSACTION_NAME).map(name => ({
              label: name
                .split('_')
                .join(' ')
                .toLowerCase()
                .replace(/^\w/, c => c.toUpperCase()),
              value: name,
            })),
            placeholder: tMemberBusinessBPayHistory('REASON'),
            value: filter?.name || '',
          },
          {
            name: 'type',
            options: Object.keys(BUSINESS_TRANSACTION_TYPE).map(type => ({
              label: tMemberBusinessBPayHistory(type),
              value: type,
            })),
            placeholder: tMemberBusinessBPayHistory('TYPE'),
            value: filter?.type || '',
          },
        ]}
      />
    </>
  );
}
