// Superset configuration
export const SUPERSET_CONFIG = {
  // Superset server configuration
  HOST: 'http://*************:8088',
  
  // Default dashboard ID (must match the ID in the guest token)
  DEFAULT_DASHBOARD_ID: '12345678-90ab-cdef-1234-567890abcdef',

  // Native filters key for the dashboard
  NATIVE_FILTERS_KEY: 'z2cx2L2-iJGWeaBB0-60YXRvZgji-TZzSSF05BglzEIsOL4PKpBAWKPRMVvqhnhQ',

  // Pre-generated guest token for embedded dashboard
  GUEST_TOKEN: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyIjp7InVzZXJuYW1lIjoiR3Vlc3QgVXNlciIsImZpcnN0X25hbWUiOiJHdWVzdCIsImxhc3RfbmFtZSI6IlVzZXIifSwicmVzb3VyY2VzIjpbeyJ0eXBlIjoiZGFzaGJvYXJkIiwiaWQiOiIxMjM0NTY3OC05MGFiLWNkZWYtMTIzNC01Njc4OTBhYmNkZWYifV0sInJscyI6W10sImlhdCI6MTcxODgxMDM4MCwiZXhwIjoxNzE4ODk2NzgwfQ.TLno-Q0W6TVtTL6P9xX57JvS3N1E6Yz8z04m4OibY6E',

  // API endpoints
  ENDPOINTS: {
    LOGIN: '/api/v1/security/login',
    GUEST_TOKEN: '/api/v1/security/guest_token/',
    DASHBOARD: '/superset/dashboard/',
    CSRF_TOKEN: '/api/v1/security/csrf_token/',
  },
  
  // Default UI configuration for embedded dashboards
  DEFAULT_UI_CONFIG: {
    hideTitle: false,
    hideTab: false,
    hideChartControls: false,
    filters: {
      expanded: true,
      visible: true,
    },
  },
  
  // Admin user configuration
  ADMIN_USER: {
    username: '<EMAIL>',
    password: '130901',
  },

  // Guest user configuration for development
  GUEST_USER: {
    username: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
  },
} as const;

// Helper function to get dashboard URL
export const getDashboardUrl = (
  dashboardId: string = SUPERSET_CONFIG.DEFAULT_DASHBOARD_ID,
  includeNativeFilters = true
) => {
  const baseUrl = `${SUPERSET_CONFIG.HOST}${SUPERSET_CONFIG.ENDPOINTS.DASHBOARD}${dashboardId}/`;

  if (includeNativeFilters && SUPERSET_CONFIG.NATIVE_FILTERS_KEY) {
    return `${baseUrl}?native_filters_key=${SUPERSET_CONFIG.NATIVE_FILTERS_KEY}`;
  }

  return baseUrl;
};

// Helper function to get guest token URL
export const getGuestTokenUrl = () => {
  return `${SUPERSET_CONFIG.HOST}${SUPERSET_CONFIG.ENDPOINTS.GUEST_TOKEN}`;
};

// Helper function to get login URL
export const getLoginUrl = () => {
  return `${SUPERSET_CONFIG.HOST}${SUPERSET_CONFIG.ENDPOINTS.LOGIN}`;
};

// Helper function to get CSRF token URL
export const getCsrfTokenUrl = () => {
  return `${SUPERSET_CONFIG.HOST}${SUPERSET_CONFIG.ENDPOINTS.CSRF_TOKEN}`;
};
