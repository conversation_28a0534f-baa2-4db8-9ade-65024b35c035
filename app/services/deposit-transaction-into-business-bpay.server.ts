import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface GetListDepositTransactionIntoBusinessBPayByBusinessIdParams {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  sort: PipelineStage.Sort['$sort'];
  isoCode: string;
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
  };
  businessId: string;
}

function getMatcher({
  filter,
}: Pick<
  GetListDepositTransactionIntoBusinessBPayByBusinessIdParams,
  'filter'
>) {
  const matcher: PipelineStage.Match['$match'] = {};

  if (filter.search) {
    matcher['member.name'] = { $regex: filter.search, $options: 'i' };
  }

  return matcher;
}

function getCommonPipeline({
  isoCode,
  filter,
  businessId,
}: Pick<
  GetListDepositTransactionIntoBusinessBPayByBusinessIdParams,
  'filter' | 'businessId' | 'isoCode'
>) {
  return [
    {
      $match: {
        createdAt: {
          $gte: filter?.rangeDate?.from,
          $lte: filter?.rangeDate?.to,
        },
        businessId,
        type: BUSINESS_TRANSACTION_TYPE.C,
        name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_MEMBER,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).businessMember,
        localField: 'memberId',
        foreignField: '_id',
        as: 'businessMember',
      },
    },
    {
      $unwind: {
        path: '$businessMember',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'businessMember.userId',
        foreignField: '_id',
        as: 'member',
      },
    },
    {
      $unwind: {
        path: '$member',
        preserveNullAndEmptyArrays: true,
      },
    },
    { $match: getMatcher({ filter }) },
  ];
}

export async function getListDepositTransactionIntoBusinessBPayByBusinessId({
  skip,
  limit,
  sort,
  isoCode,
  filter,
  businessId,
}: GetListDepositTransactionIntoBusinessBPayByBusinessIdParams) {
  const data = await getModels(isoCode)
    .businessTransaction.aggregate([
      ...getCommonPipeline({ isoCode, filter, businessId }),
      {
        $facet: {
          data: [
            { $sort: { ...sort, _id: 1 } },
            { $skip: skip },
            { $limit: limit },
            {
              $project: {
                member: {
                  name: 1,
                },
                createdAt: 1,
                amount: 1,
              },
            },
          ],
          totalAmount: [
            {
              $group: {
                _id: null,
                totalAmount: { $sum: '$amount' },
              },
            },
          ],
        },
      },
    ])
    .exec();

  return {
    data: data?.[0]?.data || [],
    totalAmount: data[0].totalAmount[0]?.totalAmount || 0,
  };
}

export async function getTotalRecordDepositTransactionIntoBusinessBPayByBusinessId({
  filter,
  isoCode,
  businessId,
}: Pick<
  GetListDepositTransactionIntoBusinessBPayByBusinessIdParams,
  'filter' | 'isoCode' | 'businessId'
>) {
  const [data] = await getModels(isoCode)
    .businessTransaction.aggregate([
      ...getCommonPipeline({ isoCode, filter, businessId }),
      { $count: 'total' },
    ])
    .exec();

  return data?.total || 0;
}
