import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useFetcher,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  DATE_RANGE_OPTIONS,
  PERMISSIONS,
} from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BTaskeeTableV2,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getRangeByPresetOptions,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import type { TaskerIncomeMonthly } from '~/services/tasker-income.server';
import {
  exportTaskerIncomeMonthly,
  getTaskerIncomeMonthly,
} from '~/services/tasker-income.server';

export const loader = hocLoader(async ({ request }) => {
  const [
    { taskId, sort, dateFilter: dateRangeFromSearchParam },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(new URL(request.url).searchParams, {
    keysString: ['taskId', 'sort', 'dateFilter'],
    keysNumber: ['pageSize', 'pageIndex'],
  });
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const settingCountry = await getSettingCountryByIsoCode({
    isoCode,
    projection: { currency: 1 },
  });
  const dateRange = dateRangeFromSearchParam
    ? JSON.parse(dateRangeFromSearchParam)
    : getRangeByPresetOptions(DATE_RANGE_OPTIONS.LAST_MONTH);

  const [sortField, sortOrder] = sort.split(':');
  const limit = pageSize || 10;
  const skip = pageIndex * limit || 0;

  const isFullYearFilter =
    momentTz(dateRange?.from).isSame(
      momentTz(dateRange?.from).startOf('year'),
    ) && momentTz(dateRange?.to).isSame(momentTz(dateRange?.to).endOf('year'));
  const MONTH_WHEN_FULL_YEAR_FILTER = 0;
  const DEFAULT_VALID_MONTH = momentTz(dateRange?.from).month() + 1;

  const month = !dateRangeFromSearchParam
    ? DEFAULT_VALID_MONTH
    : isFullYearFilter
      ? MONTH_WHEN_FULL_YEAR_FILTER
      : DEFAULT_VALID_MONTH;
  const year = momentTz(dateRange?.from).year();

  const taskerIncomeMonthly = await getTaskerIncomeMonthly({
    month,
    year,
    taskId: taskId,
    limit,
    skip,
    sortField: sortField || 'date_id',
    sortOrder: sortOrder === 'asc' ? 'ASC' : 'DESC',
  });

  return json({
    currency: settingCountry?.currency?.sign || '',
    taskerIncomeMonthly,
    filters: {
      dateRange,
    },
    month,
    year,
  });
}, PERMISSIONS.READ_TASKER_INCOME);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const month = formData.get('month');
    const year = formData.get('year');

    const taskerIncomeMonthlyExport = await exportTaskerIncomeMonthly({
      month: Number(month),
      year: Number(year),
    });
    setInformationActionHistory({
      action: ACTION_NAME.EXPORT_TASKER_INCOME,
      dataRelated: taskerIncomeMonthlyExport,
    });

    return json({ taskerIncomeMonthlyExport });
  },
  PERMISSIONS.EXPORT_TASKER_INCOME,
);

export default function TaskerIncomeScreen() {
  const { t: tTaskerIncome } = useTranslation('tasker-income');
  const [searchParams] = useSearchParams();

  const { currency, taskerIncomeMonthly, filters, month, year } =
    useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);
  const fetchData = useFetcher<ActionTypeWithError<typeof action>>();

  const { exportCsv: exportListTaskAskerIncome } = useExportFile();
  const confirmExport = useConfirm();
  const [isExporting, setIsExporting] = useState(false);

  const onExportExcel = useCallback(async () => {
    const isConfirm = await confirmExport({
      title: tTaskerIncome('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {tTaskerIncome('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTaskerIncome('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {fetchData?.data?.taskerIncomeMonthlyExport?.length || 0}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTaskerIncome('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {momentTz(filters.dateRange.from).format('DD/MM/YYYY')} -{' '}
              {momentTz(filters.dateRange.to).format('DD/MM/YYYY')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tTaskerIncome('CANCEL'),
      actionButton: tTaskerIncome('CONFIRM'),
    });

    if (isConfirm) {
      const exportedExcelData = fetchData?.data?.taskerIncomeMonthlyExport?.map(
        item => ({
          [tTaskerIncome('TASK_CODE')]: item?.transaction_id || '',
          [tTaskerIncome('TASK_DATE')]:
            momentTz(item?.date).format('DD/MM/YYYY') || '',
          [tTaskerIncome('TASKER_ID')]: item?.user_id || '',
          [tTaskerIncome('AMOUNT')]: item?.amount || 0,
          [tTaskerIncome('TRANSACTION_TYPE')]: item?.text || '',
        }),
      );

      const fileName = `Tasker Income ${momentTz(filters.dateRange.from).format('DD/MM/YYYY')} - ${momentTz(filters.dateRange.to).format('DD/MM/YYYY')}`;

      exportListTaskAskerIncome({
        fileName,
        arrayJson: exportedExcelData || [],
      });
    }
    setIsExporting(false);
  }, [
    confirmExport,
    exportListTaskAskerIncome,
    fetchData?.data,
    filters.dateRange.from,
    filters.dateRange.to,
    tTaskerIncome,
  ]);

  const onClickExportExcel = () => {
    fetchData.submit({ month, year }, { method: 'post' });
    setIsExporting(true);
  };

  useEffect(() => {
    if (fetchData?.data && isExporting) {
      onExportExcel();
    }
  }, [fetchData?.data, isExporting, onExportExcel]);

  const columns: ColumnDef<SerializeFrom<TaskerIncomeMonthly>>[] = useMemo(
    () => [
      {
        accessorKey: 'task_code',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerIncome('TASK_CODE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original.transaction_id || ''}
          </Typography>
        ),
        enableSorting: false,
        size: 345,
      },
      {
        accessorKey: 'date_id',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerIncome('TASK_DATE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original.date
              ? momentTz(row.original.date).format('DD/MM/YYYY')
              : '-'}
          </Typography>
        ),
      },
      {
        accessorKey: 'tasker_id',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerIncome('TASKER_ID')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original.user_id || ''}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerIncome('AMOUNT')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(row.original.amount || 0)}
            {currency || ''}
          </Typography>
        ),
      },
      {
        accessorKey: 'transaction_type',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerIncome('TRANSACTION_TYPE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original.text || ''}</Typography>
        ),
        enableSorting: false,
      },
    ],
    [currency, tTaskerIncome],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{tTaskerIncome('TASKER_INCOME')}</Typography>
          <Breadcrumbs />
        </Grid>
        {permissions.includes(PERMISSIONS.EXPORT_TASKER_INCOME) ? (
          <Button
            className="gap-2 text-sm font-medium"
            variant="default"
            onClick={onClickExportExcel}
            disabled={taskerIncomeMonthly?.totals?.totalRows === 0}>
            <Upload className="w-5 h-5" />
            {tTaskerIncome('EXPORT_TO_EXCEL')}
          </Button>
        ) : null}
      </div>
      <BTaskeeTableV2
        total={taskerIncomeMonthly?.totals?.totalRows || 0}
        data={taskerIncomeMonthly?.rows || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: taskerIncomeMonthly?.totals?.totalRows || 0,
          pageSize: Number(searchParams.get('pageSize') || 10),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        translationKey="tasker-income"
        searchInput={{
          defaultValue: searchParams.get('taskId') || '',
          name: 'taskId',
          placeholder: tTaskerIncome('SEARCH_PLACEHOLDER'),
        }}
        initialFilterDate={{
          name: 'dateFilter',
          mode: 'month-year',
          minDate: momentTz('2015-01-01').startOf('month').toDate(),
          maxDate: momentTz().endOf('year').toDate(),
          defaultValue: {
            from: momentTz(filters.dateRange.from).toDate(),
            to: momentTz(filters.dateRange.to).toDate(),
          },
        }}
        renderExtraComponent={[
          {
            position: 'bottom',
            component: (
              <div className="w-full overflow-x-auto">
                <div className="flex gap-6 justify-end min-w-max pb-1">
                  <div className="inline-flex flex-col items-center gap-1">
                    <Typography variant="p" className="text-gray-400 text-xs">
                      {tTaskerIncome('TOTAL_INCOME_AMOUNT')}
                    </Typography>
                    <Badge className="min-w-[160px] mx-[26px] bg-blue-50 text-blue-500 rounded-md w-fit py-3">
                      <Typography variant="p" className="mx-auto font-semibold">
                        {formatNumberWithCommas(
                          taskerIncomeMonthly?.totals?.totalAmount || 0,
                        )}
                        {currency || ''}
                      </Typography>
                    </Badge>
                  </div>
                </div>
              </div>
            ),
          },
        ]}
      />
    </>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
