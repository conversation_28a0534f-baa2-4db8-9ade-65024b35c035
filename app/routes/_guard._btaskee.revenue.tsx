import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import type { FactBTaskeeIncome } from '~/services/revenue.server';
import { getBTaskeeIncomeMonthly } from '~/services/revenue.server';

export const handle = {
  breadcrumb: () => {
    return <BreadcrumbsLink to={ROUTE_NAME.BTASKEE_REVENUE} label="REVENUE" />;
  },
  i18n: 'subscription',
};

export const loader = hocLoader(async ({ request }) => {
  const [{ taskId, sort }, { month, year, pageSize, pageIndex }] =
    getValuesFromSearchParams(new URL(request.url).searchParams, {
      keysString: ['taskId', 'sort'],
      keysNumber: ['month', 'year', 'pageSize', 'pageIndex'],
    });

  const { isoCode } = await getUserSession({
    headers: request.headers,
  });
  const settingCountry = await getSettingCountryByIsoCode({
    isoCode,
    projection: { currency: 1 },
  });

  const [sortField, sortOrder] = sort.split(':');
  const limit = pageSize || 10;
  const skip = pageIndex * limit || 0;

  let m = momentTz().month();
  let y = momentTz().year();

  if (!month && year) {
    m = 0;
    y = year;
  } else if (month && year) {
    m = month;
    y = year;
  }
  const isYearFilter = !month && year;

  const revenueMonthly = await getBTaskeeIncomeMonthly({
    month: m,
    year: y,
    taskId,
    sortField: sortField || 'task_cost',
    sortOrder: sortOrder === 'asc' ? 'ASC' : 'DESC',
    limit,
    skip,
  });

  return json({
    revenueMonthly,
    currency: settingCountry?.currency?.sign || '',
    filters: {
      date: {
        from: isYearFilter
          ? momentTz({ year }).startOf('year').toDate()
          : month
            ? momentTz({ year, month: month - 1 })
                .startOf('month')
                .toDate()
            : momentTz().subtract(1, 'month').startOf('month').toDate(),
        to: isYearFilter
          ? year < momentTz().year()
            ? momentTz({ year }).endOf('year').toDate()
            : momentTz().subtract(1, 'month').endOf('month').toDate()
          : month
            ? momentTz({ year, month: month - 1 })
                .endOf('month')
                .toDate()
            : momentTz().subtract(1, 'month').endOf('month').toDate(),
      },
      mode: isYearFilter ? 'year' : 'month',
    },
    month: m - 1,
    year: y,
  });
}, PERMISSIONS.READ_BTASKEE_REVENUE);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function BtaskeeRevenueListScreen() {
  const { t: tRevenue } = useTranslation('revenue');
  const [searchParams] = useSearchParams();

  const {
    filters,
    revenueMonthly,
    currency = '',
  } = useLoaderDataSafely<typeof loader>();

  const permissions = useGlobalStore(store => store.permissions);

  const { exportCsv: exportListTaskOfSubscription } = useExportFile();
  const confirmExport = useConfirm();

  const onExportExcel = async () => {
    const exportedExcelData = revenueMonthly.rows?.map(
      (item: FactBTaskeeIncome) => ({
        [tRevenue('TRANSACTION_ID')]: item?.task_id || '',
        [tRevenue('DATE')]: momentTz(item?.date).format('HH:mm - DD/MM/YYYY'),
        [tRevenue('SERVICE_NAME')]: item?.service_name || '',
        [tRevenue('TASK_COST')]: item?.task_cost || 0,
        [tRevenue('EXCLUDE_COST')]: item?.exclude_cost || 0,
        [tRevenue('NUMBER_OF_TASKERS')]: item?.number_of_taskers || 0,
        [tRevenue('MAIN_INCOME')]: item?.main_income || 0,
        [tRevenue('PROMOTION_INCOME')]: item?.promotion_income || 0,
        [tRevenue('PROMOTION')]: item?.promotion || 0,
        [tRevenue('PAYMENT_METHOD')]: item?.payment_method || '',
      }),
    );

    const fileName = `Revenues ${momentTz(filters.date.from).format('DD_MM_YYYY')}-${momentTz(filters.date.to).format('DD_MM_YYYY')}`;

    const isConfirm = await confirmExport({
      title: tRevenue('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {tRevenue('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tRevenue('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {revenueMonthly.totals.totalRows}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tRevenue('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {momentTz(filters.date.from).format('DD/MM/YYYY')} -{' '}
              {momentTz(filters.date.to).format('DD/MM/YYYY')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tRevenue('CANCEL'),
      actionButton: tRevenue('CONFIRM'),
    });

    if (isConfirm) {
      exportListTaskOfSubscription({
        fileName,
        arrayJson: exportedExcelData,
      });
    }
  };

  const columns: ColumnDef<FactBTaskeeIncome>[] = [
    {
      accessorKey: 'date',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={tRevenue('DATE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.date
            ? momentTz(row.original?.date).format('HH:mm - DD/MM/YYYY')
            : ''}
        </Typography>
      ),
    },
    {
      accessorKey: 'task_id',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('TRANSACTION_ID')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          <Typography variant="p">{row.original?.task_id || ''}</Typography>
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'service_name',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('SERVICE_NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.service_name || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'task_cost',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={tRevenue('TASK_COST')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {formatNumberWithCommas(Number(row.original?.task_cost))}
          {currency}
        </Typography>
      ),
    },
    {
      accessorKey: 'exclude_cost',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('EXCLUDE_COST')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {Number(row.original?.exclude_cost)}
        </Typography>
      ),
    },
    {
      accessorKey: 'number_of_taskers',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('NUMBER_OF_TASKERS')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {Number(row.original?.number_of_taskers)}
        </Typography>
      ),
    },
    {
      accessorKey: 'main_income',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('MAIN_INCOME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {formatNumberWithCommas(Number(row.original?.main_income))}
          {currency}
        </Typography>
      ),
    },
    {
      accessorKey: 'promotion_income',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('PROMOTION_INCOME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {Number(row.original?.promotion_income)}
        </Typography>
      ),
    },
    {
      accessorKey: 'promotion',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={tRevenue('PROMOTION')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{Number(row.original?.promotion)}</Typography>
      ),
    },
    {
      accessorKey: 'payment_method',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tRevenue('PAYMENT_METHOD')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.payment_method || ''}
        </Typography>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{tRevenue('REVENUE')}</Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={onExportExcel}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_LIST_OF_BTASKEE_REVENUE)
          }>
          <Upload className="w-5 h-5" />
          {tRevenue('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={revenueMonthly.totals.totalRows || 0}
        data={revenueMonthly.rows || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: revenueMonthly.totals.totalRows || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="revenue"
        search={{
          defaultValue: searchParams.get('taskId') || '',
          name: 'taskId',
          placeholder: tRevenue('SEARCH_BY_TRANSACTION_ID'),
        }}
        filterDate={{
          name: 'monthYearFilter',
          mode: 'month-year',
          selectMode: (filters?.mode as 'year' | 'month') || 'month',
          minDate: momentTz('2015-01-01').startOf('month').toDate(),
          maxDate: momentTz().endOf('month').toDate(),
          defaultValue: {
            from: momentTz(filters.date.from).toDate(),
            to: momentTz(filters.date.to).toDate(),
          },
          variant: {
            chevrons: 'outline',
          },
        }}
        extraContent={
          <div className="w-full overflow-x-auto">
            <div className="flex gap-6 justify-end min-w-max pb-1">
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tRevenue('TOTAL_AMOUNT')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-blue-50 text-blue-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      revenueMonthly?.totals?.totalTaskCost || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tRevenue('TOTAL_MAIN_ACCOUNT')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-green-50 text-green-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      revenueMonthly?.totals?.totalMainIncome || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tRevenue('TOTAL_SUB_ACCOUNT')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-yellow-50 text-yellow-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      revenueMonthly?.totals?.totalPromotionIncome || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography
                  variant="p"
                  className="text-gray-400 text-xs -mr-[26px]">
                  {tRevenue('TOTAL_PROMOTION_ACCOUNT')}
                </Typography>
                <Badge className="min-w-[160px] ml-[26px] bg-primary-50 text-primary rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      revenueMonthly?.totals?.totalPromotion || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
            </div>
          </div>
        }
      />
    </>
  );
}
