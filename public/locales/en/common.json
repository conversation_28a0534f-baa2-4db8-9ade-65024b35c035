{"SETTINGS": "Setting", "GO_BACK": "Go back", "SUCCESS": "Success", "INFORMATION": "Information", "ERROR": "Error", "WARNING": "Warning", "PARENT_GROUP_NOT_FOUND": "Parent group not found", "USER_DONT_HAVE_PERMISSION": "User don't have permission", "EMAIL_SERVICE_ERROR": "Email service error", "ENV_ERROR": "There is an error with the server environment variables", "LOGIN_FAILURE": "Login failure", "INCORRECT_ACCOUNT": "Incorrect account", "EMAIL_INCORRECT": "Email incorrect", "CODE_INCORRECT_OR_EXPIRED": "Code incorrect or expired", "LOGOUT": "Logout", "ACTIONS_HISTORY": "Action History", "USERS_MANAGEMENT": "Users management", "PROFILE": "Profile", "GROUPS": "Groups", "EDIT_ROLE": "Edit role", "EDIT_GROUP": "Edit group", "ACCESS_DENIED": "Access Denied", "NOT_PERMISSION": "You do not have permission to access this page.", "PAGE_NOT_FOUND": "Sorry, the page cannot be found", "PAGE_NOT_FOUND_DESCRIPTION": "The page you are looking for does not exist!", "SEARCH": "Search ...", "CREATE_ROLE": "Create role", "CREATE_GROUP": "Create group", "GROUPS_MANAGEMENT": "Group", "UPDATE_USER": "Update user", "ERROR_BY_MAX_FILE_SIZE": "Exceeded image file size limit.", "IMAGE_PREVIEW": "Image preview", "UPLOAD": "Upload", "PASSWORD_NOT_MATCH": "Password not match", "UNKNOWN_ERROR": "Unknown error", "TOTAL_RECORDS": "{{total}} records", "SEARCH_PLACEHOLDER": "Search...", "YESTERDAY": "Yesterday", "THIS_MONTH": "This month", "LAST_MONTH": "Last month", "THIS_YEAR": "This year", "LAST_YEAR": "Last year", "TODAY": "Today", "ONCE_PAY": "Full payment", "BNPL": "Installment", "BNPL_NAV": "<PERSON><PERSON><PERSON>", "CLEAR_FILTERS": "Clear filters", "NO_RESULTS_FOUND": "No results found", "CREATE_USER": "Create user", "LAST_7_DAYS": "Last 7 days", "LAST_14_DAYS": "Last 14 days", "LAST_30_DAYS": "Last 30 days", "CANCEL": "Cancel", "APPLY": "Apply", "BPAY": "Employee bPay Management", "RESET": "Reset", "PICK_A_MONTH": "Pick A Month", "PICK_RANGE_MONTH": "Pick Range Month", "LAST_SIX_MONTHS": "Last 6 months", "LAST_TWELVE_MONTHS": "Last 12 months", "PAYMENT_MANAGEMENT": "Payment Management", "PAY_NOW": "Pay now", "PAY_LATER": "Pay later", "TRACKING_DAILY_TRANSACTION": "Tracking Daily Transaction", "SELECTED": "Selected", "PAYING": "Paying", "DONE": "Done", "OVER_DUE": "Overdue", "STOPPED": "Stopped", "INSTALLMENT_MANAGEMENT": "Installment Management", "OTHERS": "Others", "SUBSCRIPTION": "Subscription", "TASKER": "Tasker", "TASKER_BALANCE": "Tasker <PERSON>lance", "ASKER": "<PERSON><PERSON>", "ASKER_BALANCE": "<PERSON><PERSON>", "BTASKEE": "b<PERSON><PERSON><PERSON>", "BALANCE": "Balance", "BEMPLOYEE": "bEmployee", "SELECT_DATE_RANGE_ON_BTASKEE_TABLE": "Select a date range", "ROW_PER_PAGES_ON_BTASKEE_TABLE": "Rows per page", "VIEW_TOGGLE_COLLUMN_BUTTON_ON_BTASKEE_BTASKEE_TABLE": "View", "LIST_TOGGLE_COLUMNS_ON_BTASKEE_TABLE": "Toggle columns", "PAGINATION_DISPLAYING_ON_BTASKEE_TABLE": "Page {{from}} of {{to}}", "SELECTED_ROW_DISPLAYING_ON_BTASKEE_TABLE": "{{numOfSelected}} of {{total}} row(s) selected", "PERMISSION": "Permission", "USER_MANAGEMENT": "User Management", "CONFIGURATION": "Configuration", "PERMISSION_NOT_FOUND": "Permission not found", "LIST_PERMISSIONS": "Permission group details", "ACTIVE": "Active", "INACTIVE": "Inactive", "USER_NOT_FOUND": "User not found", "YOU_CANNOT_ADD_YOURSELF_TO_THE_GROUP": "You cannot add yourself to the group", "USER_ALREADY_EXISTS": "User already exists", "INTERNAL_SERVER_ERROR": "Internal server error", "PAGE_NOT_EXIST": "The page you are looking for does not exist.", "SERVER_ERROR": "Server Error", "TRY_AGAIN_LATER": "An error occurred. Please try again later.", "SELECT_YEAR": "Select year", "ACCOUNT_INACTIVE": "Account is not activated", "REVENUE": "Revenue", "BUSINESS": "Business", "BPAY_BUSINESS_ACCOUNT": "bPay Business Account", "BUSINESS_MANAGEMENT": "Business Management", "MANAGE_BUSINESS_BPAY_ACCOUNT": "Business Management", "BUSINESS_DETAILS": "Business Details", "BUSINESS_BPAY_HISTORY": "Business bPay Transaction History", "MEMBER_BUSINESS_BPAY": "Manage Member's Business bPay", "BPAY_TRANSACTION": "Business bPay Transaction", "INCOME": "Income", "SALES_REPORT": "Sales Report", "SALES_REPORT_DETAIL": "Sales Report Detail", "INDIVIDUAL": "Individual", "MONTHLY_INCOME": "Monthly Income", "DAILY_INCOME": "Daily Income", "INCOME_TAX_REPORT_GENERAL": "Income Tax Report - General", "INCOME_TAX_REPORT_DETAIL": "Income Tax Report - Detail"}