import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import {
  DATE_RANGE_OPTIONS,
  PERMISSIONS,
  ROUTE_NAME,
  res404,
} from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import { useExportFile, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Grid,
  MonthRangePicker,
  SelectBase,
  Typography,
  YearRangePicker,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getRangeByPresetOptions,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getListBtaskeeIncome } from '~/services/individual-btaskee-income.server';

export enum RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME {
  RANGE_MONTH = 'RANGE_MONTH',
  RANGE_YEAR = 'RANGE_YEAR',
}

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.ACCOUNTING_MONTHLY_INCOME}
      label="MONTHLY_INCOME"
    />
  ),
  i18n: 'btaskee-income',
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  if (!getEnvFeatureFlag()?.Btaskee_Income) {
    throw new Response(null, res404);
  }
  const url = new URL(request.url);

  const rangeDateSearchValue = url.searchParams.get('rangeDate');

  const parsedRangeDate =
    rangeDateSearchValue &&
    rangeDateSearchValue !== 'null' &&
    rangeDateSearchValue !== 'undefined'
      ? {
          from: momentTz(JSON.parse(rangeDateSearchValue).from).toDate(),
          to: momentTz(JSON.parse(rangeDateSearchValue).to).toDate(),
        }
      : getRangeByPresetOptions(DATE_RANGE_OPTIONS.THIS_MONTH);

  return json({
    filteredValue: {
      rangeDate: parsedRangeDate,
    },
  });
}, PERMISSIONS.READ_ACCOUNTING_MONTHLY_INCOME);

export const action = hocAction(async ({ request }) => {
  const formData = await request.clone().formData();
  const actionType = formData.get('actionType')?.toString() || '';

  if (actionType === 'EXPORT_TRANSACTION') {
    const url = new URL(request.url);

    const [{ rangeDate }] = getValuesFromSearchParams(url.searchParams, {
      keysString: ['rangeDate'],
    });

    const { isoCode } = await getUserSession({ headers: request.headers });
    const parsedRangeDate =
      rangeDate && rangeDate !== 'null' && rangeDate !== 'undefined'
        ? {
            from: momentTz(JSON.parse(rangeDate).from).toDate(),
            to: momentTz(JSON.parse(rangeDate).to).toDate(),
          }
        : getRangeByPresetOptions(DATE_RANGE_OPTIONS.THIS_MONTH);

    const filteredValue = {
      rangeDate: {
        from: momentTz(parsedRangeDate?.from).toDate(),
        to: momentTz(parsedRangeDate?.to).toDate(),
      },
    };

    const listMonthlyIncome = await getListBtaskeeIncome({
      filter: filteredValue,
      isoCode,
    });

    return json({
      actionType,
      listMonthlyIncome,
    });
  }

  return json({
    actionType: '',
    listMonthlyIncome: [],
  });
}, PERMISSIONS.READ_ACCOUNTING_MONTHLY_INCOME);

export default function MonthlyInCome() {
  const { t: tMonthlyInCome } = useTranslation('btaskee-income');
  const { filteredValue, error: loaderError } =
    useLoaderDataSafely<typeof loader>();
  const [selectedRangeDateMode, setSelectedRangeDateMode] = useState<string>(
    RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH,
  );
  const [, setSearchParams] = useSearchParams();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  const confirmExport = useConfirm();
  const submit = useSubmit();
  const { exportCsv: exportTransactionList } = useExportFile();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }

    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [loaderError, actionData?.error]);

  const handleFilterDateRange = (rangeDateValue: { from: Date; to: Date }) => {
    setSearchParams(params => {
      params.set('rangeDate', JSON.stringify(rangeDateValue));

      return params;
    });
  };

  const submitExportingAction = async () => {
    const isConfirm = await confirmExport({
      title: tMonthlyInCome('EXPORT_EXCEL_FILE'),
      body: tMonthlyInCome('ARE_YOU_SURE_EXPORT_TO_EXCEL'),
      cancelButton: tMonthlyInCome('CANCEL'),
      actionButton: tMonthlyInCome('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('actionType', 'EXPORT_TRANSACTION');

      submit(formData, { method: 'post' });
    }
  };

  useEffect(() => {
    if (actionData?.actionType === 'EXPORT_TRANSACTION') {
      if (actionData.listMonthlyIncome?.length) {
        /**
         * Add column with value as required by accounting department include:
         *  - PRODUCT_ID: 1
         *  - QUANTITY: 1
         *  - VAT: 'KCT'
         *  - PROPERTY: 1
         *  - The column with empty title: 1
         */
        const exportMonthlyIncome = actionData.listMonthlyIncome.map(
          (formattedIncome, orderIndex) => ({
            [tMonthlyInCome('ORDER_NUMBER')]: (orderIndex ?? 0) + 1,
            [tMonthlyInCome('TRANSACTION_DATE')]: momentTz(
              formattedIncome?.transactionDate,
            ).format('HH:mm - DD/MM/YYYY'),
            [tMonthlyInCome('PRODUCT_ID')]: 1,
            [tMonthlyInCome('PRODUCT_NAME')]: tMonthlyInCome(
              'PRODUCTION_NAME_IN_EXPORT_DATA',
            ),
            [tMonthlyInCome('TRANSACTION_ID')]: formattedIncome?.taskId ?? '',
            [tMonthlyInCome('UNIT')]: tMonthlyInCome('UNIT_IN_EXPORT_DATA'),
            [tMonthlyInCome('QUANTITY')]: 1,
            [tMonthlyInCome('AMOUNT')]: formattedIncome?.amount ?? 0,
            [tMonthlyInCome('VAT')]: 'KCT',
            [tMonthlyInCome('PROPERTY')]: 1,
            '': 1,
            [tMonthlyInCome('PRODUCT_PROPERTY')]: tMonthlyInCome(
              'SOFTWARE_APPLICATION',
            ),
          }),
        );

        const fileName = `${tMonthlyInCome('TITLE_BTASKEE_INCOME_REPORT')} ${momentTz(filteredValue?.rangeDate?.from).format('DD/MM/YYYY')} to ${momentTz(filteredValue?.rangeDate?.to).format('DD/MM/YYYY')}`;

        exportTransactionList({
          fileName,
          arrayJson: exportMonthlyIncome,
        });
      } else {
        toast({
          variant: 'information',
          description: tMonthlyInCome('TRANSACTION_NOT_FOUND', {
            fromDate: momentTz(filteredValue?.rangeDate?.from).format(
              'DD-MM-YYYY',
            ),
            toDate: momentTz(filteredValue?.rangeDate?.to).format('DD-MM-YYYY'),
          }),
        });
      }
    }
    // Only run useEffect for actionType dependencies
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actionData?.actionType]);

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">
            {tMonthlyInCome('MONTHLY_INCOME')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <Grid className="flex gap-2">
        <SelectBase
          defaultValue={selectedRangeDateMode}
          backgroundColor="h-8 w-36"
          onValueChange={rangeDate => {
            setSearchParams(params => {
              params.delete('rangeDate');

              return params;
            });
            setSelectedRangeDateMode(rangeDate);
          }}
          options={Object.values(
            RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
          ).map(rangeDateMode => ({
            label: tMonthlyInCome(rangeDateMode),
            value: rangeDateMode,
          }))}
        />
        <div className="col-span-5">
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH ? (
            <MonthRangePicker
              onMonthRangeSelect={handleFilterDateRange}
              defaultRangeMonth={{
                from: momentTz(filteredValue?.rangeDate?.from).toDate(),
                to: momentTz(filteredValue?.rangeDate?.to).toDate(),
              }}
            />
          ) : null}
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_YEAR ? (
            <YearRangePicker
              onYearRangeSelect={handleFilterDateRange}
              defaultRangeYear={{
                from: momentTz(filteredValue?.rangeDate?.from).toDate(),
                to: momentTz(filteredValue?.rangeDate?.to).toDate(),
              }}
            />
          ) : null}
        </div>
        <Button className="h-8" onClick={submitExportingAction}>
          {tMonthlyInCome('EXPORT_EXCEL_FILE')}
        </Button>
      </Grid>
    </>
  );
}
