import {
  BUSINESS_TRANSACTION_TYPE,
  DATE_RANGE_OPTIONS,
} from 'btaskee-constants';
import {
  getFormattedPhoneNumber,
  getRangeByPresetOptions,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import * as XLSX from 'xlsx';
import { hocLoader } from '~/hoc/remix';
import useGetMemberTransactionForSalesReport from '~/hooks/useGetMemberTransactionForSalesReport';
import i18next from '~/i18next.server';
import { getUserSession } from '~/services/helpers.server';
import {
  getListSaleReportWithoutPagination,
  getMemberTransactionsReportByBusinessIds,
} from '~/services/sales-report.server';

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const { isoCode } = await getUserSession({ headers: request.headers });

  const [{ businessIds, rangeDate, searchText, serviceIds }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['businessIds', 'rangeDate', 'searchText', 'serviceIds'],
    });

  const filteredRangeDate =
    rangeDate && rangeDate !== 'null' && rangeDate !== 'undefined'
      ? {
          from: momentTz(JSON.parse(rangeDate).from).toDate(),
          to: momentTz(JSON.parse(rangeDate).to).toDate(),
        }
      : getRangeByPresetOptions(DATE_RANGE_OPTIONS.THIS_MONTH);

  const filteredSaleReport = {
    ...(filteredRangeDate ? { rangeDate: filteredRangeDate } : {}),
    searchText,
    serviceIds,
  };

  const [dataCalculatingTotalAmount, businessMemberTransactions] =
    await Promise.all([
      getListSaleReportWithoutPagination({
        filter: filteredSaleReport,
        isoCode,
      }),
      getMemberTransactionsReportByBusinessIds({
        businessIds: businessIds.split(','),
        ...(filteredRangeDate ? { rangeDate: filteredRangeDate } : {}),
        isoCode,
      }),
    ]);

  const tExportTransaction = await i18next.getFixedT(
    request,
    'sales-report-detail',
  );

  const { transactions: memberTransactions } =
    useGetMemberTransactionForSalesReport({
      transactions: businessMemberTransactions,
    });

  const formattedReportData = () => {
    const uniqueBusinessIds = new Set(
      dataCalculatingTotalAmount?.map(item => item.businessId) ?? [],
    );

    return Array.from(uniqueBusinessIds).map(businessId => {
      const businessData = dataCalculatingTotalAmount?.filter(
        item => item.businessId === businessId,
      );

      const totalTaskPrice = businessData?.reduce(
        (acc, currentVal) =>
          acc +
          (currentVal?.task?.newCostDetail?.cost ??
            currentVal?.task?.costDetail?.cost ??
            0),
        0,
      );

      const totalPromotionAmount = businessData?.reduce(
        (acc, currentVal) =>
          acc +
          (currentVal?.task?.costDetail?.decreasedReasons
            ?.filter(
              decreasedReason =>
                decreasedReason.key === 'PROMOTION_CODE' &&
                decreasedReason.promotionBy === 'BTASKEE',
            )
            .reduce((acc, currentVal) => acc + (currentVal?.value ?? 0), 0) ??
            0),
        0,
      );

      return {
        businessId,
        businessName: businessData?.[0]?.business?.name ?? '',
        businessPhone: businessData?.[0]?.businessUser?.phone ?? '',
        totalPaid: totalTaskPrice - totalPromotionAmount,
        totalPromotionAmount: Math.abs(totalPromotionAmount),
        totalCollectionFee: businessData?.reduce(
          (acc, currentVal) =>
            acc +
            (currentVal?.faTransaction?.reduce((acc, currentVal) => {
              if (
                currentVal?.source?.name === 'TASK' &&
                currentVal?.type === BUSINESS_TRANSACTION_TYPE.C
              ) {
                return acc + (currentVal?.amount ?? 0);
              }

              return acc;
            }, 0) ?? 0),
          0,
        ),
      };
    });
  };

  const exportBusinessMemberTransactions = memberTransactions?.map(
    transaction => ({
      [tExportTransaction('TRANSACTION_DATE')]: transaction?.createdAt
        ? momentTz(transaction.createdAt).format('HH:mm - DD/MM/YYYY')
        : '',
      [tExportTransaction('DONE_TASK_DATE')]: transaction?.transactionDate
        ? momentTz(transaction?.transactionDate).format('HH:mm - DD/MM/YYYY')
        : '',
      [tExportTransaction('BUSINESS_ID')]: transaction?.business?._id ?? '',
      [tExportTransaction('BUSINESS_PHONE')]: getFormattedPhoneNumber(
        transaction?.businessUser?.phone ?? '',
      ),
      [tExportTransaction('BUSINESS_NAME')]: transaction?.business?.name ?? '',
      [tExportTransaction('TASK_ID')]: transaction?.task?._id ?? '',
      [tExportTransaction('CUSTOMER_NAME')]: transaction?.asker?.name ?? '',
      [tExportTransaction('PHONE_NUMBER')]: transaction?.asker?.phone ?? '',
      [tExportTransaction('SERVICE')]: transaction?.task?.serviceText?.en,
      [tExportTransaction('PROMOTION_CODE')]:
        transaction?.task?.promotion?.code ?? '',
      [tExportTransaction('TASKER_NAME')]: transaction?.tasker?.name ?? '',
      [tExportTransaction('TASKER_PHONE_NUMBER')]: getFormattedPhoneNumber(
        transaction.tasker?.phone ?? '',
      ),
      [tExportTransaction('PRICE')]: transaction?.taskPrice ?? 0,
      [tExportTransaction('VALUE_OF_PROMOTION')]:
        transaction?.promotionValue ?? 0,
      [tExportTransaction('CUSTOMER_PAY')]: transaction?.customerPay ?? 0,
      [tExportTransaction('COLLECTION_FEE')]: transaction?.collectionFee ?? 0,
      [tExportTransaction('MAIN_ACCOUNT_TASKER')]:
        transaction?.mainAccountTasker ?? 0,
      [tExportTransaction('PROMOTION_ACCOUNT_TASKER')]:
        transaction?.promotionAccountTasker ?? 0,
    }),
  );

  const exportBusinessTransactions = formattedReportData()?.map(report => ({
    [tExportTransaction('BUSINESS_NAME')]: report?.businessName ?? '',
    [tExportTransaction('PHONE_NUMBER')]: getFormattedPhoneNumber(
      report?.businessPhone ?? '',
    ),
    [tExportTransaction('TOTAL_PAID')]: report?.totalPaid || 0,
    [tExportTransaction('TOTAL_PROMOTION_AMOUNT')]:
      report?.totalPromotionAmount || 0,
    [tExportTransaction('TOTAL_COLLECTION_FEE')]:
      report?.totalCollectionFee || 0,
  }));

  const fileType =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';

  const businessWs = XLSX.utils.json_to_sheet(exportBusinessTransactions);
  const memberOfBusinessWs = XLSX.utils.json_to_sheet(
    exportBusinessMemberTransactions,
  );

  const excelBuffer = XLSX.write(
    {
      Sheets: {
        [tExportTransaction('BUSINESS')]: businessWs,
        [tExportTransaction('TRANSACTIONS_OF_BUSINESS_EXPORT_TITLE')]:
          memberOfBusinessWs,
      },
      SheetNames: [
        tExportTransaction('BUSINESS'),
        tExportTransaction('TRANSACTIONS_OF_BUSINESS_EXPORT_TITLE'),
      ],
    },
    {
      bookType: 'xlsx',
      type: 'array',
    },
  );
  const fileName = `Sales Report from ${momentTz(filteredSaleReport?.rangeDate?.from).format('DD/MM/YYYY')} to ${momentTz(filteredSaleReport?.rangeDate?.to).format('DD/MM/YYYY')}`;

  const dataXLSX = new Blob([excelBuffer], { type: fileType });

  return new Response(dataXLSX, {
    status: 200,
    headers: {
      'Content-Type': fileType,
      'Content-Disposition': `attachment; filename=${fileName}.xlsx`,
    },
  });
});
