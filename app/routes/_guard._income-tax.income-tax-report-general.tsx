import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import {
  PERMISSIONS,
  RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
  res404,
} from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  Grid,
  Label,
  SelectBase,
  Separator,
  SingleMonthPicker,
  SingleYearPicker,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getFormattedPhoneNumber,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { endOfMonth, format } from 'date-fns';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getListTaskerIncomeTaxGeneralReport } from '~/services/tasker-income-tax.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  if (!getEnvFeatureFlag().Income_Tax_Report) {
    throw new Response(null, res404);
  }

  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const [{ date, filterOption }] = getValuesFromSearchParams(url.searchParams, {
    keysString: ['date', 'filterOption'],
  });

  const filteredValue = {
    date: momentTz(date || new Date()).toDate(),
    filterOption:
      filterOption || RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH,
    isoCode,
  };

  const exportingResults =
    await getListTaskerIncomeTaxGeneralReport(filteredValue);

  return json({
    exportingResults,
    filteredValue,
  });
}, PERMISSIONS.EXPORT_INCOME_TAX_REPORT_GENERAL);

export default function IncomeTaxReportScreen() {
  const { t: tIncomeTaxReport } = useTranslation('income-tax-report-general');
  const permissions = useGlobalStore(store => store?.permissions);
  const [selectedRangeDateMode, setSelectedRangeDateMode] = useState<string>(
    RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH,
  );
  const { exportCsv: exportReportIncomeTax } = useExportFile();
  const confirmExport = useConfirm();
  const [, setSearchParams] = useSearchParams();

  const {
    error: loaderError,
    exportingResults,
    filteredValue,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: tIncomeTaxReport(loaderError) });
  }, [loaderError, tIncomeTaxReport]);

  const onExportExcel = async () => {
    const formatDate =
      selectedRangeDateMode ===
      RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH
        ? 'MM.yyyy'
        : 'yyyy';
    const filteredTimeValue = format(
      filteredValue?.date || new Date(),
      formatDate,
    );

    const isConfirm = await confirmExport({
      title: tIncomeTaxReport('TITLE_EXPORT_INCOME_TAX'),
      body: (
        <div>
          <Typography variant="p">
            {tIncomeTaxReport('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tIncomeTaxReport('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {exportingResults.length ?? 0}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tIncomeTaxReport('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {`${filteredTimeValue}`}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tIncomeTaxReport('CANCEL'),
      actionButton: tIncomeTaxReport('CONFIRM'),
    });

    if (isConfirm) {
      exportReportIncomeTax({
        fileName: `${tIncomeTaxReport('INCOME_TAX_REPORT_FILE_NAME')} - ${filteredTimeValue}`,
        arrayJson:
          exportingResults?.map((transaction, index) => ({
            [tIncomeTaxReport('ORDER_NUMBER')]: index + 1,
            [tIncomeTaxReport('TASKER_ID')]: transaction?.taskerId ?? '',
            [tIncomeTaxReport('FULL_NAME')]: transaction?.name ?? '',
            [tIncomeTaxReport('BIRTH_DATE')]: transaction?.dob
              ? format(transaction?.dob, 'dd/MM/yyyy')
              : '',
            [tIncomeTaxReport('TAX_CODE')]: '',
            [tIncomeTaxReport('GENDER')]: transaction?.gender ?? '',
            [tIncomeTaxReport('COUNTRY')]: transaction?.country ?? '',
            [tIncomeTaxReport('IDENTITY_CARD_NUMBER')]:
              transaction?.identityCardNumber ?? '',
            [tIncomeTaxReport('IDENTITY_CARD_ISSUED_DATE')]:
              transaction?.identityCardIssuedDate
                ? format(
                    transaction?.identityCardIssuedDate,
                    'dd/MM/yyyy',
                  )
                : '',
            [tIncomeTaxReport('IDENTITY_CARD_ISSUED_PLACE')]:
              transaction?.identityCardIssuedPlace ?? '',
            [tIncomeTaxReport('PHONE_NUMBER_IN_IDENTITY_CARD')]:
              getFormattedPhoneNumber(
                transaction?.phoneNumberInIdentityCard ?? '',
              ),
            [tIncomeTaxReport('PLACE_OF_RESIDENCE')]:
              transaction?.placeOfResidence ?? '',
            [tIncomeTaxReport('TEMPORARY_ADDRESS')]:
              transaction?.temporaryAddress ?? '',
            [tIncomeTaxReport('ETHNIC_GROUP')]: transaction?.ethnicGroup ?? '',
            [tIncomeTaxReport('CONTACT_PHONE')]:
              transaction?.contactPhone ?? '',
            [tIncomeTaxReport('EMAIL')]: transaction?.email ?? '',
            [tIncomeTaxReport('TOTAL_INCOME_TAXABLE')]: (
              transaction?.totalIncomeAmount ?? 0
            ).toLocaleString(),
            [tIncomeTaxReport('DEDUCTION_INCOME_TAX')]: (
              transaction?.totalIncomeTaxAmount ?? 0
            ).toLocaleString(),
            [tIncomeTaxReport('TOTAL_INCOME_AFTER_TAX')]: (
              transaction?.totalIncomeAfterTax ?? 0
            ).toLocaleString(),
          })) ?? [],
      });
    }
  };

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tIncomeTaxReport('INCOME_TAX_REPORT_GENERAL')}
          </Typography>
        </Grid>
      </div>
      <Grid className="flex gap-4">
        <SelectBase
          defaultValue={selectedRangeDateMode}
          backgroundColor="h-8 w-36"
          onValueChange={option => {
            setSearchParams(params => {
              params.delete('date');
              params.set('filterOption', option);

              return params;
            });

            setSelectedRangeDateMode(option);
          }}
          options={Object.values(
            RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
          ).map(rangeDateMode => ({
            label: tIncomeTaxReport(rangeDateMode),
            value: rangeDateMode,
          }))}
        />
        <div className="col-span-5">
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH ? (
            <SingleMonthPicker
              onMonthSelect={value => {
                setSearchParams(params => {
                  params.set('date', endOfMonth(value).toString());

                  return params;
                });
              }}
              defaultMonth={new Date(filteredValue.date)}
            />
          ) : null}
          {selectedRangeDateMode ===
          RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_YEAR ? (
            <SingleYearPicker
              onYearSelect={value => {
                setSearchParams(params => {
                  params.set('date', endOfMonth(value).toString());

                  return params;
                });
              }}
              defaultYear={new Date(filteredValue.date)}
            />
          ) : null}
        </div>
        <Button
          disabled={
            !permissions.includes(
              PERMISSIONS.EXPORT_INCOME_TAX_REPORT_DETAIL,
            ) || !exportingResults?.length
          }
          className="h-8"
          onClick={onExportExcel}>
          {tIncomeTaxReport('EXPORT_EXCEL_FILE')}
        </Button>
      </Grid>
      <Card className="w-36 mt-6">
        <CardContent className="p-2">
          <Typography
            variant="p"
            className="text-gray-800 text-lg font-bold"
            affects="removePMargin">
            {tIncomeTaxReport('TOTAL')}
          </Typography>
          <Typography className="text-green-500" variant="h4" affects="large">
            {exportingResults?.length ?? 0}
          </Typography>
        </CardContent>
      </Card>
    </>
  );
}
