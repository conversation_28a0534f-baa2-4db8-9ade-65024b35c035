{"version": "0.2", "language": "en, vi", "words": ["tasker", "spellcheckerrc"], "ignorePaths": ["node_modules", "dist", "public", "build", "package.json", "Dockerfile", "coverage", "CHANGELOG.md", "\\@", "__tests__", "./seeding-data.sh", "pnpm-lock.yaml", "Dockerfile.staging"], "ignoreRegExpList": ["CStyleComment", "(?![a-zA-Z])\\b[àáảãạâầấẩẫậăằắẳẵặèéẻẽẹêềếểễệđìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵ]+\\b", "/from\\s+(['\"]).*\\1/", "\\btasker\\w*\\b"], "ignoreWords": ["languagedetector", "uuidv4", "b<PERSON><PERSON><PERSON>", "BSON", "Shadcn", "cmdk", "STMP", "<PERSON><PERSON><PERSON>", "Zustand", "<PERSON>", "supportedLngs", "isbot", "renderToPipeableStream", "DONT", "denined", "DENINED", "BNPL", "loggly", "WORKDIR", "TOPUP", "BPAY", "SHOPEE", "ZALO", "MOMO", "VIET", "QRIS", "ISOCODE", "bbay", "Refundb", "BEMPLOYEE", "Midtrans", "<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ngay", "àáảãạâầấẩẫậăằắẳẵặèéẻẽẹêềếểễệđìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵ", "truy", "<PERSON><PERSON><PERSON>", "ngh<PERSON>", "taskers", "Reseted", "reseted", "do<PERSON>h", "indo", "บัญชีธุรกิจ", "ของคุณได้เติมเงินจำนวน", "สำเร็จแล้ว", "<PERSON><PERSON><PERSON>", "bisnis", "<PERSON><PERSON>", "telah", "<PERSON><PERSON><PERSON><PERSON>", "diisi", "<PERSON><PERSON>", "jumlah", "openxmlformats", "officedocument", "spreadsheetml"]}