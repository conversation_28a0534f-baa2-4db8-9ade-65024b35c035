import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useFetcher,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS } from 'btaskee-constants';
import {
  useExportFile,
  useGlobalStore,
  useLoaderDataSafely,
} from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  type TaskerBalanceMonthly,
  exportBalanceMonthlyByTasker,
  getBalanceMonthlyByTasker,
} from '~/services/users-balance-monthly.server';

export const loader = hocLoader(async ({ request }) => {
  const [{ nameRegex, sort }, { month, year, pageSize, pageIndex }] =
    getValuesFromSearchParams(new URL(request.url).searchParams, {
      keysString: ['nameRegex', 'sort'],
      keysNumber: ['month', 'year', 'pageSize', 'pageIndex'],
    });
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const settingCountry = await getSettingCountryByIsoCode({
    isoCode,
    projection: { currency: 1 },
  });

  /**
   * Table will set sort into url search params like sort=name:asc
   * Convert search params before set to query params, from sort=name:asc to sortField=name, sortOrder=asc
   */
  const [sortField, sortOrder] = sort.split(':');
  const limit = pageSize || 10;
  const skip = pageIndex * limit || 0;

  let m = momentTz().month();
  let y = momentTz().year();

  if (!month && year) {
    m = 0;
    y = year;
  } else if (month && year) {
    m = month;
    y = year;
  }

  const usersBalanceMonthly = await getBalanceMonthlyByTasker({
    month: m,
    year: y,
    nameRegex: nameRegex,
    limit,
    skip,
    sortField: sortField || 'name',
    sortOrder: sortOrder === 'asc' ? 'ASC' : 'DESC',
  });

  const isYearFilter = !month && year;

  return json({
    currency: settingCountry?.currency?.sign || '',
    usersBalanceMonthly,
    filters: {
      date: {
        from: isYearFilter
          ? momentTz({ year }).startOf('year').toDate()
          : month
            ? momentTz({ year, month: month - 1 })
                .startOf('month')
                .toDate()
            : momentTz().subtract(1, 'month').startOf('month').toDate(),
        to: isYearFilter
          ? year < momentTz().year()
            ? momentTz({ year }).endOf('year').toDate()
            : momentTz().subtract(1, 'month').endOf('month').toDate()
          : month
            ? momentTz({ year, month: month - 1 })
                .endOf('month')
                .toDate()
            : momentTz().subtract(1, 'month').endOf('month').toDate(),
      },
      mode: isYearFilter ? 'year' : 'month',
    },
    month: m - 1,
    year: y,
  });
}, PERMISSIONS.READ_TASKER_BALANCE); //TODO: add permission

export const action = hocAction(async ({ request }) => {
  const formData = await request.clone().formData();
  const month = formData.get('month');
  const year = formData.get('year');

  const usersBalanceMonthlyExport = await exportBalanceMonthlyByTasker({
    month: Number(month) + 1,
    year: Number(year),
  });

  return json({ usersBalanceMonthlyExport });
}, PERMISSIONS.EXPORT_TASKER_BALANCE); //TODO: add permission

export default function TaskerBalanceScreen() {
  const { t: tTaskerBalance } = useTranslation('tasker-balance');
  const [searchParams] = useSearchParams();

  const { currency, usersBalanceMonthly, filters, month, year } =
    useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);
  const fetchData = useFetcher<ActionTypeWithError<typeof action>>();

  const { exportCsv: exportListTaskAskerBalance } = useExportFile();
  const confirmExport = useConfirm();

  const onExportExcel = useCallback(async () => {
    const isConfirm = await confirmExport({
      title: tTaskerBalance('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {tTaskerBalance('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTaskerBalance('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {fetchData?.data?.usersBalanceMonthlyExport?.length || 0}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTaskerBalance('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {momentTz(filters.date.from).format('DD/MM/YYYY')} -{' '}
              {momentTz(filters.date.to).format('DD/MM/YYYY')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: tTaskerBalance('CANCEL'),
      actionButton: tTaskerBalance('CONFIRM'),
    });

    if (isConfirm) {
      const exportedExcelData = fetchData?.data?.usersBalanceMonthlyExport?.map(
        item => ({
          [tTaskerBalance('ASKER_ID')]: item?.user_id || '',
          [tTaskerBalance('ASKER_NAME')]: item?.name || '',
          [tTaskerBalance('BEGINNING_BALANCE')]:
            item?.opening_balance_main_account || 0,
          [tTaskerBalance('INCREASED_AMOUNT_INCURRED')]:
            item?.increased_main_account || 0,
          [tTaskerBalance('DECREASED_AMOUNT_INCURRED')]:
            item?.decreased_main_account || 0,
          [tTaskerBalance('ENDING_BALANCE')]:
            item?.ending_balance_main_account || 0,
          [tTaskerBalance('INCREASED_PROMOTION_ACCOUNT')]:
            item?.increased_promotion_account || 0,
          [tTaskerBalance('DECREASED_PROMOTION_ACCOUNT')]:
            item?.decreased_promotion_account || 0,
        }),
      );

      const fileName = `Asker Balance ${momentTz(filters.date.from).format('DD/MM/YYYY')} - ${momentTz(filters.date.to).format('DD/MM/YYYY')}`;

      exportListTaskAskerBalance({
        fileName,
        arrayJson: exportedExcelData || [],
      });
    }
  }, [
    confirmExport,
    exportListTaskAskerBalance,
    fetchData?.data?.usersBalanceMonthlyExport,
    filters.date.from,
    filters.date.to,
    tTaskerBalance,
  ]);

  const onClickExportExcel = () => {
    fetchData.submit({ month, year }, { method: 'post' });
  };

  useEffect(() => {
    if (fetchData?.data) {
      onExportExcel();
    }
  }, [fetchData?.data]);

  const columns: ColumnDef<SerializeFrom<TaskerBalanceMonthly>>[] = useMemo(
    () => [
      {
        accessorKey: 'user_id',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('TASKER_ID')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original.user_id || ''}</Typography>
        ),
        enableSorting: false,
        size: 115,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original.name || ''}</Typography>
        ),
        enableSorting: false,
        size: 345,
      },
      {
        accessorKey: 'opening_balance_main_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('BEGINNING_BALANCE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.opening_balance_main_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'increased_main_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('INCREASED_AMOUNT_INCURRED')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(row.original.increased_main_account || 0)}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'decreased_main_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('DECREASED_AMOUNT_INCURRED')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(row.original.decreased_main_account || 0)}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'ending_balance_main_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('ENDING_BALANCE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.ending_balance_main_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'opening_balance_promotion_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('OPENING_PROMOTION_BALANCE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.opening_balance_promotion_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'increased_promotion_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('INCREASED_PROMOTION_ACCOUNT')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.increased_promotion_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'decreased_promotion_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('DECREASED_PROMOTION_ACCOUNT')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.decreased_promotion_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
      {
        accessorKey: 'ending_balance_promotion_account',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerBalance('ENDING_PROMOTION_BALANCE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatNumberWithCommas(
              row.original.ending_balance_promotion_account || 0,
            )}
            {currency || ''}
          </Typography>
        ),
        size: 115,
      },
    ],
    [currency, tTaskerBalance],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tTaskerBalance('TASKER_BALANCE')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        {permissions.includes(PERMISSIONS.EXPORT_TASKER_BALANCE) ? (
          <Button
            className="gap-2 text-sm font-medium"
            variant="default"
            onClick={onClickExportExcel}
            disabled={usersBalanceMonthly?.totals?.totalRows === 0}>
            <Upload className="w-5 h-5" />
            {tTaskerBalance('EXPORT_TO_EXCEL')}
          </Button>
        ) : null}
      </div>
      <BTaskeeTable
        isShowClearButton
        total={usersBalanceMonthly?.totals?.totalRows || 0}
        data={usersBalanceMonthly?.rows || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: usersBalanceMonthly?.totals?.totalRows || 0,
          pageSize: Number(searchParams.get('pageSize') || 10),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="tasker-balance"
        search={{
          defaultValue: searchParams.get('nameRegex') || '',
          name: 'nameRegex',
          placeholder: tTaskerBalance('SEARCH_BY_TASKER_NAME'),
        }}
        filterDate={{
          name: 'monthYearFilter',
          mode: 'month-year',
          selectMode: (filters?.mode as 'year' | 'month') || 'month',
          minDate: momentTz('2015-01-01').startOf('month').toDate(),
          maxDate: momentTz().endOf('month').toDate(),
          defaultValue: {
            from: momentTz(filters.date.from).toDate(),
            to: momentTz(filters.date.to).toDate(),
          },
          variant: {
            chevrons: 'outline',
          },
        }}
        extraContent={
          <div className="w-full overflow-x-auto">
            <div className="flex gap-6 justify-end min-w-max pb-1">
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_BEGINNING_BALANCE')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-blue-50 text-blue-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalOpeningMain || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_INCREASED_AMOUNT_INCURRED')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-green-50 text-green-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalIncreasedMainAccount ||
                        0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_DECREASED_AMOUNT_INCURRED')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-yellow-50 text-yellow-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalDecreasedMainAccount ||
                        0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_ENDING_BALANCE')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-primary-50 text-primary rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalEndingMain || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_BEGINNING_PROMOTION_BALANCE')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-blue-50 text-blue-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalOpeningPromotion || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_INCREASED_PROMOTION_ACCOUNT')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-green-50 text-green-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals
                        ?.totalIncreasedPromotionAccount || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography variant="p" className="text-gray-400 text-xs">
                  {tTaskerBalance('TOTAL_DECREASED_PROMOTION_ACCOUNT')}
                </Typography>
                <Badge className="min-w-[160px] mx-[26px] bg-yellow-50 text-yellow-500 rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals
                        ?.totalDecreasedPromotionAccount || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
              <div className="inline-flex flex-col items-center gap-1">
                <Typography
                  variant="p"
                  className="text-gray-400 text-xs -mr-[26px]">
                  {tTaskerBalance('TOTAL_ENDING_PROMOTION_BALANCE')}
                </Typography>
                <Badge className="min-w-[160px] ml-[26px] bg-primary-50 text-primary rounded-md w-fit py-3">
                  <Typography variant="p" className="mx-auto font-semibold">
                    {formatNumberWithCommas(
                      usersBalanceMonthly?.totals?.totalEndingPromotion || 0,
                    )}
                    {currency || ''}
                  </Typography>
                </Badge>
              </div>
            </div>
          </div>
        }
      />
    </>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
