import { mongoClientBE } from 'mongo-connection';
import { Schema } from 'mongoose';

const PermissionsSchema = new Schema<BtaskeePermissions>(
  {
    _id: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
      required: true,
    },
    description: {
      $type: String,
      required: true,
    },
    module: {
      $type: String,
      required: true,
    },
    key: {
      $type: String,
      unique: true,
      required: true,
    },
    'slug-module': {
      $type: String,
      required: true,
    },
  },
  { typeKey: '$type', collection: 'permissions' },
);

const PermissionsModel = mongoClientBE.model(
  'BtaskeePermissions',
  PermissionsSchema,
);
export default PermissionsModel;
