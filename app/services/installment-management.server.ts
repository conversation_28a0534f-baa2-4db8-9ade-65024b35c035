import { type PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface InstallmentPaymentListParams {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  sort: PipelineStage.Sort['$sort'];
  filter: {
    search: string;
    status: string;
    rangeDate: { from: Date; to: Date };
  };
  isoCode: string;
}

export async function getInstallmentManagement({
  skip,
  limit,
  sort,
  filter,
  isoCode,
}: InstallmentPaymentListParams): Promise<InstallmentManagement[]> {
  const { matcher, searcher } = mapFilterFromClient({ filter });

  /**
   * When don't pass search value, just count documents to get total records
   */
  if (!searcher) {
    const rowData = await getModels(isoCode)
      .taskerBNPLProcess.find(matcher ? matcher : {}, {
        status: 1,
        amount: 1,
        remainingAmount: 1,
        moneyBNPLOnTask: 1,
        firstPayMoney: 1,
        createdAt: 1,
        expiredAt: 1,
        taskerId: 1,
      })
      .sort({ ...sort, _id: 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    if (!rowData.length) return [];

    const taskerDetails = await getModels(isoCode)
      .users.find(
        {
          _id: { $in: rowData.map(row => row.taskerId) },
        },
        {
          _id: 1,
          name: 1,
          phone: 1,
        },
      )
      .lean();
    if (!taskerDetails.length) return [];

    const mapRowDataWithTaskerDetails = rowData.map(row => ({
      ...row,
      tasker: taskerDetails.find(r => r._id === row.taskerId),
    }));

    return getRowDataWithPaidAmount(mapRowDataWithTaskerDetails);
  }

  /**
   * When pass search value is number,
   * search tasker by phone and count documents to get total records
   */
  if (!isNaN(Number(searcher))) {
    const user = await getModels(isoCode)
      .users.findOne({ phone: searcher }, { _id: 1 })
      .lean();
    if (!user) return [];

    const rowData = await getModels(isoCode)
      .taskerBNPLProcess.find(
        matcher ? { ...matcher, taskerId: user._id } : { taskerId: user._id },
        {
          status: 1,
          amount: 1,
          remainingAmount: 1,
          moneyBNPLOnTask: 1,
          firstPayMoney: 1,
          createdAt: 1,
          expiredAt: 1,
          taskerId: 1,
        },
      )
      .sort({ ...sort, _id: 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    if (!rowData.length) return [];

    const taskerDetails = await getModels(isoCode)
      .users.find(
        {
          _id: { $in: rowData.map(row => row.taskerId) },
        },
        {
          _id: 1,
          name: 1,
          phone: 1,
        },
      )
      .lean();
    if (!taskerDetails.length) return [];

    const mapRowDataWithTaskerDetails = rowData.map(row => ({
      ...row,
      tasker: taskerDetails.find(r => r._id === row.taskerId),
    }));

    return getRowDataWithPaidAmount(mapRowDataWithTaskerDetails);
  }

  /**
   * Need lookup users to get taskerId by name (regex)
   */
  const rowData = await getModels(isoCode)
    .taskerBNPLProcess.aggregate([
      ...(matcher
        ? [
            {
              $match: matcher,
            },
          ]
        : []),
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(searcher
        ? [
            {
              $match: {
                'tasker.type': 'TASKER',
                'tasker.name': { $regex: searcher, $options: 'i' },
              },
            },
          ]
        : []),
      { $sort: { ...sort, _id: 1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          tasker: {
            _id: 1,
            name: 1,
            phone: 1,
          },
          status: 1,
          amount: 1,
          remainingAmount: 1,
          moneyBNPLOnTask: 1,
          firstPayMoney: 1,
          createdAt: 1,
          expiredAt: 1,
        },
      },
    ])
    .exec();

  return getRowDataWithPaidAmount(rowData);
}

export async function getTotalAmountInstallmentManagement({
  filter,
  isoCode,
}: Pick<InstallmentPaymentListParams, 'filter' | 'isoCode'>) {
  const { matcher, searcher } = mapFilterFromClient({ filter });

  /**
   * When don't pass search value, just count documents to get total records
   */
  if (!searcher) {
    const result = await getModels(isoCode)
      .taskerBNPLProcess.aggregate([
        ...(matcher
          ? [
              {
                $match: matcher,
              },
            ]
          : []),
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            totalRemainingAmount: { $sum: '$remainingAmount' },
          },
        },
      ])
      .exec();

    return getSummary(result);
  }

  /**
   * When pass search value is number,
   * search tasker by phone and count documents to get total records
   */
  if (!isNaN(Number(searcher))) {
    const user = await getModels(isoCode)
      .users.findOne({ phone: searcher }, { _id: 1 })
      .lean();
    if (!user) return getSummary([]);

    const $match = matcher
      ? { ...matcher, taskerId: user._id }
      : { taskerId: user._id };

    const result = await getModels(isoCode)
      .taskerBNPLProcess.aggregate([
        { $match },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            totalRemainingAmount: { $sum: '$remainingAmount' },
          },
        },
      ])
      .exec();
    return getSummary(result);
  }

  const result = await getModels(isoCode)
    .taskerBNPLProcess.aggregate([
      ...(matcher
        ? [
            {
              $match: matcher,
            },
          ]
        : []),
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'tasker.type': 'TASKER',
          'tasker.name': { $regex: searcher, $options: 'i' },
        },
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalRemainingAmount: { $sum: '$remainingAmount' },
        },
      },
    ])
    .exec();

  return getSummary(result);
}

function getSummary(result: MustBeAny[]) {
  if (!result.length) {
    return {
      totalAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
    };
  }

  const summary = result[0];
  const totalPaidAmount = summary.totalAmount - summary.totalRemainingAmount;

  return {
    ...summary,
    totalPaidAmount,
  };
}

function getRowDataWithPaidAmount(rowData: MustBeAny[]) {
  if (!rowData.length) return [];

  return rowData.map(row => ({
    ...row,
    paidAmount: (row.amount || 0) - (row.remainingAmount || 0),
  }));
}

function mapFilterFromClient({
  filter,
}: Pick<InstallmentPaymentListParams, 'filter'>) {
  const mapMatcher: Map<string, PipelineStage.Match['$match']> = new Map();

  // Move date and status filtering to initial stage
  if (filter.rangeDate) {
    mapMatcher.set('createdAt', {
      $gte: filter.rangeDate.from,
      $lte: filter.rangeDate.to,
    });
  }

  if (
    typeof filter.status === 'string' &&
    filter.status &&
    filter.status.split(',').length
  ) {
    mapMatcher.set('status', { $in: filter.status.split(',') });
  }

  return {
    matcher: mapMatcher.size ? Object.fromEntries(mapMatcher) : null,
    searcher: filter?.search?.trim(),
  };
}

export async function getTotalInstallmentManagement({
  isoCode,
  filter,
}: Pick<InstallmentPaymentListParams, 'isoCode' | 'filter'>) {
  const { matcher, searcher } = mapFilterFromClient({ filter });

  /**
   * When don't pass search value, just count documents to get total records
   */
  if (!searcher) {
    return getModels(isoCode)
      .taskerBNPLProcess.countDocuments(matcher ? matcher : {})
      .lean();
  }

  /**
   * When pass search value is number,
   * search tasker by phone and count documents to get total records
   */
  if (!isNaN(Number(searcher))) {
    const user = await getModels(isoCode)
      .users.findOne({ phone: searcher }, { _id: 1 })
      .lean();
    if (!user) return 0;

    return getModels(isoCode)
      .taskerBNPLProcess.countDocuments({
        ...(matcher ? matcher : {}),
        taskerId: user._id,
      })
      .lean();
  }

  /**
   * Need lookup users to get taskerId by name (regex)
   */
  const result = await getModels(isoCode)
    .taskerBNPLProcess.aggregate([
      ...(matcher
        ? [
            {
              $match: matcher,
            },
          ]
        : []),
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'taskerId',
          foreignField: '_id',
          as: 'tasker',
        },
      },
      {
        $unwind: {
          path: '$tasker',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'tasker.type': 'TASKER',
          'tasker.name': { $regex: filter.search, $options: 'i' },
        },
      },
      { $count: 'total' },
    ])
    .exec();

  return result?.[0]?.total || 0;
}
