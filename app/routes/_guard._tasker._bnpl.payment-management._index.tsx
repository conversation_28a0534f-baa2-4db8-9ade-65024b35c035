import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Label,
  StatusBadge,
  Typography,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletPaymentManagement } from '~/hooks/usePaymentManagement';
import type { getPaymentToolKitTransaction } from '~/services/payment-management.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function PaymentManagementIndex() {
  const { t } = useTranslation('payment-management');
  const [searchParams] = useSearchParams();

  const outletData = useOutletPaymentManagement();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getPaymentToolKitTransaction>['data'][0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'tasker.name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.name}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.phone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.phone}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'region',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('REGION')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.city}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'type',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TYPE')} />
        ),
        cell: ({ row }) => (
          <StatusBadge
            status={row.original?.type}
            statusClasses={{
              ONCE_PAY: 'bg-primary-50 text-primary rounded-md text-center',
              BNPL: 'bg-blue-50 text-blue-500 rounded-md text-center',
            }}
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('AMOUNT')} />
        ),
        cell: ({ row }) => (
          <div>
            {formatNumberWithCommas(row.original?.amount || 0) +
              (outletData.settingCountry?.currency?.sign || '')}
          </div>
        ),
      },
      {
        accessorKey: 'paymentDate',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PAYMENT_DATE')} />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p" className="whitespace-nowrap">
              {format(row.original.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
        enableSorting: false,
      },
      {
        accessorKey: 'payment.method',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PAYMENT_METHOD')} />
        ),
        cell: ({ row }) => <div>{t(row.original?.payment?.method)}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'transactionId',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TRANSACTION_ID')} />
        ),
        cell: ({ row }) =>
          row?.original?.bankTransactionId ? (
            <Typography
              variant="p"
              affects="removePMargin"
              className="whitespace-nowrap">
              {row.original.bankTransactionId}
            </Typography>
          ) : null,
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">{t('PAYMENT_MANAGEMENT')}</Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={outletData?.total || 0}
        data={outletData?.paymentToolkitTransaction?.data || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH_TASKER_PHONE_CODE'),
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(outletData?.filterValue?.rangeDate?.from).toDate(),
            to: momentTz(outletData?.filterValue?.rangeDate?.to).toDate(),
          },
        }}
        localeAddress="payment-management"
        filters={[
          {
            placeholder: t('TYPE'),
            name: 'type',
            options: [
              { label: t('ONCE_PAY'), value: 'ONCE_PAY' },
              { label: t('BNPL'), value: 'BNPL' },
            ],
            value: outletData?.filterValue?.type,
          },
          {
            placeholder: t('PAYMENT_METHOD'),
            name: 'paymentMethod',
            options: outletData?.LIST_TRANSACTION_PAYMENT_BY_ISOCODE?.map(
              payment => ({
                label: t(payment.name),
                value: payment.value,
              }),
            ),
            value: outletData?.filterValue?.paymentMethod,
          },
          {
            placeholder: t('REGION'),
            name: 'city',
            options: outletData?.cities?.map?.(city => ({
              label: city,
              value: city,
            })),
            value: outletData?.filterValue?.city,
          },
        ]}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_ALL_VALUE')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(
                  outletData?.paymentToolkitTransaction?.totalAmount || 0,
                )}${outletData?.settingCountry?.currency?.sign || ''}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
