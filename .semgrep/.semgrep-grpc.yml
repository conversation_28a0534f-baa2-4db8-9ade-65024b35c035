rules:

  - id: grpc-client-insecure-connection
    metadata:
      cwe:
        - "CWE-300: Channel Accessible by Non-Endpoint"
      references:
        - https://blog.gopheracademy.com/advent-2019/go-grps-and-tls/#connection-without-encryption
      category: security
      technology:
        - grpc
      confidence: HIGH
      owasp:
        - A07:2021 - Identification and Authentication Failures
      subcategory:
        - audit
      likelihood: LOW
      impact: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Other
    message: "Found an insecure gRPC connection using 'grpc.WithInsecure()'. This
      creates a connection without encryption to a gRPC server. A malicious
      attacker could tamper with the gRPC message, which could compromise the
      machine. Instead, establish a secure connection with an SSL certificate
      using the 'grpc.WithTransportCredentials()' function. You can create a
      create credentials using a 'tls.Config{}' struct with
      'credentials.NewTLS()'. The final fix looks like this:
      'grpc.WithTransportCredentials(credentials.NewTLS(<config>))'."
    languages:
      - go
    severity: ERROR
    pattern: $GRPC.Dial($ADDR, ..., $GRPC.WithInsecure(...), ...)
    fix-regex:
      regex: (.*)WithInsecure\(.*?\)
      replacement: \1WithTransportCredentials(credentials.NewTLS(<your_tls_config_here>))

  - id: grpc-server-insecure-connection
    metadata:
      cwe:
        - "CWE-300: Channel Accessible by Non-Endpoint"
      references:
        - https://blog.gopheracademy.com/advent-2019/go-grps-and-tls/#connection-without-encryption
      category: security
      technology:
        - grpc
      confidence: HIGH
      owasp:
        - A07:2021 - Identification and Authentication Failures
      subcategory:
        - audit
      likelihood: LOW
      impact: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Other
    message: Found an insecure gRPC server without 'grpc.Creds()' or options with
      credentials. This allows for a connection without encryption to this
      server. A malicious attacker could tamper with the gRPC message, which
      could compromise the machine. Include credentials derived from an SSL
      certificate in order to create a secure gRPC connection. You can create
      credentials using 'credentials.NewServerTLSFromFile("cert.pem",
      "cert.key")'.
    languages:
      - go
    severity: ERROR
    mode: taint
    pattern-sinks:
      - requires: OPTIONS and not CREDS
        pattern: grpc.NewServer($OPT, ...)
      - requires: EMPTY_CONSTRUCTOR
        pattern: grpc.NewServer()
    pattern-sources:
      - label: OPTIONS
        pattern: grpc.ServerOption{ ... }
      - label: CREDS
        pattern: grpc.Creds(...)
      - label: EMPTY_CONSTRUCTOR
        pattern: grpc.NewServer()
