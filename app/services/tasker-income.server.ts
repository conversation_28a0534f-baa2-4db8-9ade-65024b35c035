import { getClient } from '~/third-party/pg-connect.server';

interface TaskerIncomeParams {
  month?: number;
  year: number;
  taskId?: string;
  sortField: string;
  sortOrder: string;
  limit: number;
  skip: number;
}

export interface FactTaskerIncome {
  date: string;
  task_id: string;
  service_name: string;
  task_cost: number;
  exclude_cost: number;
  number_of_taskers: number;
  main_income: number;
  promotion_income: number;
  promotion: number;
  payment_method: string;
}

interface TaskerIncomeSummary {
  totalRows: number;
  totalAmount: number;
}

export interface TaskerIncomeMonthly {
  text: string;
  date: string;
  user_id: string;
  amount: number;
  transaction_id: string;
}

export async function getTaskerIncomeMonthly({
  month,
  year,
  taskId,
  sortField,
  sortOrder,
  limit,
  skip,
}: TaskerIncomeParams): Promise<{
  rows: Array<TaskerIncomeMonthly>;
  totals: TaskerIncomeSummary;
}> {
  const pgClient = await getClient();

  try {
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    const query = `
        WITH filtered_results AS (
          SELECT
              s.date_id,
              s.transaction_id,
              s.user_id,
              s.amount,
              s.text,
              d.id,
              d.date as date
          FROM
            fact_tasker_income s
          JOIN
              dim_date_tasker_income d ON s.date_id = d.id
          WHERE
            d.year = $1
            AND ($2::INTEGER IS NULL OR d.month = $2)
            AND ($3::TEXT IS NULL OR s.transaction_id = $3)
        )
        SELECT
            date,
            transaction_id,
            user_id,
            amount,
            text,
            COUNT(*) OVER () AS total_rows,
            SUM(amount) OVER () AS total_amount
        FROM filtered_results
        ORDER BY
            ${sortField} ${validSortOrder}
        LIMIT $4 OFFSET $5;
    `;

    const res = await pgClient.query(query, [
      year,
      month || null,
      taskId?.trim() || null,
      limit,
      skip,
    ]);
    if (!res.rows.length) {
      return {
        rows: [],
        totals: {
          totalRows: 0,
          totalAmount: 0,
        },
      };
    }

    return {
      totals: {
        totalRows: res.rows[0].total_rows,
        totalAmount: res.rows[0].total_amount,
      },
      rows: res.rows,
    };
  } finally {
    pgClient.release();
  }
}

export async function exportTaskerIncomeMonthly({
  month,
  year,
}: Pick<TaskerIncomeParams, 'month' | 'year'>): Promise<
  Array<TaskerIncomeSummary & TaskerIncomeMonthly>
> {
  const pgClient = await getClient();

  try {
    const query = `
      SELECT
          d.date as date,
          s.transaction_id,
          s.user_id,
          s.amount,
          s.text,
          d.id
      FROM
          fact_tasker_income s
      JOIN
          dim_date_tasker_income d ON s.date_id = d.id
      WHERE
          d.year = $1
          AND ($2::INTEGER IS NULL OR d.month = $2)
`;

    const res = await pgClient.query(query, [year, month || null]);

    return res.rows;
  } finally {
    pgClient.release();
  }
}
