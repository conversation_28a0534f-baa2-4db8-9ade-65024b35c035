import { getClient } from '~/third-party/pg-connect.server';

interface BTaskeeIncomeParams {
  month?: number;
  year: number;
  taskId?: string;
  sortField: string;
  sortOrder: string;
  limit: number;
  skip: number;
}

export interface FactBTaskeeIncome {
  date: string;
  task_id: string;
  service_name: string;
  task_cost: number;
  exclude_cost: number;
  number_of_taskers: number;
  main_income: number;
  promotion_income: number;
  promotion: number;
  payment_method: string;
}

interface BTaskeeIncomeSummary {
  totalRows: number;
  totalTaskCost: number;
  totalMainIncome: number;
  totalPromotionIncome: number;
  totalPromotion: number;
}

export async function getBTaskeeIncomeMonthly({
  month,
  year,
  taskId,
  sortField,
  sortOrder,
  limit,
  skip,
}: BTaskeeIncomeParams): Promise<{
  rows: Array<FactBTaskeeIncome>;
  totals: BTaskeeIncomeSummary;
}> {
  const pgClient = await getClient();

  try {
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    const query = `
        WITH filtered_results AS (
          SELECT
              s.date_id,
              s.task_id,
              s.service_name,
              s.task_cost,
              s.exclude_cost,
              s.number_of_taskers,
              s.main_income,
              s.promotion_income,
              s.promotion,
              s.payment_method,
              d.id,
              d.date as date
          FROM
            fact_btaskee_income s
          JOIN
              dim_date_btaskee_income d ON s.date_id = d.id
          WHERE
            d.year = $1
            AND ($2::INTEGER IS NULL OR d.month = $2)
            AND ($3::TEXT IS NULL OR s.task_id = $3)
        )
        SELECT
            date,
            task_id,
            service_name,
            task_cost,
            exclude_cost,
            number_of_taskers,
            main_income,
            promotion_income,
            promotion,
            payment_method,
            COUNT(*) OVER () AS total_rows,
            SUM(task_cost) OVER () AS total_task_cost,
            SUM(main_income) OVER () AS total_main_income,
            SUM(promotion_income) OVER () AS total_promotion_income,
            SUM(promotion) OVER () AS total_promotion
        FROM filtered_results
        ORDER BY
            ${sortField} ${validSortOrder}
        LIMIT $4 OFFSET $5;
    `;

    const res = await pgClient.query(query, [
      year,
      month || null,
      taskId?.trim() || null,
      limit,
      skip,
    ]);
    if (!res.rows.length) {
      return {
        rows: [],
        totals: {
          totalRows: 0,
          totalTaskCost: 0,
          totalMainIncome: 0,
          totalPromotionIncome: 0,
          totalPromotion: 0,
        },
      };
    }

    return {
      totals: {
        totalRows: res.rows[0].total_rows,
        totalTaskCost: res.rows[0].total_task_cost,
        totalMainIncome: res.rows[0].total_main_income,
        totalPromotionIncome: res.rows[0].total_promotion_income,
        totalPromotion: res.rows[0].total_promotion,
      },
      rows: res.rows,
    };
  } finally {
    pgClient.release();
  }
}

export async function exportBalanceMonthlyByTasker({
  month,
  year,
}: Pick<BTaskeeIncomeParams, 'month' | 'year'>): Promise<
  Array<BTaskeeIncomeSummary>
> {
  const pgClient = await getClient();

  try {
    const query = `
      SELECT
          d.date as date,
          s.task_id,
          s.service_name,
          s.task_cost,
          s.exclude_cost,
          s.number_of_taskers,
          s.main_income,
          s.promotion_income,
          s.promotion,
          s.payment_method,
          d.id
      FROM
          fact_btaskee_income s
      JOIN
          dim_date_btaskee_income d ON s.date_id = d.id
      WHERE
          d.year = $1
          AND ($2::INTEGER IS NULL OR d.month = $2)
`;

    const res = await pgClient.query(query, [year, month || null]);

    return res.rows;
  } finally {
    pgClient.release();
  }
}
