import { type SerializeFrom, json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  BNPL_TRACKING_DAILY_TRANSACTION_REASON,
  CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL,
  PERMISSIONS,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Label,
  Typography,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getCitiesByUserId,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
  getBNPLTrackingDailyTransactions,
  getTotalBNPLTrackingDailyTransaction,
  getTotalDetectedAmountBNPLTrackingDailyTransaction,
} from '~/services/tracking-daily-transaction.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const { isoCode, userId, isSuperUser } = await getUserSession(request);
  const isManager = await verifyManager(userId);

  const [
    { sort: sortString, rangeDate, search, reason, city },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(url.searchParams, {
    keysString: ['sort', 'rangeDate', 'search', 'reason', 'city'],
    keysNumber: ['pageSize', 'pageIndex'],
  });

  const filterTransaction = {
    // TODO: need refactor name of DEFAULT_RANGE_DATE_CURRENT_DAY, because default is current month now
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(
      rangeDate ||
        JSON.stringify({
          from: momentTz().startOf('date'),
          to: momentTz().endOf('date'),
        }),
    ),
    search,
    filter: {
      reason,
      city,
    },
  };

  const total = await getTotalBNPLTrackingDailyTransaction({
    ...filterTransaction,
    isoCode,
  });
  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: pageSize || 10,
      pageIndex: pageIndex || 0,
    }),
  );
  const [totalDeductedAmount, transactions, cities, settingCountry] =
    await Promise.all([
      getTotalDetectedAmountBNPLTrackingDailyTransaction({
        ...filterTransaction,
        isoCode,
      }),
      getBNPLTrackingDailyTransactions({
        ...filterTransaction,
        isoCode,
        sort: convertSortString({
          sortString,
          defaultValue: {
            createdAt: -1,
          },
        }),
        limit,
        skip,
      }),
      getCitiesByUserId({ userId, isManager: isSuperUser || isManager }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

  return json({
    transactions,
    total,
    totalDeductedAmount,
    filterTransaction,
    cities,
    settingCountry,
  });
}, PERMISSIONS.BNPL_TRACKING_DAILY_TRANSACTION);

export default function BNPLTrackingDailyTransactionIndex() {
  const { t } = useTranslation('bnpl-tracking-daily-transaction');
  const [searchParams] = useSearchParams();
  const {
    transactions,
    settingCountry,
    filterTransaction,
    total,
    cities,
    totalDeductedAmount,
  } = useLoaderDataSafely<typeof loader>();

  const ReasonBySourceTransaction = ({
    source,
  }: {
    source: ReturnValueIgnorePromise<
      typeof getBNPLTrackingDailyTransactions
    >[0]['source'];
  }) => {
    if (
      source?.reason === CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL &&
      source?.name === BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL &&
      !source?.taskId
    ) {
      return (
        <Typography variant="p">
          {t(
            BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE,
          )}
        </Typography>
      );
    }

    if (
      source?.name === BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL &&
      source?.taskId &&
      !source?.reason
    ) {
      return (
        <Typography variant="p">
          {t(BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL)}
        </Typography>
      );
    }

    return <Typography variant="p">{t('INVALID_REASON')}</Typography>;
  };

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getBNPLTrackingDailyTransactions>[0]
    >
  >[] = [
    {
      accessorKey: 'taskerFullName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TASKER_FULL_NAME')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.taskerInfo?.name}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'phoneNumber',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('PHONE_NUMBER')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.taskerInfo?.phone}</Typography>
      ),
      enableSorting: false,
    },
    {
      // regions is user's workingPlaces, i'd changed name it to show on UI
      accessorKey: 'regions',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="whitespace-nowrap"
          column={column}
          title={t('REGIONS')}
        />
      ),
      cell: ({ row }) =>
        Array.from(
          new Set(
            row.original?.taskerInfo?.workingPlaces?.map(
              region => region?.city,
            ) || [],
          ),
        ).join(', '),
      enableSorting: false,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('DEDUCTED_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${row.original?.amount.toLocaleString()}${settingCountry?.currency?.sign}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
      ),
      cell: ({ row }) =>
        row.original?.createdAt ? (
          <Typography variant="p">
            {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
          </Typography>
        ) : null,
      enableSorting: false,
    },
    {
      accessorKey: 'reason',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('REASON')} />
      ),
      cell: ({ row }) => (
        <ReasonBySourceTransaction source={row.original?.source} />
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'taskId',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={t('TRANSACTION_ID_OR_TASK_ID')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.source?.taskId}</Typography>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="mb-6 rounded-md bg-secondary p-4">
        <div className="grid space-y-2">
          <Typography variant="h2">
            {t('TRACKING_DAILY_TRANSACTION')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        columns={columns}
        data={transactions || []}
        total={total || 0}
        localeAddress="bnpl-tracking-daily-transaction"
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize')) || 0,
          pageIndex: Number(searchParams.get('pageIndex')) || 0,
        })}
        search={{
          placeholder: t('ENTER_NAME_OR_PHONE'),
          name: 'search',
          defaultValue: filterTransaction?.search || '',
        }}
        filterDate={{
          name: 'rangeDate',
          defaultValue: {
            from: momentTz(filterTransaction?.rangeDate?.from).toDate(),
            to: momentTz(filterTransaction?.rangeDate?.to).toDate(),
          },
        }}
        filters={[
          {
            placeholder: t('REASON'),
            name: 'reason',
            options: Object.values(BNPL_TRACKING_DAILY_TRANSACTION_REASON).map(
              value => ({
                label: t(value),
                value,
              }),
            ),
            value: filterTransaction?.filter?.reason || '',
          },
          {
            placeholder: t('REGIONS'),
            name: 'city',
            options:
              cities?.map(city => ({
                label: city,
                value: city,
              })) || [],
            value: filterTransaction?.filter?.city || '',
          },
        ]}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_DEDUCTED')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(
                  totalDeductedAmount || 0,
                )}${settingCountry?.currency?.sign || ''}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
