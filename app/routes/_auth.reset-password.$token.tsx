import { zodResolver } from '@hookform/resolvers/zod';
import { type LoaderFunctionArgs, redirect } from '@remix-run/node';
import { useActionData, useNavigation, useSubmit } from '@remix-run/react';
import {
  ERROR,
  ROUTE_NAME,
  VALIDATION_ACCOUNT_PASSWORD,
} from 'btaskee-constants';
import {
  Button,
  ErrorMessageBase,
  Form,
  Label,
  PasswordInput,
  Typography,
  toast,
} from 'btaskee-ui';
import { isStrengthPassword } from 'btaskee-utils';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction } from '~/hoc/remix';
import { changePassword, isResetPassExpired } from '~/services/auth.server';

export const action = hocAction(async ({ request, params }) => {
  const formData = await request.clone().formData();
  const newPassword = formData.get('newPassword')?.toString() || '';
  const reEnterPassword = formData.get('reEnterPassword')?.toString() || '';

  if (typeof newPassword !== 'string' || typeof reEnterPassword !== 'string') {
    throw new Error(ERROR.UNKNOWN_ERROR);
  }
  if (newPassword !== reEnterPassword) {
    throw new Error(ERROR.PASSWORD_NOT_MATCH);
  }

  await changePassword({
    newPassword,
    token: params.token || '',
  });

  return redirect(ROUTE_NAME.SIGN_IN);
});

export async function loader({ params }: LoaderFunctionArgs) {
  const isExpired = await isResetPassExpired({ token: params.token || '' });
  if (isExpired) return redirect(ROUTE_NAME.RESET_PASSWORD_FOR_EXPIRED_TOKEN);

  return null;
}

export default function Screen() {
  const { t } = useTranslation('authentication');
  const { state } = useNavigation();
  const submit = useSubmit();

  const formSchema = useMemo(
    () =>
      z.object({
        newPassword: z
          .string()
          .min(1, t('THIS_FIELD_IS_REQUIRED'))
          .refine(
            val =>
              val.length >= VALIDATION_ACCOUNT_PASSWORD.MIN_LENGTH &&
              isStrengthPassword(val),
            {
              message: t('PASSWORD_NOT_SAFE'),
            },
          ),
        reEnterPassword: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
      }),
    [t],
  );

  const formAddingService = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    const formData = new FormData();

    formData.append('newPassword', data.newPassword);
    formData.append('reEnterPassword', data.reEnterPassword);

    submit(formData, { method: 'post' });
  };

  return (
    <>
      <div className="flex flex-col space-y-1 text-start">
        <Typography variant={'h3'}>{t('CREATE_NEW_PASSWORD')}</Typography>
        <Typography variant="p">{t('OUR_PASSWORD_STANDARD')}</Typography>
        <ol className="list-disc ml-4">
          <li>
            <Typography variant="p">
              {t('NEW_PASSWORD_MUST_DIFFERENT_PREVIOUS_USED_PASSWORD')}
            </Typography>
          </li>
          <li>
            <Typography variant="p">
              {t('PASSWORD_MIN_LENGTH_VALIDATION')}
            </Typography>
          </li>
          <li>
            <Typography variant="p">
              {t('PASSWORD_CHARACTER_VALIDATION')}
            </Typography>
          </li>
        </ol>
      </div>
      <div className="grid gap-6">
        <Form {...formAddingService}>
          <form onSubmit={formAddingService.handleSubmit(onSubmit)}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label>{t('NEW_PASSWORD')}</Label>
                <PasswordInput
                  maxLength={VALIDATION_ACCOUNT_PASSWORD.MAX_LENGTH}
                  {...formAddingService.register('newPassword', {
                    required: t('THIS_FIELD_IS_REQUIRED'),
                  })}
                  placeholder={t('NEW_PASSWORD')}
                />
                <ErrorMessageBase
                  errors={formAddingService.formState.errors}
                  name="newPassword"
                />
              </div>
              <div className="grid gap-1">
                <Label>{t('CONFIRM_PASSWORD')}</Label>
                <PasswordInput
                  maxLength={VALIDATION_ACCOUNT_PASSWORD.MAX_LENGTH}
                  {...formAddingService.register('reEnterPassword', {
                    required: t('THIS_FIELD_IS_REQUIRED'),
                  })}
                  placeholder={t('CONFIRM_PASSWORD')}
                />
                <ErrorMessageBase
                  errors={formAddingService.formState.errors}
                  name="reEnterPassword"
                />
              </div>
              <Button>
                {state !== 'idle' ? t('LOADING') : t('RESET_PASSWORD')}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
}
