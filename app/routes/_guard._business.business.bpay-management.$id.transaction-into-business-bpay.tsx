import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Label,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getListDepositTransactionIntoBusinessBPayByBusinessId,
  getTotalRecordDepositTransactionIntoBusinessBPayByBusinessId,
} from '~/services/deposit-transaction-into-business-bpay.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const [
    { sort: sortString, createdAt: rangeDate, search, paymentMethod },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(url.searchParams, {
    keysString: ['sort', 'createdAt', 'search', 'paymentMethod'],
    keysNumber: ['pageSize', 'pageIndex'],
  });

  const filterValue = {
    paymentMethod,
    search,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };

  const total =
    await getTotalRecordDepositTransactionIntoBusinessBPayByBusinessId({
      filter: filterValue,
      businessId: params.id || '',
      isoCode,
    });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [depositTransactionIntoBusinessBPay, settingCountry] =
    await Promise.all([
      getListDepositTransactionIntoBusinessBPayByBusinessId({
        filter: filterValue,
        businessId: params.id || '',
        isoCode,
        skip,
        limit,
        sort: convertSortString({
          sortString,
          defaultValue: { createdAt: -1 },
        }),
      }),
      getSettingCountryByIsoCode({
        isoCode,
        projection: { currency: 1 },
      }),
    ]);

  return json({
    total,
    data: depositTransactionIntoBusinessBPay.data || [],
    totalAmount: depositTransactionIntoBusinessBPay.totalAmount,
    filterValue,
    currency: settingCountry?.currency?.sign,
  });
}, PERMISSIONS.READ_DEPOSIT_TRANSACTION_INTO_BUSINESS_BPAY);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function TransactionIntoBusinessBPay() {
  const { t: tTransactionIntoBusinessBPay } = useTranslation(
    'deposit-transaction-into-business-bpay',
  );
  const [searchParams] = useSearchParams();
  const {
    error: loaderError,
    total,
    data,
    totalAmount,
    filterValue,
    currency,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  const columns: ColumnDef<
    ReturnValueIgnorePromise<
      typeof getListDepositTransactionIntoBusinessBPayByBusinessId
    >['data'][0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBusinessBPay('CREATED_AT')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800">
              {row?.original?.createdAt
                ? format(row?.original?.createdAt, 'HH:mm - dd/MM/yyyy')
                : null}
            </Typography>
          );
        },
        size: 440,
      },
      {
        accessorKey: 'member.name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBusinessBPay('MEMBER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original?.member?.name || ''}
          </Typography>
        ),
        size: 440,
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTransactionIntoBusinessBPay('AMOUNT')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {`${formatNumberWithCommas(row.original?.amount || 0)}${currency}`}
          </Typography>
        ),
        size: 440,
      },
    ],
    [currency, tTransactionIntoBusinessBPay],
  );

  return (
    <BTaskeeTable
      isShowClearButton
      columns={columns}
      data={data}
      total={total}
      localeAddress="deposit-transaction-into-business-bpay"
      pagination={getPageSizeAndPageIndex({
        total,
        pageSize: Number(searchParams.get('pageSize') || 0),
        pageIndex: Number(searchParams.get('pageIndex') || 0),
      })}
      filterDate={{
        name: 'createdAt',
        defaultValue: {
          from: momentTz(filterValue?.rangeDate?.from).toDate(),
          to: momentTz(filterValue?.rangeDate?.to).toDate(),
        },
      }}
      search={{
        placeholder: tTransactionIntoBusinessBPay('SEARCH_BY_MEMBER_NAME'),
        defaultValue: filterValue.search || '',
        name: 'search',
      }}
      extraContent={
        <div className="flex gap-6 justify-end">
          <div className="inline-flex flex-col items-center gap-1">
            <Label className="text-sm font-normal text-gray-400">
              {tTransactionIntoBusinessBPay('TOTAL_VALUE')}
            </Label>
            <Badge className="bg-orange-50 text-orange-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
              {`${formatNumberWithCommas(totalAmount || 0)}${currency}`}
            </Badge>
          </div>
        </div>
      }
    />
  );
}
