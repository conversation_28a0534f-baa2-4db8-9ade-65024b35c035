import { getEnvMailServer } from 'btaskee-dotenv';
import nodemailer from 'nodemailer';
import type Mail from 'nodemailer/lib/mailer';
import { sendEmail } from '~/third-party/mail.server';

describe('sendEmail', () => {
  it('Should send email by <PERSON><PERSON> successfully with nodemailer library', async () => {
    const sendEmailSpy: (
      mailOptions: Mail.Options,
      callback: (err: Error | null, info: unknown) => void,
    ) => void = jest.fn();
    const createTransportSpy = jest
      .spyOn(nodemailer, 'createTransport')
      .mockReturnValue({ sendMail: sendEmailSpy } as MustBeAny);

    const mockParams = {
      text: 'text',
      subject: 'subject',
      to: 'to',
      from: 'from',
    };
    await sendEmail(mockParams);

    const dotenv = getEnvMailServer();
    expect(createTransportSpy).toHaveBeenNthCalledWith(1, {
      host: dotenv.MAIL_HOST,
      secure: true,
      auth: {
        user: dotenv.MAIL_SERVER_USERNAME,
        pass: dotenv.MAIL_SERVER_PASSWORD,
      },
    });
    expect(sendEmailSpy).toHaveBeenNthCalledWith(1, mockParams);
  });

  it('Should throw error when send email have problem', async () => {
    jest.spyOn(nodemailer, 'createTransport');
    await expect(
      sendEmail({ text: 'text', subject: 'subject', to: 'to', from: 'from' }),
    ).rejects.toThrow('EMAIL_SERVICE_ERROR');
  });
});
