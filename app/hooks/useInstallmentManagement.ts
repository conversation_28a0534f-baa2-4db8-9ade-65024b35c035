import {
  type LoaderFunctionArgs,
  type SerializeFrom,
  json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  getInstallmentManagement,
  getTotalAmountInstallmentManagement,
  getTotalInstallmentManagement,
} from '~/services/installment-management.server';

export const willBecomeLoader = async ({ request }: LoaderFunctionArgs) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });
  const url = new URL(request.url);

  const rangeDate =
    url.searchParams.get('createdAt') ||
    JSON.stringify({
      from: momentTz().subtract(7, 'days').startOf('day'),
      to: momentTz().endOf('day'),
    });

  const [{ search, status, sort }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['search', 'status', 'sort'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filterValue = {
    search,
    status,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };

  const total = await getTotalInstallmentManagement({
    isoCode,
    filter: filterValue,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [installmentPayment, settingCountry, totals] = await Promise.all([
    getInstallmentManagement({
      isoCode,
      filter: filterValue,
      limit,
      skip,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { createdAt: -1 },
      }),
    }),
    getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    getTotalAmountInstallmentManagement({ filter: filterValue, isoCode }),
  ]);

  return json({
    installmentPayment,
    total,
    settingCountry,
    filterValue,
    totals,
  });
};

export const useOutletInstallmentManagement = () => {
  return useOutletContext<SerializeFrom<typeof willBecomeLoader>>();
};

export const useLoaderInstallmentManagement = () => {
  return useLoaderDataSafely<typeof willBecomeLoader>();
};
