import bcrypt from 'bcryptjs';
import { getEnvBcrypt, getEnvMailServer } from 'btaskee-dotenv';
import { momentTz } from 'btaskee-utils';
import {
  changePassword,
  getUserByUserId,
  isResetPassExpired,
  isVerificationCodeExpired,
  resetPassword,
  updateUser,
  verifyAndSendCode,
} from '~/services/auth.server';
import UsersModel from '~/services/model/users.server';
import { setupPasswordForExpiredLinkOfNewUser } from '~/services/settings.server';
import * as EmailService from '~/third-party/mail.server';

jest.mock('~/third-party/mail.server', () => ({
  __esModule: true,
  sendEmail: jest.fn(),
}));

jest.mock('~/services/utils.server', () => ({
  __esModule: true,
  sendSetupPasswordEmailForUser: jest.fn(),
}));

describe('Authentication', () => {
  const dotenv = { ...getEnvBcrypt(), ...getEnvMailServer() };

  describe('verifyAndSendCode & sendVerificationCode', () => {
    const mockUser = {
      _id: 'fake-id',
      username: 'fake-user',
      email: '<EMAIL>',
      createdAt: new Date('2024-02-01'),
      status: 'ACTIVE',
      cities: ['Hồ Chí Minh'],
      services: {
        password: {
          bcrypt: 'testing',
        },
      },
      isoCode: 'VN',
      language: 'vi',
      name: 'fake-name',
    };
    beforeEach(async () => {
      await UsersModel.create(mockUser);
    });

    afterEach(async () => {
      await UsersModel.deleteOne({ _id: mockUser._id });
    });

    it('Should compare password, generate verify code and send this code into email with nodemailer successfully', async () => {
      const mockVerificationCode = '123456';
      const randomSpy = jest
        .spyOn(Math, 'floor')
        .mockReturnValue(Number(mockVerificationCode));

      const asyncCompare = new Promise<boolean>(resolve => resolve(true));
      // Always return true when decode password
      const bcryptCompareSpy = jest
        .spyOn(bcrypt, 'compare')
        .mockImplementation(() => asyncCompare);
      const mockPassword = '123456';

      await verifyAndSendCode({
        username: mockUser.username,
        password: mockPassword,
      });

      expect(bcryptCompareSpy).toHaveBeenNthCalledWith(
        1,
        dotenv.BCRYPT_PLAIN_TEXT + mockPassword,
        mockUser.services.password.bcrypt,
      );
      expect(EmailService.sendEmail).toHaveBeenCalled();

      randomSpy.mockRestore();
      bcryptCompareSpy.mockRestore();
    });

    it('Should throw incorrect account', async () => {
      await expect(
        verifyAndSendCode({
          username: 'notExistedUsername',
          password: '123456',
        }),
      ).rejects.toThrow('INCORRECT_ACCOUNT');
    });
  });

  describe('Change User information', () => {
    const mockUser = {
      _id: 'fake-id',
      username: 'fake-user',
      email: '<EMAIL>',
      createdAt: new Date('2024-02-01'),
      status: 'ACTIVE',
      cities: ['Hồ Chí Minh'],
      services: {
        password: {
          bcrypt: 'testing',
        },
      },
      isoCode: 'VN',
      language: 'vi',
      resetPassword: {
        expired: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow,
        token: 'resetToken',
      },
    };

    beforeEach(async () => {
      await UsersModel.create(mockUser);
    });

    afterEach(async () => {
      await UsersModel.deleteOne({ _id: mockUser._id });
    });

    it('Should return data correctly when update user', async () => {
      const account = await updateUser({
        username: mockUser.username,
        cities: mockUser.cities,
        userId: mockUser._id,
        groupIds: ['1', '2'],
        email: mockUser.email,
        name: 'fake-name',
        status: 'ACTIVE',
      });

      expect(account?._id).toEqual(mockUser._id);
      expect(account?.email).toEqual(mockUser.email);
    });

    it('Should change password successful', async () => {
      const result = await changePassword({
        newPassword: '123456',
        token: mockUser.resetPassword.token,
      });
      expect(result).toEqual(mockUser._id);
    });

    it('Should return data correctly when get user by user id', async () => {
      const account = await getUserByUserId({ userId: mockUser._id });

      expect(account?._id).toEqual(mockUser._id);
      expect(account?.username).toEqual(mockUser.username);
      expect(account?.email).toEqual(mockUser.email);
    });

    it('Should throw correct message when call resetPassword', async () => {
      await expect(resetPassword('wrongEmail')).rejects.toThrow(
        'EMAIL_INCORRECT',
      );
    });

    it('should not reset password if account incorrect', async () => {
      const result = await isResetPassExpired({
        token: mockUser.resetPassword.token,
      });
      expect(result).toBe(false);
    });
  });

  describe('isVerificationCodeExpired & isResetPassExpired', () => {
    const mockUser = {
      _id: 'fake-id',
      username: 'fake-user',
      email: '<EMAIL>',
      createdAt: new Date('2024-02-01'),
      status: 'ACTIVE',
      cities: ['Hồ Chí Minh'],
      services: {
        password: {
          bcrypt: 'testing',
        },
      },
      isoCode: 'VN',
      language: 'vi',
      resetPassword: {
        expired: new Date(Date.now() - 24 * 60 * 60 * 1000),
        token: 'resetToken',
      },
      verification: {
        code: '123456',
        token: 'verificationToken',
        expired: new Date(Date.now() - 24 * 60 * 60 * 1000), //last day
      },
    };

    beforeEach(async () => {
      await UsersModel.create(mockUser);
    });

    afterEach(async () => {
      await UsersModel.deleteOne({ _id: mockUser._id });
    });

    it('isVerificationCodeExpired', async () => {
      const result = await isVerificationCodeExpired({
        token: mockUser.verification.token,
      });
      expect(result).toBe(true);
    });

    it('isResetPassExpired', async () => {
      const result = await isResetPassExpired({
        token: mockUser.resetPassword.token,
      });
      expect(result).toBe(true);
    });
  });
  describe('setupPasswordForExpiredLinkOfNewUser', () => {
    const MOCK_TOKEN = 'mock-token';
    const MOCK_EMAIL = 'mock-email.com';
    const MOCK_USER = {
      _id: 'mock-user-id',
      email: MOCK_EMAIL,
      status: 'ACTIVE',
      username: 'username',
      resetPassword: {
        expired: momentTz().toDate(),
        token: MOCK_TOKEN,
      },
    };

    beforeEach(async () => {
      await UsersModel.create(MOCK_USER);
    });
    afterEach(async () => {
      await UsersModel.deleteOne({ _id: MOCK_USER._id });
    });

    it('Should reset password for expired link successfully', async () => {
      await setupPasswordForExpiredLinkOfNewUser({ email: MOCK_EMAIL });

      const userFound = await UsersModel.findOne({ email: MOCK_EMAIL });

      expect(userFound?.resetPassword?.token).not.toEqual(MOCK_TOKEN);
      expect(userFound?.resetPassword?.expired).not.toEqual(
        MOCK_USER.resetPassword.expired,
      );
    });
    it('Should throw error if system update failed', async () => {
      await expect(
        setupPasswordForExpiredLinkOfNewUser({
          email: 'email-not-found',
        }),
      ).rejects.toThrow();
    });
  });
});
