import {
  Link,
  useNavigate,
  useOutletContext,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Breadcrumbs,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DropdownMenuBase,
  Grid,
  Separator,
  Typography,
} from 'btaskee-ui';
import {
  formatNumberWithCommas,
  getFormattedPhoneNumber,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

type OutletSaleReportData = {
  currency: {
    sign: string;
    code: string;
  };
  formattedReportData: Array<{
    businessId: string;
    businessName: string;
    businessPhone: string;
    totalPaid: number;
    totalPromotionAmount: number;
    totalCollectionFee: number;
  }>;
  filters: {
    rangeDate: {
      from: Date;
      to: Date;
    };
    searchText: string;
    serviceIds: string;
  };
};

export default function SalesReportContent() {
  const { t: tSalesReport } = useTranslation('sales-report');
  const permissions = useGlobalStore(store => store.permissions);
  const outletData = useOutletContext<OutletSaleReportData>();
  const [searchParams] = useSearchParams();
  const [isOpenExportFileDialog, setIsOpenExportFileDialog] =
    useState<boolean>(false);

  const navigate = useNavigate();

  const columns: ColumnDef<OutletSaleReportData['formattedReportData'][0]>[] =
    useMemo(
      () => [
        {
          accessorKey: 'phoneNumber',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('PHONE_NUMBER')}
            />
          ),
          cell: ({ row }) => (
            <Typography variant="p">
              {getFormattedPhoneNumber(row.original?.businessPhone ?? '')}
            </Typography>
          ),
          enableSorting: false,
        },
        {
          accessorKey: 'businessName',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('BUSINESS_NAME')}
            />
          ),
          cell: ({ row }) => (
            <Typography variant="p">
              {row.original?.businessName ?? ''}
            </Typography>
          ),
          enableSorting: false,
        },
        {
          accessorKey: 'totalPaid',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('TOTAL_PAID')}
            />
          ),
          cell: ({ row }) => (
            <Typography variant="p">{`${formatNumberWithCommas(row.original?.totalPaid ?? 0)}${outletData.currency.sign}`}</Typography>
          ),
          enableSorting: false,
        },
        {
          accessorKey: 'totalPromotionAmount',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('TOTAL_PROMOTION_AMOUNT')}
            />
          ),
          cell: ({ row }) => (
            <Typography variant="p">
              {`${formatNumberWithCommas(row.original?.totalPromotionAmount ?? 0)}${outletData.currency.sign}`}
            </Typography>
          ),
          enableSorting: false,
        },
        {
          accessorKey: 'totalCollectionFee',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('TOTAL_COLLECTION_FEE')}
            />
          ),
          cell: ({ row }) => (
            <Typography variant="p">
              {`${formatNumberWithCommas(row.original?.totalCollectionFee ?? 0)}${outletData.currency.sign}`}
            </Typography>
          ),
          enableSorting: false,
        },
        {
          accessorKey: 'action',
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={tSalesReport('ACTION')}
            />
          ),
          cell: ({ row }) => (
            <DropdownMenuBase
              links={[
                {
                  link: `${ROUTE_NAME.SALES_REPORT_IN_ACCOUNTING}/${row.original?.businessId}`,
                  label: tSalesReport('VIEW'),
                },
              ]}
            />
          ),
          size: 44,
          enableSorting: false,
        },
      ],
      [outletData.currency.sign, tSalesReport],
    );

  const searchParamsForExportMemberTransaction = useMemo(() => {
    const params = new URLSearchParams({
      businessIds: outletData?.formattedReportData
        ?.map(report => report?.businessId)
        .join(','),
      rangeDate: JSON.stringify(outletData?.filters?.rangeDate),
      searchText: outletData?.filters?.searchText ?? '',
      serviceIds: outletData?.filters?.serviceIds ?? '',
    });

    return params;
  }, [outletData]);

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{tSalesReport('SALES_REPORT')}</Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={() => setIsOpenExportFileDialog(true)}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_LIST_SALES_REPORT)
          }>
          <Upload className="w-5 h-5" />
          {tSalesReport('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <DataTableBasic
        columns={columns}
        data={outletData?.formattedReportData ?? []}
        searchInput={{
          defaultValue: searchParams.get('searchText') || '',
          name: 'searchText',
          placeholder: tSalesReport('SEARCH_BY_BUSINESS_PHONE'),
        }}
        onClickRow={report =>
          navigate(
            `${ROUTE_NAME.SALES_REPORT_IN_ACCOUNTING}/${report?.businessId}`,
          )
        }
        translationKey="sales-report"
        initialFilterDate={{
          name: 'rangeDate',
          defaultValue: {
            from: momentTz(outletData?.filters?.rangeDate?.from).toDate(),
            to: momentTz(outletData?.filters?.rangeDate?.to).toDate(),
          },
        }}
      />
      <Dialog
        open={isOpenExportFileDialog}
        onOpenChange={() => setIsOpenExportFileDialog(true)}
        defaultOpen={false}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{tSalesReport('EXPORT_TO_EXCEL')}</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            {tSalesReport('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </DialogDescription>
          <Separator />
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpenExportFileDialog(false)}>
              {tSalesReport('CANCEL')}
            </Button>
            <Link
              onClick={() => setIsOpenExportFileDialog(false)}
              className="bg-primary text-white text-sm rounded-md py-2 px-4"
              to={`${ROUTE_NAME.MEMBER_TRANSACTION_RESOURCE_FOR_SALES_REPORT_IN_ACCOUNTING}?${searchParamsForExportMemberTransaction}`}
              download
              reloadDocument>
              {tSalesReport('CONFIRM')}
            </Link>
            <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
