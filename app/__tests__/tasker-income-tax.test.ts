import { SOURCE_NAME_FOR_FA_TRANSACTION, TASK_STATUS } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
  getListTaskerIncomeTaxGeneralReport,
  getTaskIncomeTaxTransactionsByTaskerPhone,
} from '~/services/tasker-income-tax.server';

describe('tasker-income-tax', () => {
  const isoCode = 'VN';

  const MOCK_TASKER_ID = 'mock-tasker-id';
  const MOCK_TASKER_ID_2 = 'mock-tasker-id-2';
  const MOCK_TASK_ID = 'mock-task-id';
  const MOCK_TASK_ID_2 = 'mock-task-id-2';
  const MOCK_TASK_TRANSACTION_ID = 'mock-task-transaction-id';
  const MOCK_INCOME_TAX_TRANSACTION_ID = 'mock-income-tax-transaction-id';
  const MOCK_TASK_TRANSACTION_ID_2 = 'mock-task-transaction-id-2';
  const MOCK_INCOME_TAX_TRANSACTION_ID_2 = 'mock-income-tax-transaction-id-2';

  const mockTasker = {
    _id: MOCK_TASKER_ID,
    name: 'Mock Tasker',
    phone: '**********',
    address: '123 Mock Street, Mock City',
    emails: [{ address: '<EMAIL>' }],
    fAccountId: 'mock-f-account-id',
    language: 'vi',
    status: 'ACTIVE',
    isoCode: 'VN',
    createdAt: momentTz().toDate(),
  };

  const mockTasker2 = {
    _id: MOCK_TASKER_ID_2,
    name: 'Mock Tasker 2',
    phone: '**********',
    address: '456 Mock Avenue, Mock City',
    emails: [{ address: '<EMAIL>' }],
    fAccountId: 'mock-f-account-id-2',
    language: 'vi',
    status: 'ACTIVE',
    isoCode: 'VN',
    createdAt: momentTz().toDate(),
  };

  const mockTask = {
    _id: MOCK_TASK_ID,
    costDetail: { finalCost: 200000 },
    newCostDetail: { finalCost: 250000 },
    status: TASK_STATUS.DONE,
    isoCode: 'VN',
    taskerIncome: {
      income: 160000,
      incomeTax: 16000,
    },
    changesHistory: [
      {
        key: 'DONE_TASK',
        createdAt: momentTz().toDate(),
      },
    ],
    createdAt: momentTz().toDate(),
  };

  const mockTask2 = {
    _id: MOCK_TASK_ID_2,
    costDetail: { finalCost: 300000 },
    status: TASK_STATUS.DONE,
    isoCode: 'VN',
    taskerIncome: {
      income: 240000,
      incomeTax: 24000,
    },
    createdAt: momentTz().toDate(),
  };

  const mockTaskTransaction = {
    _id: MOCK_TASK_TRANSACTION_ID,
    userId: MOCK_TASKER_ID,
    type: 'D',
    amount: 160000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      taskId: MOCK_TASK_ID,
      value: MOCK_TASK_ID,
    },
    createdAt: momentTz().toDate(),
  };

  const mockIncomeTaxTransaction = {
    _id: MOCK_INCOME_TAX_TRANSACTION_ID,
    userId: MOCK_TASKER_ID,
    type: 'C',
    amount: 16000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.INCOME_TAX,
      taskId: MOCK_TASK_ID,
      value: MOCK_TASK_TRANSACTION_ID,
    },
    createdAt: momentTz().toDate(),
  };

  const mockTaskTransaction2 = {
    _id: MOCK_TASK_TRANSACTION_ID_2,
    userId: MOCK_TASKER_ID_2,
    type: 'D',
    amount: 240000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      taskId: MOCK_TASK_ID_2,
      value: MOCK_TASK_ID_2,
    },
    createdAt: momentTz().toDate(),
  };

  const mockIncomeTaxTransaction2 = {
    _id: MOCK_INCOME_TAX_TRANSACTION_ID_2,
    userId: MOCK_TASKER_ID_2,
    type: 'C',
    amount: 24000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.INCOME_TAX,
      taskId: MOCK_TASK_ID_2,
      value: MOCK_TASK_TRANSACTION_ID_2,
    },
    createdAt: momentTz().toDate(),
  };

  const mockReductionTransaction = {
    _id: 'mock-reduction-transaction-id',
    userId: MOCK_TASKER_ID,
    type: 'C',
    amount: 5000,
    source: {
      name: 'TASKER_INCOME_REDUCTION',
      value: 'reduction-reason',
    },
    createdAt: momentTz().toDate(),
  };

  const mockReportTaskerIncomeTax = {
    _id: 'mock-report-tasker-income-tax-id',
    userId: MOCK_TASKER_ID,
    month: momentTz().month() + 1,
    year: momentTz().year(),
    income: 160000,
    reward: 20000,
    incomeReduction: 5000,
    incomeTax: 16000,
    createdAt: momentTz().toDate(),
  };

  const mockReportTaskerIncomeTax2 = {
    _id: 'mock-report-tasker-income-tax-id-2',
    userId: MOCK_TASKER_ID_2,
    month: momentTz().month() + 1,
    year: momentTz().year(),
    income: 240000,
    reward: 30000,
    incomeReduction: 0,
    incomeTax: 24000,
    createdAt: momentTz().toDate(),
  };

  const mockTaskerProfile = {
    _id: 'mock-tasker-profile-id',
    taskerId: MOCK_TASKER_ID,
    taskerName: 'Mock Tasker',
    taskerIdNumber: '*********',
    taskerPhone: '**********',
    appointmentInfo: {
      date: momentTz().toDate(),
      phoneNumber: '**********',
      name: 'Mock Tasker',
      city: 'Ho Chi Minh City',
      address: '123 Mock Street',
    },
    vneID: {
      nationality: 'Vietnam',
      idNumber: '*********',
      idDob: '1990-01-01',
      issueDate: '2020-01-01',
      placeOfIssue: 'Ho Chi Minh City',
      idPhone: '**********',
      placeOfResidence: '123 Mock Street',
      temporaryAddress: '456 Temp Street',
      ethnicGroup: 'Kinh',
    },
    createdAt: momentTz().toDate(),
  };

  const mockTaskerProfile2 = {
    _id: 'mock-tasker-profile-id-2',
    taskerId: MOCK_TASKER_ID_2,
    taskerName: 'Mock Tasker 2',
    taskerIdNumber: '*********',
    taskerPhone: '**********',
    appointmentInfo: {
      date: momentTz().toDate(),
      phoneNumber: '**********',
      name: 'Mock Tasker 2',
      city: 'Ha Noi',
      address: '456 Mock Avenue',
    },
    vneID: {
      nationality: 'Vietnam',
      idNumber: '*********',
      idDob: '1992-02-01',
      issueDate: '2020-02-01',
      placeOfIssue: 'Ha Noi',
      idPhone: '**********',
      placeOfResidence: '456 Mock Avenue',
      temporaryAddress: '789 Temp Avenue',
      ethnicGroup: 'Kinh',
    },
    createdAt: momentTz().toDate(),
  };

  beforeEach(async () => {
    await Promise.all([
      getModels(isoCode).users.insertMany([mockTasker, mockTasker2]),
      getModels(isoCode).task.insertMany([mockTask, mockTask2]),
      getModels(isoCode).FATransaction.insertMany([
        mockTaskTransaction,
        mockIncomeTaxTransaction,
        mockTaskTransaction2,
        mockIncomeTaxTransaction2,
        mockReductionTransaction,
      ]),
      getModels(isoCode).reportTaskerIncomeTax.insertMany([
        mockReportTaskerIncomeTax,
        mockReportTaskerIncomeTax2,
      ]),
    ]);
  });

  afterEach(async () => {
    await getModels(isoCode).users.deleteMany({});
    await getModels(isoCode).task.deleteMany({});
    await getModels(isoCode).FATransaction.deleteMany({});
    await getModels(isoCode).reportTaskerIncomeTax.deleteMany({});
    await getModels(isoCode).taskerProfile.deleteMany({});
  });

  describe('getTaskIncomeTaxTransactionsByTaskerPhone', () => {
    const rangeDate = {
      from: momentTz().subtract(7, 'days').startOf('day').toDate(),
      to: momentTz().add(7, 'days').endOf('day').toDate(),
    };

    it('should return empty object if phone length is less than minimum', async () => {
      const shortPhone = '1';

      const result = await getTaskIncomeTaxTransactionsByTaskerPhone({
        taskerPhone: shortPhone,
        date: rangeDate.from,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toEqual({
        taskerInfo: {
          name: '',
          phone: '',
          status: '',
        },
        reportingTasks: [],
        reportingCompensations: [],
        reportingReductionTransactions: [],
        warningMessage: '',
      });
    });

    it('should return empty object if date is not provided', async () => {
      const result = await getTaskIncomeTaxTransactionsByTaskerPhone({
        taskerPhone: mockTasker.phone,
        date: undefined as MustBeAny,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toEqual({
        taskerInfo: {
          name: '',
          phone: '',
          status: '',
        },
        reportingTasks: [],
        reportingCompensations: [],
        reportingReductionTransactions: [],
        warningMessage: '',
      });
    });

    it('should return empty object if tasker not found', async () => {
      const nonExistentPhone = '0999999999';

      const result = await getTaskIncomeTaxTransactionsByTaskerPhone({
        taskerPhone: nonExistentPhone,
        date: rangeDate.from,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toEqual({
        taskerInfo: {
          name: '',
          phone: '',
          status: '',
        },
        reportingTasks: [],
        reportingCompensations: [],
        reportingReductionTransactions: [],
        warningMessage: '',
      });
    });

    it('should return income tax transactions by tasker phone', async () => {
      const result = await getTaskIncomeTaxTransactionsByTaskerPhone({
        taskerPhone: mockTasker.phone,
        date: rangeDate.from,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toHaveProperty('reportingTasks');
      expect(result).toHaveProperty('reportingCompensations');
      expect(result).toHaveProperty('reportingReductionTransactions');
      expect(Array.isArray(result.reportingTasks)).toBe(true);
      expect(Array.isArray(result.reportingCompensations)).toBe(true);
      expect(Array.isArray(result.reportingReductionTransactions)).toBe(true);
    });

    it('should use costDetail.finalCost when newCostDetail is not available', async () => {
      await getModels(isoCode).task.updateOne(
        { _id: MOCK_TASK_ID_2 },
        { $unset: { newCostDetail: 1 } },
      );

      const result = await getTaskIncomeTaxTransactionsByTaskerPhone({
        taskerPhone: mockTasker2.phone,
        date: rangeDate.from,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toHaveProperty('reportingTasks');
      expect(result).toHaveProperty('reportingCompensations');
      expect(result).toHaveProperty('reportingReductionTransactions');
      expect(Array.isArray(result.reportingTasks)).toBe(true);
      expect(Array.isArray(result.reportingCompensations)).toBe(true);
      expect(Array.isArray(result.reportingReductionTransactions)).toBe(true);
    });
  });

  describe('getListTaskerIncomeTaxGeneralReport', () => {
    const testDate = momentTz().toDate();

    it('should return empty array if date is not provided', async () => {
      const result = await getListTaskerIncomeTaxGeneralReport({
        date: undefined as MustBeAny,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toEqual([]);
    });

    it('should return empty array if filterOption is not provided', async () => {
      const result = await getListTaskerIncomeTaxGeneralReport({
        date: testDate,
        filterOption: undefined as MustBeAny,
        isoCode,
      });

      expect(result).toEqual([]);
    });

    it('should return empty array if no income tax transactions found', async () => {
      await getModels(isoCode).reportTaskerIncomeTax.deleteMany({});

      const result = await getListTaskerIncomeTaxGeneralReport({
        date: testDate,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(result).toEqual([]);
    });

    it('should return general income tax report for all taskers', async () => {
      const result = await getListTaskerIncomeTaxGeneralReport({
        date: testDate,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle taskers with no email', async () => {
      await getModels(isoCode).users.updateOne(
        { _id: MOCK_TASKER_ID },
        { $unset: { emails: 1 } },
      );

      const result = await getListTaskerIncomeTaxGeneralReport({
        date: testDate,
        filterOption: 'RANGE_MONTH',
        isoCode,
      });

      expect(Array.isArray(result)).toBe(true);
    });
  });
});
