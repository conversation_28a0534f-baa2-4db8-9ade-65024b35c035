import type { SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData, useSearchParams } from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  BTaskeeTable,
  Breadcrumbs,
  BreadcrumbsLink,
  DataTableColumnHeader,
  Typography,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, getSkipAndLimit } from 'btaskee-utils';
import { ChevronDown, ChevronUp } from 'lucide-react';
import moment from 'moment';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getActionsHistoryManagedByManagerId,
  getTotalActionsHistoryManageByManagerId,
} from '~/services/settings.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.ACTION_HISTORY_SETTING}
      label="ACTIONS_HISTORY"
    />
  ),
  i18n: 'user-settings',
};

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const searchText = url.searchParams.get('username') || '';

  const { userId: managerId } = await getUserSession({
    headers: request.headers,
  });

  const total = await getTotalActionsHistoryManageByManagerId({
    searchText,
    managerId,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );
  const actionsHistory = await getActionsHistoryManagedByManagerId({
    searchText: url.searchParams.get('username') || '',
    skip,
    limit,
    projection: {
      'user.username': 1,
      action: 1,
      requestFormData: 1,
      createdAt: 1,
    },
    managerId,
  });

  return json({ actionsHistory, total });
}, PERMISSIONS.MANAGER);

export default function ActionHistoryScreen() {
  const { t } = useTranslation('user-settings');
  const [searchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getActionsHistoryManagedByManagerId>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'username',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={t('USERNAME')}
          />
        ),
        cell: ({ row }) => (
          <p className="text-sm font-normal text-gray-400">
            {row.original.user.username}
          </p>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={t('CREATED_DATE')}
          />
        ),
        cell: ({ row }) => (
          <p className="text-sm font-normal text-gray-400">
            {moment(row.original.createdAt)
              .local()
              .format('DD/MM/YYYY HH:mm:ss')}
          </p>
        ),
        size: 80,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => {
          return (
            <p className="text-sm font-normal text-gray-400">
              {row.original.action}
            </p>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'requestFormData',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center text-black"
            column={column}
            title={t('DATA')}
          />
        ),
        cell: ({ row }) => (
          <button
            className="mx-auto flex"
            {...{
              onClick: row.getToggleExpandedHandler(),
              style: { cursor: 'pointer' },
            }}>
            {row.getIsExpanded() ? <ChevronUp /> : <ChevronDown />}
          </button>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      },
    ],
    [t],
  );

  return (
    <>
      <div className="mb-6 grid min-h-[90px] space-y-2 rounded-2xl bg-secondary p-4">
        <Typography variant="h3">{t('ACTIONS_HISTORY')}</Typography>
        <Breadcrumbs />
      </div>
      <BTaskeeTable
        isShowClearButton
        total={loaderData?.total || 0}
        data={loaderData?.actionsHistory || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('username') || '',
          name: 'username',
        }}
        localeAddress="user-settings"
        renderSubComponent={({ row }) => (
          <pre
            style={{
              fontSize: '10px',
              maxWidth: '100%',
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            }}>
            <code>
              {JSON.stringify(row.getValue('requestFormData'), null, 2)}
            </code>
          </pre>
        )}
        getRowCanExpand={() => true}
      />
    </>
  );
}
