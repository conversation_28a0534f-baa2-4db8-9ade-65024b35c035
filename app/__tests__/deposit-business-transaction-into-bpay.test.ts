import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  depositBusinessTransactionIntoBPay,
  getBusinessCityAndBPayInfo,
  getInvoiceCodeDepositBusinessTransactionIntoBPay,
  getListDepositBusinessTransactionIntoBPayByBusinessId,
  getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId,
} from '~/services/deposit-business-transaction-into-bpay.server';

describe('deposit-business-transaction-into-bpay', () => {
  const isoCode = 'VN';

  const mockAskerId = 'mock-asker-id';
  const mockAsker = {
    _id: mockAskerId,
    cities: [{ country: 'VN', city: 'H<PERSON> Chí <PERSON>', district: 'Quận 1' }],
    fAccountId: 'mock-f-account-id',
    language: 'vi',
    status: 'ACTIVE',
    isoCode: 'VN',
    createdAt: momentTz().toDate(),
  };

  const mockBusiness = {
    _id: mockAskerId,
    name: 'mock-business-name',
    bPay: 200000,
    businessSize: 'UNDER_250',
    createdAt: momentTz().toDate(),
    sector: 'INFORMATION_TECHNOLOGY',
    status: 'ACTIVE',
    taxCode: 'mock-tax-code',
    email: 'mock-email',
  };

  const mockDepositBusinessTransactionIntoBPay = {
    _id: 'mock-deposit-business-transaction-into-bpay-id',
    type: BUSINESS_TRANSACTION_TYPE.D,
    name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
    businessId: 'mock-business-id',
    requester: 'mock-requester',
    payment: {
      invoice: 'mock-invoice-number',
      referenceCode: 'mock-reference-code',
      method: 'BANK_TRANSFER', // Current only support bank transfer
    },
    reason: 'mock-reason',
    amount: 100000,
    createdAt: momentTz().toDate(),
  };

  const mockWorkingPlaces = {
    _id: 'mock-working-place-id',
    countryName: 'Việt Nam',
    countryCode: 'VN',
    cities: [{ name: 'Hồ Chí Minh', code: 'HCM', district: ['Quận 1'] }],
  };

  beforeEach(async () => {
    await getModels(isoCode).businessTransaction.insertMany([
      mockDepositBusinessTransactionIntoBPay,
    ]);
    await getModels(isoCode).users.insertMany([mockAsker]);
    await getModels(isoCode).business.insertMany([mockBusiness]);
    await getModels(isoCode).workingPlaces.insertMany([mockWorkingPlaces]);
  });

  afterEach(async () => {
    await getModels(isoCode).businessTransaction.deleteMany({});
    await getModels(isoCode).users.deleteMany({});
    await getModels(isoCode).business.deleteMany({});
    await getModels(isoCode).workingPlaces.deleteMany({});
  });

  describe('getListDepositBusinessTransactionIntoBPayByBusinessId', () => {
    it('should return list of deposit business transaction into BPay by business id', async () => {
      const mockParams = {
        isoCode,
        businessId: 'mock-business-id',
        skip: 0,
        limit: 10,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          paymentMethod: 'BANK_TRANSFER',
          search: 'mock-reference-code',
          rangeDate: {
            from: momentTz().subtract(1, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
      };

      const result =
        await getListDepositBusinessTransactionIntoBPayByBusinessId(mockParams);

      expect(result.data).toHaveLength(1);
      expect(result.data[0].payment.referenceCode).toBe('mock-reference-code');
      expect(result.data[0].payment.method).toBe('BANK_TRANSFER');
      expect(result.totalAmount).toBe(100000);
    });
  });

  describe('getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId', () => {
    it('should return total record of deposit business transaction into BPay by business id', async () => {
      const mockParams = {
        isoCode,
        businessId: 'mock-business-id',
        filter: {
          paymentMethod: 'BANK_TRANSFER',
          search: 'mock-reference-code',
          rangeDate: {
            from: momentTz().subtract(1, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
      };

      const result =
        await getTotalRecordDepositBusinessTransactionIntoBPayByBusinessId(
          mockParams,
        );

      expect(result).toBe(1);
    });
  });

  describe('getBusinessCityAndBPayInfo', () => {
    it('should return business city and BPay info', async () => {
      const result = await getBusinessCityAndBPayInfo({
        isoCode,
        businessId: mockAskerId,
      });

      expect(result.city).toBe('Hồ Chí Minh');
      expect(result.bPay).toBe(200000);
      expect(result.businessName).toBe('mock-business-name');
    });
  });

  describe('getInvoiceCodeDepositBusinessTransactionIntoBPay', () => {
    it('should return next invoice code of deposit business transaction into BPay', async () => {
      const result = await getInvoiceCodeDepositBusinessTransactionIntoBPay({
        isoCode,
      });

      // Test for format: BIS-VN-{alphanumeric}
      expect(result).toMatch(/^BIS-VN-[0-9A-Za-z]+$/);
    });
  });

  describe('depositBusinessTransactionIntoBPay', () => {
    it('should deposit business transaction into BPay', async () => {
      const result = await depositBusinessTransactionIntoBPay({
        isoCode,
        data: {
          businessId: mockAskerId,
          payment: {
            invoice: 'BIS-HCM-0001',
            method: 'BANK_TRANSFER',
            referenceCode: 'mock-reference-code-1',
          },
          amount: 100000,
          reason: 'mock-reason',
          requester: 'mock-requester',
          createdBy: 'mock-created-by',
        },
      });

      const business = await getModels(isoCode).business.findById(mockAskerId);

      expect(result.type).toEqual(BUSINESS_TRANSACTION_TYPE.D);
      expect(result.name).toEqual(
        BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
      );
      expect(result.amount).toEqual(100000);
      expect(result.reason).toEqual('mock-reason');
      expect(result.requester).toEqual('mock-requester');

      expect(business?.bPay).toEqual(mockBusiness.bPay + 100000);
    });

    it('should throw error if duplicate payment reference code', async () => {
      await expect(
        depositBusinessTransactionIntoBPay({
          isoCode,
          data: {
            businessId: mockAskerId,
            payment: {
              invoice: 'BIS-HCM-0001',
              method: 'BANK_TRANSFER',
              referenceCode: 'mock-reference-code',
            },
            amount: 100000,
            reason: 'mock-reason',
            requester: 'mock-requester',
            createdBy: 'mock-created-by',
          },
        }),
      ).rejects.toThrow('DUPLICATE_PAYMENT_REFERENCE_CODE');
    });
  });
});
