import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

interface Filter {
  rangeDate: {
    from: Date;
    to: Date;
  };
  search: string;
  sort?: PipelineStage.Sort['$sort'];
}
interface Result {
  userId: UserApp['_id'];
  createdAt: Date;
  subscriptionId: string;
  start: Date;
  end: Date;
  finalCost: number;
  taskId: string;
  transactionDate: Date | string;
  moneyUsed: number | string;
  moneyRemain: number | string;
  moneyRefundbPay: number | string;
}

export async function getTesterIds({
  isoCode,
}: {
  isoCode: IsoCode;
}): Promise<Array<UserApp['_id']>> {
  const settingSystem = await getModels(isoCode)
    .settingSystem.findOne({}, { tester: 1 })
    .lean();
  const testerIds = await getModels(isoCode)
    .users.find({ phone: { $in: settingSystem?.tester || [] } }, { _id: 1 })
    .exec();
  return testerIds.map(user => user._id);
}

export function getListSubscription({
  isoCode,
  testerIds,
  filter,
}: {
  isoCode: IsoCode;
  testerIds: Array<UserApp['_id']>;
  filter: Filter;
}) {
  const matcher: PipelineStage.Match['$match'] = {};

  if (filter.search) {
    matcher._id = { $regex: filter.search, $options: 'i' };
  }

  return getModels(isoCode)
    .subscription.find(
      {
        $or: [
          { status: { $in: ['DONE', 'PAUSED'] } },
          { status: 'CANCELED', taskIds: { $exists: true } },
        ],
        isoCode: { $ne: 'TH' },
        'schedule.0': { $gte: filter.rangeDate.from, $lt: filter.rangeDate.to },
        userId: { $nin: testerIds },
        ...matcher,
      },
      {
        userId: 1,
        createdAt: 1,
        startDate: 1,
        endDate: 1,
        history: 1,
        'costDetail.finalCost': 1,
        taskIds: 1,
      },
    )
    .exec();
}

export async function getTotalListTaskOfSubscription({
  isoCode,
  testerIds,
  filter,
}: {
  isoCode: IsoCode;
  testerIds: Array<UserApp['_id']>;
  filter: Filter;
}) {
  const subscriptions = await getListSubscription({
    isoCode,
    testerIds,
    filter,
  });

  const taskIds = subscriptions.flatMap(sub => sub.taskIds);

  const taskCount = await getModels(isoCode)
    .task.countDocuments({
      _id: { $in: taskIds },
      date: { $gte: filter.rangeDate.from, $lte: filter.rangeDate.to },
      isoCode: { $ne: 'TH' },
    })
    .lean();

  return taskCount;
}

// Function to get subscriptions based on isoCode, filter, and testIds
export async function getListTaskOfSubscription({
  isoCode,
  filter,
  testIds,
  skip,
  limit,
}: {
  isoCode: IsoCode;
  filter: Filter;
  testIds: Array<UserApp['_id']>;
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
}): Promise<Result[]> {
  const matcher: PipelineStage.Match['$match'] = {};

  if (filter.search) {
    matcher._id = { $regex: filter.search, $options: 'i' };
  }

  const subscriptions = await getListSubscription({
    isoCode,
    testerIds: testIds,
    filter,
  });

  const result: Result[] = [];

  await Promise.all(
    subscriptions.map(async subscription => {
      const createdAt = momentTz(subscription.createdAt).toDate();
      const start = momentTz(subscription.startDate).toDate();
      const end = momentTz(subscription.endDate).toDate();

      if (subscription.taskIds && subscription.taskIds.length > 0) {
        const moneyRemainInitial = subscription?.costDetail?.finalCost || 0;

        // sort with 'date' field
        // TODO: Currently, we can't get data from 2 collections in 1 query with pagination
        const tasks = await getModels(isoCode)
          .task.find(
            {
              _id: { $in: subscription.taskIds },
              date: { $gte: filter.rangeDate.from, $lte: filter.rangeDate.to },
              isoCode: { $ne: 'TH' },
            },
            { status: 1, date: 1, 'costDetail.finalCost': 1 },
            {
              sort: { ...filter.sort, _id: 1 },
              skip,
              limit,
            },
          )
          .exec();

        const taskResults = tasks.map(t => {
          const moneyUsed =
            t.status === 'DONE' ? t?.costDetail?.finalCost || 0 : '-';
          const moneyRefundbPay =
            t.status === 'CANCELED' || t.status === 'EXPIRED'
              ? t?.costDetail?.finalCost || 0
              : '';
          const transactionDate = momentTz(t.date).toDate();
          const moneyRemain =
            moneyRemainInitial - (t?.costDetail?.finalCost || 0);

          return {
            userId: subscription.userId || '-',
            createdAt,
            subscriptionId: subscription._id,
            start,
            end,
            finalCost: subscription?.costDetail?.finalCost || 0,
            taskId: t._id,
            transactionDate,
            moneyUsed,
            moneyRemain,
            moneyRefundbPay,
          };
        });

        result.push(...taskResults);
      } else {
        result.push({
          userId: subscription.userId || '-',
          createdAt,
          subscriptionId: subscription._id,
          start,
          end,
          finalCost: subscription?.costDetail?.finalCost || 0,
          taskId: '-',
          transactionDate: '-',
          moneyUsed: '-',
          moneyRemain: '-',
          moneyRefundbPay: '-',
        });
      }
    }),
  );

  return result;
}
