import {
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  getListDepositTransactionIntoBusinessBPayByBusinessId,
  getTotalRecordDepositTransactionIntoBusinessBPayByBusinessId,
} from '~/services/deposit-transaction-into-business-bpay.server';

describe('deposit-transaction-into-business-bpay', () => {
  const isoCode = 'VN';

  const mockAskerId = 'mock-asker-id';
  const mockBusinessMemberId = 'mock-business-member-id';
  const mockAsker = {
    _id: mockAskerId,
    name: 'mock-asker-name',
    cities: [{ country: 'VN', city: 'Hồ Chí Minh', district: 'Quận 1' }],
    fAccountId: 'mock-f-account-id',
    language: 'vi',
    status: 'ACTIVE',
    isoCode: 'VN',
    createdAt: momentTz().toDate(),
  };

  const mockDepositBusinessTransactionIntoBPay = {
    _id: 'mock-deposit-business-transaction-into-bpay-id',
    type: BUSINESS_TRANSACTION_TYPE.C,
    name: BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_MEMBER,
    businessId: 'mock-business-id',
    memberId: mockBusinessMemberId,
    requester: 'mock-requester',
    amount: 100000,
    createdAt: momentTz().toDate(),
  };

  const mockBusinessMember = {
    _id: mockBusinessMemberId,
    businessId: 'mock-business-id',
    userId: mockAskerId,
    status: 'ACTIVE',
    bPay: 100000,
    phone: '090xxxxxxx',
    levelId: 'mock-level-id',
  };

  beforeEach(async () => {
    await getModels(isoCode).businessTransaction.insertMany([
      mockDepositBusinessTransactionIntoBPay,
    ]);
    await getModels(isoCode).users.insertMany([mockAsker]);
    await getModels(isoCode).businessMember.insertMany([mockBusinessMember]);
  });

  afterEach(async () => {
    await getModels(isoCode).businessTransaction.deleteMany();
    await getModels(isoCode).users.deleteMany();
    await getModels(isoCode).businessMember.deleteMany();
  });

  describe('getListDepositTransactionIntoBusinessBPayByBusinessId', () => {
    it('should return list deposit transaction into business bpay by business id', async () => {
      const mockParams = {
        isoCode,
        skip: 0,
        limit: 10,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          search: 'mock-asker-name',
          rangeDate: {
            from: momentTz().subtract(1, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
        businessId: mockDepositBusinessTransactionIntoBPay.businessId,
      };

      const result =
        await getListDepositTransactionIntoBusinessBPayByBusinessId(mockParams);

      expect(result.data).toHaveLength(1);
      expect(result.data[0].member.name).toBe('mock-asker-name');
    });
  });

  describe('getTotalDepositTransactionIntoBusinessBPayByBusinessId', () => {
    it('should return total deposit transaction into business bpay by business id', async () => {
      const mockParams = {
        isoCode,
        filter: {
          search: 'mock-asker-name',
          rangeDate: {
            from: momentTz().subtract(1, 'day').startOf('day').toDate(),
            to: momentTz().add(1, 'day').endOf('day').toDate(),
          },
        },
        businessId: mockDepositBusinessTransactionIntoBPay.businessId,
      };

      const result =
        await getTotalRecordDepositTransactionIntoBusinessBPayByBusinessId(
          mockParams,
        );

      expect(result).toBe(1);
    });
  });
});
