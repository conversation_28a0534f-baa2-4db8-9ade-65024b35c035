rules:

  - id: robots-denied
    metadata:
      category: best-practice
      technology:
        - html
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    message: This page denies crawlers from indexing the page. Remove the robots
      'meta' tag.
    severity: INFO
    languages:
      - generic
    pattern-either:
      - pattern: <meta name="robots" ...>
      - pattern: <META NAME="ROBOTS" ...>

  - id: https-equiv
    metadata:
      category: correctness
      technology:
        - html
      references:
        - https://developer.mozilla.org/en-US/docs/Web/HTML/Element/meta#http-equiv
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    message: The correct attribute name for this meta tag is `http-equiv`, not
      `https-equiv`.
    severity: ERROR
    languages:
      - html
    pattern-either:
      - pattern: <meta ... https-equiv=...>
    fix-regex:
      regex: https-equiv=
      replacement: http-equiv=
