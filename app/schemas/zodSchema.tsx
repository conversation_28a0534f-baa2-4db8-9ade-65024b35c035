import type { TFunction } from 'i18next';
import { z } from 'zod';

const zodDepositBusinessTransactionIntoBPay = (
  t: TFunction<string, undefined>,
) => {
  return {
    payment: z.object({
      method: z.string().min(1, t('REQUIRED')),
      referenceCode: z.string().min(1, t('REQUIRED')),
    }),
    amount: z.coerce.number().min(1, t('VALUE_MUST_BE_GREATER_THAN_ZERO')),
    reason: z.string().min(1, t('REQUIRED')),
    requester: z.string().min(1, t('REQUIRED')),
    sendNotify: z.string().optional(),
  };
};

export { zodDepositBusinessTransactionIntoBPay };
