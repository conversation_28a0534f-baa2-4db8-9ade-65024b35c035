import { type LoaderFunctionArgs, json } from '@remix-run/node';
import {
  Form,
  Link,
  Outlet,
  useLoaderData,
  useLocation,
  useSubmit,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import {
  GlobalContext,
  type GlobalStore,
  createGlobalStore,
} from 'btaskee-hooks';
import {
  AccountantLogo,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Input,
  NavigatorDropdownCommon,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
} from 'btaskee-ui';
import { AlignJustify, UserCircle, XIcon } from 'lucide-react';
import { useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { authenticator } from '~/services/passports.server';
import { getUserPermissionIdsGlobal } from '~/services/role-base-access-control.server';
import { commitSession, getSession } from '~/services/session.server';
import { getUserProfile } from '~/services/settings.server';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const featureFlags = getEnvFeatureFlag();
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: ROUTE_NAME.SIGN_IN,
  });
  const [session, userPermissions, userProfile] = await Promise.all([
    getSession(request.headers.get('cookie')),
    getUserPermissionIdsGlobal(user.userId),
    getUserProfile(user.userId),
  ]);

  // get flash session
  session.get('flashMessage');

  return json(
    {
      user: { ...user, permissions: userPermissions },
      userProfile,
      featureFlags,
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session), // You must commit the session whenever you read a flash
      },
    },
  );
};

export const handle = { i18n: 'common' };

export default function Screen() {
  const { user, userProfile, featureFlags } = useLoaderData<typeof loader>();

  const storeRef = useRef<GlobalStore>();
  if (!storeRef.current) {
    storeRef.current = createGlobalStore({ ...user, featureFlags });
  }
  const { t } = useTranslation('common');

  const { i18n } = useTranslation();
  const submit = useSubmit();

  const location = useLocation();

  const onSubmit = (language: string) => {
    const formData = new FormData();
    formData.append('language', language);
    formData.append('name', 'changeLanguage');
    formData.append('redirect', location.pathname);

    i18n.changeLanguage(language);
    submit(formData, {
      method: 'post',
      action: '/',
    });
  };

  const navigation = useMemo(
    () => [
      {
        title: 'BTASKEE',
        routes: [
          {
            title: 'SUBSCRIPTION',
            route: ROUTE_NAME.OTHER_SUBSCRIPTION,
            permissions: [PERMISSIONS.READ_SUBSCRIPTION],
          },
          {
            title: 'REVENUE',
            route: ROUTE_NAME.BTASKEE_REVENUE,
            permissions: [PERMISSIONS.READ_BTASKEE_REVENUE],
          },
        ],
      },
      {
        title: 'ASKER',
        routes: [
          {
            title: 'BALANCE',
            route: ROUTE_NAME.ASKER_BALANCE,
            permissions: [PERMISSIONS.READ_ASKER_BALANCE],
          },
        ],
      },
      {
        title: 'TASKER',
        routes: [
          {
            title: 'INCOME',
            route: ROUTE_NAME.TASKER_INCOME,
            permissions: [PERMISSIONS.READ_TASKER_INCOME],
          },
          {
            route: ROUTE_NAME.TASKER_BALANCE,
            title: 'BALANCE',
            permissions: [PERMISSIONS.READ_TASKER_BALANCE],
          },
          {
            title: 'BNPL_NAV',
            routes: [
              {
                title: 'INSTALLMENT_MANAGEMENT',
                route: ROUTE_NAME.INSTALLMENT_MANAGEMENT,
                permissions: [PERMISSIONS.READ_INSTALLMENT_MANAGEMENT],
              },
              {
                title: 'PAYMENT_MANAGEMENT',
                route: ROUTE_NAME.PAYMENT_MANAGEMENT,
                permissions: [PERMISSIONS.READ_PAYMENT_MANAGEMENT],
              },
              {
                title: 'TRACKING_DAILY_TRANSACTION',
                route: ROUTE_NAME.TRACKING_DAILY_TRANSACTION,
                permissions: [PERMISSIONS.BNPL_TRACKING_DAILY_TRANSACTION],
              },
            ],
          },
          ...(featureFlags.Income_Tax_Report
            ? [
                {
                  route: ROUTE_NAME.INCOME_TAX_REPORT_GENERAL,
                  title: 'INCOME_TAX_REPORT_GENERAL',
                  permissions: [PERMISSIONS.EXPORT_INCOME_TAX_REPORT_GENERAL],
                },
                {
                  route: ROUTE_NAME.INCOME_TAX_REPORT_DETAIL,
                  title: 'INCOME_TAX_REPORT_DETAIL',
                  permissions: [PERMISSIONS.EXPORT_INCOME_TAX_REPORT_DETAIL],
                },
              ]
            : []),
        ],
      },

      {
        title: 'BUSINESS',
        routes: [
          {
            title: 'BPAY_TRANSACTION',
            route: ROUTE_NAME.BUSINESS_BPAY_TRANSACTION,
            permissions: [PERMISSIONS.READ_BUSINESS_BPAY_TRANSACTION],
          },
          {
            title: 'BUSINESS_MANAGEMENT',
            route: ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT,
            permissions: [PERMISSIONS.READ_BUSINESS_BPAY],
          },
          ...(featureFlags.Sale_Report
            ? [
                {
                  title: 'SALES_REPORT',
                  route: ROUTE_NAME.SALES_REPORT_IN_ACCOUNTING,
                  permissions: [PERMISSIONS.READ_SALES_REPORT],
                },
              ]
            : []),
        ],
      },
      ...(featureFlags.Btaskee_Income
        ? [
            {
              title: 'INDIVIDUAL',
              routes: [
                {
                  title: 'MONTHLY_INCOME',
                  route: ROUTE_NAME.ACCOUNTING_MONTHLY_INCOME,
                  permissions: [PERMISSIONS.READ_ACCOUNTING_MONTHLY_INCOME],
                },
                {
                  title: 'DAILY_INCOME',
                  route: ROUTE_NAME.ACCOUNTING_DAILY_INCOME,
                  permissions: [PERMISSIONS.READ_ACCOUNTING_DAILY_INCOME],
                },
              ],
            },
          ]
        : []),
      {
        title: 'OTHERS',
        routes: [
          {
            title: 'BEMPLOYEE',
            route: ROUTE_NAME.EMPLOYEE_BPAY_MANAGEMENT,
            permissions: [PERMISSIONS.READ_EMPLOYEE_BPAY],
          },
        ],
      },
    ],
    [featureFlags],
  );

  return (
    <>
      <div className="flex h-16 items-center m-auto max-w-[1392px]">
        <Drawer direction="left">
          <DrawerTrigger className="lg:hidden">
            <AlignJustify className="hover:text-primary" />
          </DrawerTrigger>
          <DrawerContent className="h-full w-[334px] rounded-br-lg rounded-tr-lg">
            <DrawerTitle className="flex w-full justify-between px-4 align-middle">
              <Link
                to="/"
                className="text-sm font-medium text-muted-foreground transition-colors">
                <DrawerClose>
                  <AccountantLogo />
                </DrawerClose>
              </Link>
              <DrawerClose>
                <XIcon className="transition-transform hover:rotate-180 hover:text-primary" />
              </DrawerClose>
            </DrawerTitle>
            <DrawerHeader>
              <DrawerDescription className="flex flex-col gap-4">
                <Input
                  type="search"
                  placeholder={t('SEARCH')}
                  className="my-2 w-full"
                />
              </DrawerDescription>
            </DrawerHeader>
          </DrawerContent>
        </Drawer>
        <nav className="hidden items-center gap-2 space-x-0.5 lg:flex lg:space-x-2">
          <Link
            to="/"
            className="text-sm font-medium text-muted-foreground transition-colors">
            <AccountantLogo />
          </Link>
          <NavigatorDropdownCommon
            navigation={navigation}
            userPermissions={user.permissions}
            visibleItems={7}
          />
        </nav>
        <div className="ml-auto flex items-center space-x-4">
          <Input
            type="search"
            placeholder={t('SEARCH')}
            className="hidden md:w-[100px] lg:inline-block lg:w-[270px]"
          />
          <Select
            defaultValue={userProfile?.language ?? 'en'}
            onValueChange={onSubmit}>
            <SelectTrigger className="h-10 w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="vi">Vietnamese</SelectItem>
              <SelectItem value="en">English</SelectItem>
            </SelectContent>
          </Select>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="relative h-8 w-8 rounded-full">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={userProfile?.avatarUrl} />
                  <AvatarFallback>
                    <UserCircle />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="mt-2 w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {userProfile?.username}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {userProfile?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link className="w-full" to={ROUTE_NAME.PROFILE_SETTING}>
                  {t('SETTINGS')}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Form className="w-full" method="post" action="/logout">
                  <button className="w-full text-start" type="submit">
                    {t('LOGOUT')}
                  </button>
                </Form>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <Separator />
      <div className="flex-1 my-6 m-auto max-w-[1392px]">
        <GlobalContext.Provider value={storeRef.current}>
          <Outlet />
        </GlobalContext.Provider>
      </div>
    </>
  );
}
