import {
  Outlet,
  isRouteErrorResponse,
  json,
  useRouteError,
} from '@remix-run/react';
import {
  BUSINESS_TRANSACTION_TYPE,
  DATE_RANGE_OPTIONS,
  PERMISSIONS,
  ROUTE_NAME,
  res404,
} from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, BtaskeeResponseError, toast } from 'btaskee-ui';
import {
  getRangeByPresetOptions,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getListSaleReportWithoutPagination } from '~/services/sales-report.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.SALES_REPORT_IN_ACCOUNTING}
      label="SALES_REPORT"
    />
  ),
  i18n: 'sales-report',
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  if (!getEnvFeatureFlag().Sale_Report) {
    throw new Response(null, res404);
  }

  const url = new URL(request.url);

  const [{ rangeDate, searchText, serviceIds }] = getValuesFromSearchParams(
    url.searchParams,
    {
      keysString: ['rangeDate', 'searchText', 'serviceIds'],
    },
  );

  const { isoCode } = await getUserSession({ headers: request.headers });

  const filteredRangeDate =
    rangeDate && rangeDate !== 'null' && rangeDate !== 'undefined'
      ? {
          from: momentTz(JSON.parse(rangeDate).from).toDate(),
          to: momentTz(JSON.parse(rangeDate).to).toDate(),
        }
      : getRangeByPresetOptions(DATE_RANGE_OPTIONS.THIS_MONTH);

  const filteredSaleReport = {
    ...(filteredRangeDate ? { rangeDate: filteredRangeDate } : {}),
    searchText,
    serviceIds,
  };

  const [dataCalculatingTotalAmount, settingCountry] = await Promise.all([
    getListSaleReportWithoutPagination({
      filter: filteredSaleReport,
      isoCode,
    }),
    getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    }),
  ]);

  return json({
    currency: settingCountry?.currency ?? {},
    filters: filteredSaleReport,
    dataCalculatingTotalAmount,
  });
}, PERMISSIONS.READ_SALES_REPORT);

export default function SalesReportIndex() {
  const {
    error: loaderError,
    dataCalculatingTotalAmount,
    ...restLoaderData
  } = useLoaderDataSafely<typeof loader>();

  const formattedReportData = useMemo(() => {
    const uniqueBusinessIds = new Set(
      dataCalculatingTotalAmount?.map(item => item.businessId) ?? [],
    );

    return Array.from(uniqueBusinessIds).map(businessId => {
      const businessData = dataCalculatingTotalAmount?.filter(
        item => item.businessId === businessId,
      );

      const totalTaskPrice = businessData?.reduce(
        (acc, currentVal) =>
          acc +
          (currentVal?.task?.newCostDetail?.cost ??
            currentVal?.task?.costDetail?.cost ??
            0),
        0,
      );

      const totalPromotionAmount = businessData?.reduce(
        (acc, currentVal) =>
          acc +
          (currentVal?.task?.costDetail?.decreasedReasons
            ?.filter(
              decreasedReason =>
                decreasedReason.key === 'PROMOTION_CODE' &&
                decreasedReason.promotionBy === 'BTASKEE',
            )
            .reduce((acc, currentVal) => acc + (currentVal?.value ?? 0), 0) ??
            0),
        0,
      );

      return {
        businessId,
        businessName: businessData?.[0]?.business?.name ?? '',
        businessPhone: businessData?.[0]?.businessUser?.phone ?? '',
        totalPaid: totalTaskPrice - totalPromotionAmount,
        totalPromotionAmount: Math.abs(totalPromotionAmount),
        totalCollectionFee: businessData?.reduce(
          (acc, currentVal) =>
            acc +
            (currentVal?.faTransaction?.reduce((acc, currentVal) => {
              if (
                currentVal?.source?.name === 'TASK' &&
                currentVal?.type === BUSINESS_TRANSACTION_TYPE.C
              ) {
                return acc + (currentVal?.amount ?? 0);
              }

              return acc;
            }, 0) ?? 0),
          0,
        ),
      };
    });
  }, [dataCalculatingTotalAmount]);

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return (
    <Outlet
      context={{
        formattedReportData,
        ...restLoaderData,
      }}
    />
  );
}
