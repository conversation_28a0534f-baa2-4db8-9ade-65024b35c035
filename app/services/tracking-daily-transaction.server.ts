import {
  BNPL_TRACKING_DAILY_TRANSACTION_REASON,
  CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL,
} from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface GettingBNPLTrackingDailyTransaction {
  isoCode: IsoCode;
  search?: string;
  rangeDate: { from: Date; to: Date };
  filter: { city?: string; reason?: string };
}

function getTrackingDailyTransactionAggregate({
  filter,
  search,
  isoCode,
  rangeDate,
}: GettingBNPLTrackingDailyTransaction) {
  if (!rangeDate) {
    throw new Error('REQUIRE_RANGE_DATE');
  }

  const filterReasonByOrCondition = [];
  const reasons = filter?.reason
    ? filter.reason.split(',')
    : Object.values(BNPL_TRACKING_DAILY_TRANSACTION_REASON);

  if (reasons.includes(BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL)) {
    filterReasonByOrCondition.push({
      $and: [
        { 'source.name': BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL },
        { 'source.taskId': { $exists: true } },
        { 'source.reason': { $exists: false } },
      ],
    });
  }

  if (
    reasons.includes(
      BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE,
    )
  ) {
    filterReasonByOrCondition.push({
      $and: [
        { 'source.name': BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL },
        { 'source.taskId': { $exists: false } },
        {
          'source.reason': CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL,
        },
      ],
    });
  }

  const aggregateMatcher: Array<PipelineStage> = [
    {
      $match: {
        createdAt: { $gte: rangeDate.from, $lte: rangeDate.to },
        ...(filterReasonByOrCondition.length
          ? { $or: filterReasonByOrCondition }
          : {}),
      },
    },
  ];

  const wrapper = {
    lookupAndUnwindUser,
    lookupUserIfSearchOrFilterByCity,
    searchAndFilter,
  };

  function lookupAndUnwindUser() {
    aggregateMatcher.push(
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).users,
          localField: 'taskerId',
          foreignField: '_id',
          as: 'taskerInfo',
        },
      },
      {
        $unwind: {
          path: '$taskerInfo',
          preserveNullAndEmptyArrays: true,
        },
      },
    );

    return wrapper;
  }

  function lookupUserIfSearchOrFilterByCity() {
    if (search || filter?.city) {
      aggregateMatcher.push(
        {
          $lookup: {
            from: getCollectionNameByIsoCode(isoCode).users,
            localField: 'taskerId',
            foreignField: '_id',
            as: 'taskerInfo',
          },
        },
        {
          $unwind: {
            path: '$taskerInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
      );
    }

    return wrapper;
  }

  function searchAndFilter() {
    const filters = {
      $match: {
        ...(search
          ? {
              $or: [
                { 'taskerInfo.name': { $regex: search, $options: 'i' } },
                { 'taskerInfo.phone': { $regex: search, $options: 'i' } },
              ],
            }
          : {}),
        ...(filter?.city
          ? {
              'taskerInfo.workingPlaces.city': { $in: filter.city.split(',') },
            }
          : {}),
      },
    };

    if (Object.keys(filters).length) aggregateMatcher.push(filters);

    return aggregateMatcher;
  }

  return wrapper;
}

export async function getTotalBNPLTrackingDailyTransaction(
  params: GettingBNPLTrackingDailyTransaction,
) {
  const transactions = await getModels(
    params.isoCode,
  ).taskerBNPLTransaction.aggregate(
    getTrackingDailyTransactionAggregate(params)
      .lookupUserIfSearchOrFilterByCity()
      .searchAndFilter(),
  );

  return transactions && Array.isArray(transactions) ? transactions.length : 0;
}

export async function getTotalDetectedAmountBNPLTrackingDailyTransaction(
  params: GettingBNPLTrackingDailyTransaction,
) {
  const transactions = await getModels(
    params.isoCode,
  ).taskerBNPLTransaction.aggregate(
    getTrackingDailyTransactionAggregate(params)
      .lookupUserIfSearchOrFilterByCity()
      .searchAndFilter(),
  );

  const totalTransaction = transactions.reduce((acc, transaction) => {
    return acc + transaction.amount;
  }, 0);

  return totalTransaction;
}

/**
 * @documentation
 * This function will $lookup to users (big collection), serviceChannel and service
 * Then $facet to 2 sub pipelines
 * This can significantly impact performance.
 */
export async function getBNPLTrackingDailyTransactions({
  sort,
  skip,
  limit,
  ...params
}: GettingBNPLTrackingDailyTransaction & {
  sort: PipelineStage.Sort['$sort'];
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
}) {
  const transactions = await getModels(
    params.isoCode,
  ).taskerBNPLTransaction.aggregate<
    {
      taskerInfo: Pick<UserApp, 'name' | 'phone' | 'workingPlaces'>;
    } & Pick<TaskerBNPLTransactionSchema, 'amount' | 'createdAt' | 'source'>
  >([
    ...getTrackingDailyTransactionAggregate(params)
      .lookupAndUnwindUser()
      .searchAndFilter(),
    { $sort: { ...sort, _id: 1 } },
    { $skip: skip },
    { $limit: limit },
    {
      $project: {
        'taskerInfo.name': 1,
        'taskerInfo.phone': 1,
        'taskerInfo.workingPlaces': 1,
        amount: 1,
        source: 1,
        createdAt: 1,
      },
    },
  ]);

  return transactions ?? [];
}
