// HIGHER ORDER COMPONENT
import {
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  json,
} from '@remix-run/node';
import { ACTION_NAME, ERROR, res403, res404 } from 'btaskee-constants';
import { logger } from '~/hoc/from.server';
import i18next from '~/i18next.server';
import { getUserId, newRecord<PERSON>ommonField } from '~/services/helpers.server';
import ActionsHistoryModel from '~/services/model/actionHistory.server';
import { getUserPermissionIdsGlobal } from '~/services/role-base-access-control.server';
import {
  httpRequest4xxCounter,
  httpRequest5xxCounter,
  httpRequestDurationMicroseconds,
} from '~/third-party/prometheus-client.server';

/**
 * @warning can make memory leak
 */
export function closureControllerDeeply<T>(defaultValue: T) {
  let action = defaultValue;

  function get() {
    return action;
  }
  function set(_action: T) {
    action = _action;
  }

  return { get, set };
}

type SetInformationActionHistory = (args: {
  action: string;
  dataRelated?: MustBeAny;
}) => void;
export function hocAction<A>(
  callback: (
    args: ActionFunctionArgs,
    argsHoc: { setInformationActionHistory: SetInformationActionHistory },
  ) => A,
  permission?: string | Array<string>,
) {
  async function action(args: ActionFunctionArgs) {
    let actionResult: Awaited<A>;

    const startEpoch = Date.now();
    const url = new URL(args.request.url);
    function prometheusObserve(status: string) {
      const responseTimeInMs = Date.now() - startEpoch;
      httpRequestDurationMicroseconds
        .labels(args.request.method, url.pathname, status)
        .observe(responseTimeInMs);
    }

    const userId = await getUserId({ request: args.request });

    if (permission) {
      const userPermissions = await getUserPermissionIdsGlobal(userId);

      if (typeof permission === 'string') {
        if (!userPermissions.includes(permission)) {
          prometheusObserve('403');
          httpRequest4xxCounter.inc();

          throw new Response(null, res403);
        }
      } else {
        let flag = false;

        // verify array permissions with user's permission
        permission.forEach(p => {
          if (userPermissions.includes(p)) {
            flag = true;
          }
        });
        if (!flag) {
          prometheusObserve('403');
          httpRequest4xxCounter.inc();

          throw new Response(null, res403);
        }
      }
    }

    const newActionHistory = new ActionsHistoryModel({
      ...newRecordCommonField(),
    });
    const { get, set } = closureControllerDeeply<{
      action: string;
      dataRelated?: MustBeAny;
      actorId?: string;
    }>({ action: '' });

    try {
      actionResult = await callback(args, {
        setInformationActionHistory: set,
      });
    } catch (error) {
      prometheusObserve('500');
      httpRequest5xxCounter.inc();

      logger.error(error);

      const t = await i18next.getFixedT(args.request, 'user-settings');
      if (error instanceof Error) {
        return json({ error: t(error.message) });
      }

      return json({ error: t(ERROR.UNKNOWN_ERROR), detail: error });
    }

    const { action, dataRelated } = get();

    // case login: special
    // must have dataRelated
    if (action === ACTION_NAME.LOGIN) {
      newActionHistory.action = action;
      newActionHistory.actorId = dataRelated.userId;

      await newActionHistory.save();
    } else if (action) {
      const formData = await args.request.clone().formData();
      const requestFormData = Object.fromEntries(formData);

      newActionHistory.actorId = userId;
      newActionHistory.action = action;

      // case insert data
      newActionHistory.requestFormData = {
        ...(requestFormData ? requestFormData : {}),
        ...(dataRelated ? dataRelated : {}),
      };

      await newActionHistory.save();
    }

    prometheusObserve('200');
    return actionResult;
  }

  return action;
}

export function hocLoader<A>(
  callback: (
    args: LoaderFunctionArgs,
    argsHoc: { permissionsPassed: Array<string> },
  ) => A,
  permission?: string | Array<string>,
) {
  async function loader(args: LoaderFunctionArgs) {
    const startEpoch = Date.now();
    const url = new URL(args.request.url);
    function prometheusObserve(status: string) {
      const responseTimeInMs = Date.now() - startEpoch;
      httpRequestDurationMicroseconds
        .labels(args.request.method, url.pathname, status)
        .observe(responseTimeInMs);
    }

    const userId = await getUserId({ request: args.request });
    const userPermissions = await getUserPermissionIdsGlobal(userId);

    const permissionsPassed: Array<string> = [];

    if (typeof permission === 'string') {
      if (!userPermissions.includes(permission)) {
        prometheusObserve('403');
        httpRequest4xxCounter.inc();
        throw new Response(null, res403);
      }
      permissionsPassed.push(permission);
    } else if (Array.isArray(permission)) {
      permission.forEach(p => {
        if (userPermissions.includes(p)) {
          permissionsPassed.push(p);
        }
      });
      if (!permissionsPassed.length) {
        prometheusObserve('403');
        httpRequest4xxCounter.inc();
        throw new Response(null, res403);
      }
    }

    const loaderResult = await callback(args, {
      permissionsPassed,
    });

    prometheusObserve('200');
    return loaderResult;
  }

  return loader;
}

export interface ErrorResponse {
  status: number;
  statusText: string;
}

export async function hoc404<TResult>(callback: () => Promise<TResult>) {
  const result = await callback();
  if ((Array.isArray(result) && !result.length) || !result) {
    throw new Response(null, res404);
  }

  return result;
}

export async function hoc403<TResult>(callback: () => Promise<TResult>) {
  const result = await callback();
  if ((Array.isArray(result) && !result.length) || !result) {
    throw new Response(null, res403);
  }

  return result;
}
