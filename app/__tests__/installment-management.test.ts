import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  getInstallmentManagement,
  getTotalInstallmentManagement,
} from '~/services/installment-management.server';

describe('Installment Management', () => {
  const isoCode = 'VN';
  const processId = [
    'process_1',
    'process_2',
    'process_3',
    'process_4',
    'process_5',
    'process_6',
    'process_7',
    'process_8',
    'process_9',
  ];

  const taskerId = [
    'tasker_1',
    'tasker_2',
    'tasker_3',
    'tasker_4',
    'tasker_5',
    'tasker_6',
    'tasker_7',
    'tasker_8',
    'tasker_9',
  ];

  const paymentToolKitTransactionId = [
    'payment_tool_kit_transaction_1',
    'payment_tool_kit_transaction_2',
    'payment_tool_kit_transaction_3',
    'payment_tool_kit_transaction_4',
    'payment_tool_kit_transaction_5',
    'payment_tool_kit_transaction_6',
    'payment_tool_kit_transaction_7',
    'payment_tool_kit_transaction_8',
    'payment_tool_kit_transaction_9',
  ];

  const paymentToolKitTransaction = paymentToolKitTransactionId.map(
    (id, index) => ({
      _id: id,
      type: 'BNPL',
      taskerId: taskerId[index],
      amount: 35000,
      createdAt: momentTz().add(index, 'hours').toDate(),
      payment: {
        status: 'PAID',
      },
      listTool: [
        {
          _id: `tool_${index}`,
          text: {
            en: `tool_${index}`,
          },
          price: 35000,
          quantity: 1,
          createdAt: momentTz().toDate(),
        },
      ],
    }),
  );

  const process = processId.map((id, index) => ({
    _id: id,
    taskerId: taskerId[index],
    status: 'PAYING',
    amount: 50000,
    remainingAmount: 35000.0,
    moneyBNPLOnTask: 3000.0,
    expiredAt: momentTz().toDate(),
    createdAt: momentTz().add(index, 'hours').toDate(),
    firstPayMoney: 36000.0,
  }));

  const tasker = taskerId.map((id, index) => ({
    _id: id,
    type: 'TASKER',
    name: `tasker ${index + 1}`,
    phone: `**********${index}`,
    language: 'vi',
    fAccountId: `fAccountId${index}`,
    status: 'ACTIVE',
    createdAt: momentTz().toDate(),
  }));

  beforeEach(async () => {
    await getModels(isoCode).taskerBNPLProcess.insertMany(process);
    await getModels(isoCode).users.insertMany(tasker);
    await getModels(isoCode).paymentToolKitTransaction.insertMany(
      paymentToolKitTransaction,
    );
  });

  afterEach(async () => {
    await getModels(isoCode).taskerBNPLProcess.deleteMany();
    await getModels(isoCode).users.deleteMany();
    await getModels(isoCode).paymentToolKitTransaction.deleteMany();
  });
  describe('getInstallmentMangement', () => {
    it('should return installment management', async () => {
      const mockParams = {
        skip: 0,
        limit: 10,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          search: 'tasker',
          status: 'PAYING',
          rangeDate: {
            from: momentTz().subtract(1, 'days').toDate(),
            to: momentTz().add(1, 'days').toDate(),
          },
        },
        isoCode,
      };

      const installmentManagement = await getInstallmentManagement(mockParams);

      const expectationData = process
        .filter(item =>
          mockParams.filter.status.split(',').includes(item.status),
        )
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      expect(installmentManagement).toHaveLength(expectationData.length);
      expect(installmentManagement[0]._id).toBe(expectationData[0]._id);
      expect(installmentManagement[0].tasker._id).toBe(
        expectationData[0].taskerId,
      );
    });
  });

  describe('getTotalInstallmentManagement', () => {
    it('should return total installment management', async () => {
      const mockParams = {
        skip: 0,
        limit: 10,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          search: 'tasker',
          status: 'PAYING',
          rangeDate: {
            from: momentTz().subtract(1, 'days').toDate(),
            to: momentTz().add(1, 'days').toDate(),
          },
        },
        isoCode,
      };

      const totalInstallmentManagement =
        await getTotalInstallmentManagement(mockParams);

      expect(totalInstallmentManagement).toBe(process.length);
    });
  });
});
