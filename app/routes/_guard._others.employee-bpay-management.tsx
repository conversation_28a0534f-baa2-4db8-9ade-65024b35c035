import { type SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useLoaderData,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  Grid,
  Typography,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { Upload } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getEmployeeDepartments,
  getEmployeeLevels,
  getListBEmployee,
  getTotalBEmployee,
} from '~/services/bEmployee.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.EMPLOYEE_BPAY_MANAGEMENT} label="BPAY" />
  ),
};

export const loader = hocLoader(
  async ({ request }) => {
    const url = new URL(request.url);

    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const bEmployeeFilter = {
      search: url.searchParams.get('search') || '',
      filter: {
        departments: url.searchParams.get('departments') || '',
        levels: url.searchParams.get('levels') || '',
      },
      rangeMonth: DEFAULT_RANGE_DATE_CURRENT_DAY(
        url.searchParams.get('rangeMonth') || '',
      ),
    };

    const [total, levels, settingCountry, departmentList] = await Promise.all([
      getTotalBEmployee({
        isoCode,
        ...bEmployeeFilter,
      }),
      getEmployeeLevels({ isoCode }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
      getEmployeeDepartments({ isoCode }),
    ]);

    const { limit, skip } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total,
        pageSize: Number(url.searchParams.get('pageSize')) || 0,
        pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
      }),
    );

    const employeeBPay = await getListBEmployee({
      isoCode,
      ...bEmployeeFilter,
      sort: convertSortString({
        sortString: url.searchParams.get('sort') || '',
        defaultValue: { date: -1 },
      }),
      skip,
      limit,
    });

    return json({
      employeeBPay,
      levels,
      bEmployeeFilter,
      settingCountry,
      departmentList,
      total,
    });
  },
  [PERMISSIONS.READ_EMPLOYEE_BPAY],
);

export default function EmployeeBPayManagement() {
  const { t } = useTranslation('employee-bPay-management');
  const [searchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListBEmployee>['transactions'][0]
    >
  >[] = [
    {
      accessorKey: 'askerFullName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('ASKER_FULL_NAME')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.name || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'phoneNumber',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('PHONE_NUMBER')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.phone || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'level',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('LEVEL')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.level || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'department',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('DEPARTMENT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.team || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'date',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TIME')} />
      ),
      cell: ({ row }) =>
        row.original?.date ? (
          <Typography variant="p" className="whitespace-nowrap">
            {format(row.original?.date, 'MMMM, yyyy')}
          </Typography>
        ) : null,
    },
    {
      accessorKey: 'topUpAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TOP_UP_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${(row.original?.topUpAmount || 0).toLocaleString()}${loaderData?.settingCountry?.currency?.sign || ''}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'usageAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('USAGE_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${(row.original?.usageAmount || 0).toLocaleString()}${loaderData?.settingCountry?.currency?.sign || ''}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'resetAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('RESET_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${(row.original?.resetAmount || 0).toLocaleString()}${loaderData?.settingCountry?.currency?.sign || ''}`}
        </Typography>
      ),
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{t('EMPLOYEE_B_PAY_MANAGEMENT')}</Typography>
          <Breadcrumbs />
        </Grid>
        <Button className="gap-2 text-sm font-medium" variant="default">
          <Upload className="w-5 h-5" />
          {t('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={loaderData?.total || 0}
        data={loaderData?.employeeBPay?.transactions || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="employee-bPay-management"
        search={{
          defaultValue: loaderData?.bEmployeeFilter?.search || '',
          name: 'search',
          placeholder: t('SEARCH_BY_ASKER_NAME_OR_PHONE'),
        }}
        filterDate={{
          name: 'rangeMonth',
          mode: 'range-month',
          defaultValue: {
            from: new Date(
              loaderData?.bEmployeeFilter?.rangeMonth?.from || new Date(),
            ),
            to: new Date(
              loaderData?.bEmployeeFilter?.rangeMonth?.to || new Date(),
            ),
          },
        }}
        filters={[
          {
            placeholder: t('DEPARTMENT'),
            options:
              loaderData?.departmentList?.map((department: string) => ({
                label: department,
                value: department,
              })) || [],
            value: loaderData?.bEmployeeFilter?.filter?.departments || '',
            name: 'departments',
          },
          {
            placeholder: t('LEVEL'),
            options:
              loaderData?.levels?.map(level => ({
                label: level.name,
                value: level.name,
              })) || [],
            value: loaderData?.bEmployeeFilter?.filter?.levels || '',
            name: 'levels',
          },
        ]}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Typography variant="p" className="text-gray-400 text-xs">
                {t('TOTAL_TOP_UP_AMOUNT')}
              </Typography>
              <Badge className="bg-blue-50 text-blue-500 rounded-md w-full py-3">
                <Typography variant="p" className="mx-auto font-semibold">
                  {`${loaderData?.employeeBPay?.totalTopUpAmount?.toLocaleString()}${loaderData?.settingCountry?.currency?.sign}`}
                </Typography>
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Typography variant="p" className="text-gray-400 text-xs">
                {t('TOTAL_USAGE_AMOUNT')}
              </Typography>
              <Badge className="bg-green-50 text-green-500 rounded-md w-full py-3">
                <Typography variant="p" className="mx-auto font-semibold">
                  {`${loaderData?.employeeBPay?.totalUsageAmount?.toLocaleString()}${loaderData?.settingCountry?.currency?.sign}`}
                </Typography>
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Typography variant="p" className="text-gray-400 text-xs">
                {t('TOTAL_RESET_AMOUNT')}
              </Typography>
              <Badge className="bg-orange-50 text-orange-500 rounded-md w-full py-3">
                <Typography variant="p" className="mx-auto font-semibold">
                  {`${loaderData?.employeeBPay?.totalResetAmount?.toLocaleString()}${loaderData?.settingCountry?.currency?.sign}`}
                </Typography>
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
