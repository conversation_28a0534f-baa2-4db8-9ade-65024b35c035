import { getClient } from '~/third-party/pg-connect.server';

interface UsersBalanceParams {
  month?: number;
  year: number;
  nameRegex?: string;
  sortField: string;
  sortOrder: string;
  limit: number;
  skip: number;
}

export interface TaskerBalanceMonthly {
  user_id: string;
  name: string;
  opening_balance_main_account: number;
  increased_main_account: number;
  decreased_main_account: number;
  ending_balance_main_account: number;
  opening_balance_promotion_account: number;
  increased_promotion_account: number;
  decreased_promotion_account: number;
  ending_balance_promotion_account: number;
}

export interface AskerBalanceMonthly {
  user_id: string;
  name: string;
  opening_balance_main_account: number;
  increased_main_account: number;
  decreased_main_account: number;
  ending_balance_main_account: number;
}

interface TaskerBalanceSummary {
  totalRows: number;
  totalOpeningMain: number;
  totalEndingMain: number;
  totalOpeningPromotion: number;
  totalEndingPromotion: number;
  totalIncreasedMainAccount: number;
  totalDecreasedMainAccount: number;
  totalIncreasedPromotionAccount: number;
  totalDecreasedPromotionAccount: number;
}
interface AskerBalanceSummary {
  totalRows: number;
  totalOpeningMain: number;
  totalEndingMain: number;
  totalIncreasedMainAccount: number;
  totalDecreasedMainAccount: number;
}

export async function getBalanceMonthlyByTasker({
  month,
  year,
  nameRegex,
  sortField,
  sortOrder,
  limit,
  skip,
}: UsersBalanceParams): Promise<{
  rows: Array<TaskerBalanceMonthly>;
  totals: TaskerBalanceSummary;
}> {
  const pgClient = await getClient();

  try {
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    const query = `
        WITH filtered_results AS (
            SELECT
                s.user_id,
                s.name,
                s.month_id AS user_month_id,
                s.opening_balance_main_account,
                s.increased_main_account,
                s.decreased_main_account,
                s.ending_balance_main_account,
                s.opening_balance_promotion_account,
                s.increased_promotion_account,
                s.decreased_promotion_account,
                s.ending_balance_promotion_account,
                d.id AS dim_month_id
            FROM
              fact_taskers_balance_monthly s
            JOIN
                dim_months d ON s.month_id = d.id
            WHERE
                d.year = $1
                AND ($2::INTEGER IS NULL OR d.month = $2)
                AND ($3::TEXT IS NULL OR s.name ~* $3)
        )
        SELECT
            user_id,
            name,
            opening_balance_main_account,
            increased_main_account,
            decreased_main_account,
            ending_balance_main_account,
            opening_balance_promotion_account,
            increased_promotion_account,
            decreased_promotion_account,
            ending_balance_promotion_account,
            COUNT(*) OVER () AS total_rows,
            SUM(opening_balance_main_account) OVER () AS total_opening_main,
            SUM(ending_balance_main_account) OVER () AS total_ending_main,
            SUM(opening_balance_promotion_account) OVER () AS total_opening_promotion,
            SUM(ending_balance_promotion_account) OVER () AS total_ending_promotion,
            SUM(increased_main_account) OVER () AS total_increased_main_account,
            SUM(decreased_main_account) OVER () AS total_decreased_main_account,
            SUM(increased_promotion_account) OVER () AS total_increased_promotion_account,
            SUM(decreased_promotion_account) OVER () AS total_decreased_promotion_account
        FROM filtered_results
        ORDER BY
            ${sortField} ${validSortOrder}
        LIMIT $4 OFFSET $5;
    `;

    const res = await pgClient.query(query, [
      year,
      month || null,
      nameRegex || null,
      limit,
      skip,
    ]);
    if (!res.rows.length) {
      return {
        rows: [],
        totals: {
          totalRows: 0,
          totalOpeningMain: 0,
          totalEndingMain: 0,
          totalOpeningPromotion: 0,
          totalEndingPromotion: 0,
          totalIncreasedMainAccount: 0,
          totalDecreasedMainAccount: 0,
          totalIncreasedPromotionAccount: 0,
          totalDecreasedPromotionAccount: 0,
        },
      };
    }

    return {
      totals: {
        totalRows: res.rows[0].total_rows,
        totalOpeningMain: res.rows[0].total_opening_main,
        totalEndingMain: res.rows[0].total_ending_main,
        totalOpeningPromotion: res.rows[0].total_opening_promotion,
        totalEndingPromotion: res.rows[0].total_ending_promotion,
        totalIncreasedMainAccount: res.rows[0].total_increased_main_account,
        totalDecreasedMainAccount: res.rows[0].total_decreased_main_account,
        totalIncreasedPromotionAccount:
          res.rows[0].total_increased_promotion_account,
        totalDecreasedPromotionAccount:
          res.rows[0].total_decreased_promotion_account,
      },
      rows: res.rows,
    };
  } finally {
    pgClient.release();
  }
}

export async function getBalanceMonthlyByAsker({
  month,
  year,
  nameRegex,
  sortField,
  sortOrder,
  limit,
  skip,
}: UsersBalanceParams): Promise<{
  rows: Array<AskerBalanceMonthly>;
  totals: AskerBalanceSummary;
}> {
  const pgClient = await getClient();

  try {
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    const query = `
        WITH filtered_results AS (
            SELECT
                s.user_id,
                s.name,
                s.month_id AS user_month_id,
                s.opening_balance_main_account,
                s.increased_main_account,
                s.decreased_main_account,
                s.ending_balance_main_account,
                d.id AS dim_month_id
            FROM
              fact_askers_balance_monthly s
            JOIN
                dim_months d ON s.month_id = d.id
            WHERE
                d.year = $1
                AND ($2::INTEGER IS NULL OR d.month = $2)
                AND ($3::TEXT IS NULL OR s.name ~* $3)
        )
        SELECT
            user_id,
            name,
            opening_balance_main_account,
            increased_main_account,
            decreased_main_account,
            ending_balance_main_account,
            COUNT(*) OVER () AS total_rows,
            SUM(opening_balance_main_account) OVER () AS total_opening_main,
            SUM(ending_balance_main_account) OVER () AS total_ending_main,
            SUM(increased_main_account) OVER () AS total_increased_main_account,
            SUM(decreased_main_account) OVER () AS total_decreased_main_account
        FROM filtered_results
        ORDER BY
            ${sortField} ${validSortOrder}
        LIMIT $4 OFFSET $5;
    `;

    const res = await pgClient.query(query, [
      year,
      month || null,
      nameRegex || null,
      limit,
      skip,
    ]);
    if (!res.rows.length) {
      return {
        rows: [],
        totals: {
          totalRows: 0,
          totalOpeningMain: 0,
          totalEndingMain: 0,
          totalIncreasedMainAccount: 0,
          totalDecreasedMainAccount: 0,
        },
      };
    }

    return {
      totals: {
        totalRows: res.rows[0].total_rows,
        totalOpeningMain: res.rows[0].total_opening_main,
        totalEndingMain: res.rows[0].total_ending_main,
        totalIncreasedMainAccount: res.rows[0].total_increased_main_account,
        totalDecreasedMainAccount: res.rows[0].total_decreased_main_account,
      },
      rows: res.rows,
    };
  } finally {
    pgClient.release();
  }
}

export async function exportBalanceMonthlyByTasker({
  month,
  year,
}: Pick<UsersBalanceParams, 'month' | 'year'>): Promise<
  Array<TaskerBalanceMonthly>
> {
  const pgClient = await getClient();

  try {
    const query = `
      SELECT
          s.user_id,
          s.name,
          s.month_id AS user_month_id,
          s.opening_balance_main_account,
          s.increased_main_account,
          s.decreased_main_account,
          s.ending_balance_main_account,
          s.opening_balance_promotion_account,
          s.increased_promotion_account,
          s.decreased_promotion_account,
          s.ending_balance_promotion_account,
          d.id AS dim_month_id
      FROM
          fact_taskers_balance_monthly s
      JOIN
          dim_months d ON s.month_id = d.id
      WHERE
          d.year = $1
          AND ($2::INTEGER IS NULL OR d.month = $2)
`;

    const res = await pgClient.query(query, [year, month || null]);

    return res.rows;
  } finally {
    pgClient.release();
  }
}

export async function exportBalanceMonthlyByAsker({
  month,
  year,
}: Pick<UsersBalanceParams, 'month' | 'year'>): Promise<
  Array<AskerBalanceMonthly>
> {
  const pgClient = await getClient();

  try {
    const query = `
      SELECT
          s.user_id,
          s.name,
          s.month_id AS user_month_id,
          s.opening_balance_main_account,
          s.increased_main_account,
          s.decreased_main_account,
          s.ending_balance_main_account,
          d.id AS dim_month_id
      FROM
          fact_askers_balance_monthly s
      JOIN
          dim_months d ON s.month_id = d.id
      WHERE
          d.year = $1
          AND ($2::INTEGER IS NULL OR d.month = $2)
`;

    const res = await pgClient.query(query, [year, month || null]);

    return res.rows;
  } finally {
    pgClient.release();
  }
}
