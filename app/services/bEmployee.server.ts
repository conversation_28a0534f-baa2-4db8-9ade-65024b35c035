import { FA_TRANSACTION_KEY_VALUE } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

export interface GettingListBEmployee {
  isoCode: IsoCode;
  rangeMonth: {
    from: Date;
    to: Date;
  };
  search?: string;
  filter?: {
    departments?: string;
    levels?: string;
  };
}

async function getMatcher({
  isoCode,
  rangeMonth,
}: GettingListBEmployee): Promise<PipelineStage.Match['$match']> {
  const commonMatcher: PipelineStage.Match['$match'] = {
    date:
      rangeMonth.from.getMonth() === rangeMonth.to.getMonth()
        ? {
            $gte: momentTz(rangeMonth.from).startOf('month').toDate(),
            $lt: momentTz(rangeMonth.to).endOf('month').toDate(),
          }
        : {
            $gte: momentTz(rangeMonth.from).startOf('date').toDate(),
            $lt: momentTz(rangeMonth.to).endOf('date').toDate(),
          },
  };

  const topUppedEmployee = await getModels(isoCode)
    .FATransaction.find({
      'source.value': FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_TOPUP,
      ...commonMatcher,
    })
    .lean<FATransaction[]>();

  const employeeIds = topUppedEmployee?.map(employee => employee.userId);

  return {
    userId: { $in: employeeIds },
    ...commonMatcher,
  };
}

/**
 * @documentation
 * This is complex query with more condition to getting value from transaction collection.
 * It also $lookup to 2 collection: users and bEmployee
 * All of this can significantly impact performance
 */
const getAggregateBeforeSearching = async ({
  isoCode,
  rangeMonth,
  filter,
  search,
}: GettingListBEmployee) => {
  const matcher = await getMatcher({ isoCode, rangeMonth });
  const aggregateBeforeSearching: Array<PipelineStage> = [
    {
      $match: matcher,
    },
    {
      $group: {
        _id: {
          month: { $month: '$date' },
          userId: '$userId',
        },
        totalTransactionAmount: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $ne: [
                      '$source.value',
                      FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_TOPUP,
                    ],
                  },
                  {
                    $ne: [
                      '$source.value',
                      FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_RESET,
                    ],
                  },
                ],
              },
              then: '$amount',
              else: 0,
            },
          },
        },
        resetAmount: {
          $sum: {
            $cond: {
              if: {
                $eq: [
                  '$source.value',
                  FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_RESET,
                ],
              },
              then: '$amount',
              else: 0,
            },
          },
        },
        topUpAmount: {
          $sum: {
            $cond: {
              if: {
                $eq: [
                  '$source.value',
                  FA_TRANSACTION_KEY_VALUE.BTASKEE_EMPLOYEE_TOPUP,
                ],
              },
              then: '$amount',
              else: 0,
            },
          },
        },
        date: { $first: '$date' },
      },
    },
    /**
     * @value topUpAmount
     * @description bPay amount adding to btaskee employee per month
     * This feature only management employee bPay,
     * so we will display if employee have the transaction adding employee bPay
     */
    {
      $match: {
        topUpAmount: {
          $gt: 0,
        },
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: '_id.userId',
        foreignField: '_id',
        as: 'userInfo',
      },
    },
    {
      $unwind: {
        path: '$userInfo',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).bEmployee,
        localField: 'userInfo.phone',
        foreignField: 'phone',
        as: 'bEmployeeInfo',
      },
    },
    {
      $unwind: {
        path: '$bEmployeeInfo',
        preserveNullAndEmptyArrays: true,
      },
    },
  ];

  const filtersBy$and = [];

  if (search) {
    filtersBy$and.push({
      $or: [
        { 'bEmployeeInfo.name': { $regex: search, $options: 'i' } },
        { 'bEmployeeInfo.phone': { $regex: search, $options: 'i' } },
      ],
    });
  }

  if (filter?.departments) {
    filtersBy$and.push({
      'bEmployeeInfo.team': { $in: filter.departments.split(',') },
    });
  }

  if (filter?.levels) {
    filtersBy$and.push({
      'bEmployeeInfo.level': { $in: filter.levels.split(',') },
    });
  }

  if (filtersBy$and.length) {
    aggregateBeforeSearching.push({
      $match: {
        $and: filtersBy$and,
      },
    });
  }

  return aggregateBeforeSearching;
};

export async function getTotalBEmployee({
  isoCode,
  rangeMonth,
  filter,
  search,
}: GettingListBEmployee) {
  const aggregate = await getAggregateBeforeSearching({
    isoCode,
    rangeMonth,
    search: search ?? '',
    ...(filter ? { filter } : {}),
  });

  const employeeBPayInfos = await getModels(isoCode)
    .FATransaction.aggregate(aggregate)
    .exec();

  return employeeBPayInfos?.length || 0;
}

export async function getListBEmployee({
  isoCode,
  rangeMonth,
  search,
  filter,
  sort,
  skip,
  limit,
}: GettingListBEmployee & {
  sort: PipelineStage.Sort['$sort'];
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
}): Promise<{
  transactions: Array<{
    name: string;
    phone: string;
    level: string;
    topUpAmount: number;
    usageAmount: number;
    resetAmount: number;
    date: Date;
    team: string;
  }>;
  totalTopUpAmount: number;
  totalUsageAmount: number;
  totalResetAmount: number;
}> {
  const aggregateBeforeSearching = await getAggregateBeforeSearching({
    isoCode,
    rangeMonth,
    search: search ?? '',
    ...(filter ? { filter } : {}),
  });

  const transactionInfos = await getModels(isoCode).FATransaction.aggregate([
    ...aggregateBeforeSearching,
    {
      $project: {
        date: 1,
        topUpAmount: 1,
        name: '$bEmployeeInfo.name',
        phone: '$bEmployeeInfo.phone',
        level: '$bEmployeeInfo.level',
        team: '$bEmployeeInfo.team',
        usageAmount: {
          $cond: {
            if: { $lte: ['$topUpAmount', '$totalTransactionAmount'] },
            then: '$topUpAmount',
            else: '$totalTransactionAmount',
          },
        },
        resetAmount: 1,
      },
    },
    {
      $facet: {
        totalTopUpAmount: [
          {
            $group: {
              _id: null,
              count: { $sum: '$topUpAmount' },
            },
          },
        ],
        totalUsageAmount: [
          {
            $group: {
              _id: null,
              count: { $sum: '$usageAmount' },
            },
          },
        ],
        totalResetAmount: [
          {
            $group: {
              _id: null,
              count: { $sum: '$resetAmount' },
            },
          },
        ],
        data: [
          { $sort: { ...sort, _id: -1 } },
          { $skip: skip },
          { $limit: limit },
        ],
      },
    },
  ]);

  return {
    transactions: transactionInfos?.[0]?.data || [],
    totalTopUpAmount: transactionInfos?.[0]?.totalTopUpAmount?.[0]?.count || 0,
    totalUsageAmount: transactionInfos?.[0]?.totalUsageAmount?.[0]?.count || 0,
    totalResetAmount: transactionInfos?.[0]?.totalResetAmount?.[0]?.count || 0,
  };
}

export async function getEmployeeDepartments({
  isoCode,
}: {
  isoCode: IsoCode;
}) {
  const departments = await getModels(isoCode).bEmployee.distinct('team');

  return departments || [];
}

export async function getEmployeeLevels({ isoCode }: { isoCode: IsoCode }) {
  const employeeSettingFound = await getModels(isoCode)
    .bEmployeeSetting.findOne({}, { level: 1 })
    .lean<EmployeeSetting>();

  return employeeSettingFound?.level || [];
}
