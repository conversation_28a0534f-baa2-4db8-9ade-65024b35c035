import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';
import {
  useLoaderInstallmentManagement,
  willBecomeLoader,
} from '~/hooks/useInstallmentManagement';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.INSTALLMENT_MANAGEMENT}
      label="INSTALLMENT_MANAGEMENT"
    />
  ),
};

export const loader = hocLoader(
  willBecomeLoader,
  PERMISSIONS.READ_INSTALLMENT_MANAGEMENT,
);

export default function InstallmentManagementScreen() {
  const loaderData = useLoaderInstallmentManagement();

  return <Outlet context={loaderData} />;
}
