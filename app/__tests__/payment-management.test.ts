import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongoose';
import { getModels } from 'schemas';
import {
  getPaymentToolKitTransaction,
  getTotalPaymentToolKitTransaction,
} from '~/services/payment-management.server';

describe('Payment Management', () => {
  const currentDate = momentTz().toDate();
  const mockPaymentToolKitTransaction = [
    {
      _id: 'paymentToolKitTransactionId_1',
      taskerId: 'taskerId_1',
      type: 'ONCE_PAY',
      amount: 100000,
      payment: {
        method: 'SHOPEE_PAY',
        transactionId: 'transactionId_1',
        status: 'PAID',
      },
      createdAt: currentDate,
    },
  ];

  const mockTasker = [
    {
      _id: 'taskerId_1',
      type: 'TASKER',
      name: 'Tasker 1',
      phone: '090xxxxx',
      workingPlaces: [
        {
          country: 'VN',
          city: 'Hồ Chí Minh',
        },
      ],
      fAccountId: 'fAccountId_1',
      language: 'en',
      status: 'ACTIVE',
      createdAt: currentDate,
    },
  ];

  beforeEach(async () => {
    await getModels('VN').paymentToolKitTransaction.insertMany(
      mockPaymentToolKitTransaction,
    );
    await getModels('VN').users.insertMany(mockTasker);
  });

  afterEach(async () => {
    await getModels('VN').paymentToolKitTransaction.deleteMany({});
    await getModels('VN').users.deleteMany({});
  });

  describe('getPaymentToolKitTransaction', () => {
    it('should get payment tool kit transaction', async () => {
      const mockParams = {
        isoCode: IsoCode.VN,
        limit: 10,
        skip: 0,
        sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
        filter: {
          type: 'ONCE_PAY',
          search: 'Tasker 1',
          paymentMethod: 'SHOPEE_PAY',
          city: 'Hồ Chí Minh',
          rangeDate: {
            from: momentTz(currentDate)
              .subtract(1, 'day')
              .startOf('day')
              .toDate(),
            to: momentTz(currentDate).add(1, 'day').endOf('day').toDate(),
          },
        },
      };
      const result = await getPaymentToolKitTransaction(mockParams);

      const filterData = mockPaymentToolKitTransaction.filter(
        transaction =>
          transaction.type === 'ONCE_PAY' &&
          transaction.payment.method === 'SHOPEE_PAY' &&
          transaction.payment.status === 'PAID' &&
          transaction.createdAt >= mockParams.filter.rangeDate.from &&
          transaction.createdAt <= mockParams.filter.rangeDate.to &&
          transaction.payment.transactionId,
      );
      const expectationData = {
        data: filterData.map(payment => ({
          ...Object.fromEntries(
            Object.entries(payment).filter(([key]) => key !== 'taskerId'),
          ),
          payment: {
            transactionId: payment.payment.transactionId,
            method: payment.payment.method,
          },
          tasker: {
            _id: mockTasker.find(tasker => tasker._id === payment.taskerId)
              ?._id,
            name: mockTasker.find(tasker => tasker._id === payment.taskerId)
              ?.name,
            phone: mockTasker.find(tasker => tasker._id === payment.taskerId)
              ?.phone,
            city: mockTasker.find(tasker => tasker._id === payment.taskerId)
              ?.workingPlaces[0]?.city,
          },
        })),
        totalAmount: filterData.reduce(
          (sum, transaction) => sum + transaction.amount,
          0,
        ),
      };

      expect(result.data).toEqual(expectationData.data);
      expect(result.totalAmount).toEqual(expectationData.totalAmount);
    });
  });

  describe('getTotalPaymentToolKitTransaction', () => {
    it('should get total payment tool kit transaction', async () => {
      const mockParams = {
        isoCode: 'VN',
        filter: {
          type: 'ONCE_PAY',
          search: 'Tasker 1',
          paymentMethod: 'SHOPEE_PAY',
          city: 'Hồ Chí Minh',
          rangeDate: {
            from: momentTz(currentDate)
              .subtract(1, 'day')
              .startOf('day')
              .toDate(),
            to: momentTz(currentDate).add(1, 'day').endOf('day').toDate(),
          },
        },
      };

      const filterData = mockPaymentToolKitTransaction.filter(
        transaction =>
          transaction.type === 'ONCE_PAY' &&
          transaction.payment.method === 'SHOPEE_PAY' &&
          transaction.payment.status === 'PAID' &&
          transaction.createdAt >= mockParams.filter.rangeDate.from &&
          transaction.createdAt <= mockParams.filter.rangeDate.to &&
          transaction.payment.transactionId,
      );
      const result = await getTotalPaymentToolKitTransaction(mockParams);
      expect(result).toEqual(filterData.length);
    });
  });
});
