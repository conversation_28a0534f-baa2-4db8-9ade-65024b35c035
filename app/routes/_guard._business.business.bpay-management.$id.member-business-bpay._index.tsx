import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  json,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Label,
  Typography,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getFormattedPhoneNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getBusinessLevels,
  getListMemberBusinessBPay,
  getTotalMemberBusinessBPay,
} from '~/services/business-bpay-management.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const url = new URL(request.url);

  const [{ sort, rangeDate, search, level }, { pageSize, pageIndex }] =
    getValuesFromSearchParams(url.searchParams, {
      keysString: ['sort', 'rangeDate', 'search', 'level'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

  const filterValue = {
    search,
    level,
    rangeDate: rangeDate
      ? DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate)
      : {
          from: momentTz().startOf('month').toDate(),
          to: momentTz().endOf('month').toDate(),
        },
  };

  const { isoCode } = await getUserSession({ headers: request.headers });

  const total = await getTotalMemberBusinessBPay({
    filter: filterValue,
    isoCode,
    businessId: params.id || '',
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const [bPayTransactionData, businessLevels, settingCountry] =
    await Promise.all([
      getListMemberBusinessBPay({
        businessId: params.id || '',
        filter: filterValue,
        isoCode,
        limit,
        skip,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
      }),
      getBusinessLevels({ businessId: params.id || '', isoCode }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

  return json({
    bPayTransactionList: bPayTransactionData?.data || [],
    total,
    filter: filterValue,
    totalDepositedAmount:
      bPayTransactionData?.totalAmount?.[0]?.totalDepositedAmount || 0,
    totalUsageAmount:
      bPayTransactionData?.totalAmount?.[0]?.totalUsageAmount || 0,
    totalResetedAmount:
      bPayTransactionData?.totalAmount?.[0]?.totalResetedAmount || 0,
    currency: settingCountry?.currency?.sign || '',
    businessLevels,
    businessId: params.id || '',
  });
}, PERMISSIONS.READ_BUSINESS_BPAY);

export default function MemberBusinessBPayScreen() {
  const { t: tMemberBusinessBPay } = useTranslation('member-business-bpay');
  const [searchParams] = useSearchParams();

  const navigate = useNavigate();

  const {
    currency,
    filter,
    total,
    bPayTransactionList,
    totalDepositedAmount,
    totalResetedAmount,
    totalUsageAmount,
    businessLevels,
    businessId,
  } = useLoaderDataSafely<typeof loader>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListMemberBusinessBPay>['data'][0]
    >
  >[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.name || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'phone',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('PHONE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {getFormattedPhoneNumber(row.original?.phone || '')}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'level',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('LEVEL')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="break-words whitespace-pre-wrap">
          {row.original?.level || ''}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('CREATED_AT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {format(row.original?.createdAt, 'MMMM, yyyy')}
        </Typography>
      ),
    },
    {
      accessorKey: 'depositedAmount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('DEPOSITED_AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.depositedAmount)}${currency}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'usageAmount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('USAGE_AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.usageAmount)}${currency}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'resetedAmount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={tMemberBusinessBPay('RESETED_AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="text-center">
          {`${formatNumberWithCommas(row.original?.resetedAmount)}${currency}`}
        </Typography>
      ),
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="text-center"
          title={tMemberBusinessBPay('ACTION')}
        />
      ),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted"
              onClick={e => {
                e.stopPropagation();
              }}>
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[140px]">
            <Link
              to={`${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${businessId}/member-business-bpay/${row.original._id}'`}>
              <DropdownMenuItem>
                {tMemberBusinessBPay('DETAIL')}
              </DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <BTaskeeTable
        isShowClearButton
        total={total || 0}
        data={bPayTransactionList || []}
        columns={columns}
        localeAddress="member-business-bpay"
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: filter?.search || '',
          name: 'search',
          placeholder: tMemberBusinessBPay('SEARCH'),
        }}
        filterDate={{
          name: 'rangeDate',
          mode: 'month',
          defaultValue: {
            from: momentTz(filter.rangeDate.from).startOf('month').toDate(),
            to: momentTz(filter.rangeDate.to).endOf('month').toDate(),
          },
        }}
        filters={[
          {
            name: 'level',
            options:
              businessLevels?.map(level => ({
                label: level.name,
                value: level._id,
              })) || [],
            placeholder: tMemberBusinessBPay('LEVEL'),
            value: filter?.level,
          },
        ]}
        onClickRow={row =>
          navigate(
            `${ROUTE_NAME.BUSINESS_BPAY_MANAGEMENT}/${businessId}/member-business-bpay/${row._id}`,
          )
        }
        extraContent={
          <div className="flex justify-end gap-6">
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tMemberBusinessBPay('TOTAL_DEPOSITED_AMOUNT')}
                </Label>
                <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalDepositedAmount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tMemberBusinessBPay('TOTAL_USAGE_AMOUNT')}
                </Label>
                <Badge className="bg-secondary text-secondary-foreground rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalUsageAmount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
            <div className="flex gap-6 justify-end">
              <div className="inline-flex flex-col items-center gap-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tMemberBusinessBPay('TOTAL_RESETED_AMOUNT')}
                </Label>
                <Badge className="bg-primary-50 text-primary rounded-md flex items-center justify-center py-[11px] w-40 text-sm leading-[18px]">
                  {`${formatNumberWithCommas(totalResetedAmount || 0)}${currency}`}
                </Badge>
              </div>
            </div>
          </div>
        }
      />
    </>
  );
}
