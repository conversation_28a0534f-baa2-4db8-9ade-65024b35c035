import FlagID from '@/images/flag-indonesia.svg';
import FlagMY from '@/images/flag-malaysia.svg';
import FlagTH from '@/images/flag-thailand.svg';
import FlagVN from '@/images/flag-vietnam.svg';
import {
  isRouteErrorResponse,
  json,
  redirect,
  useActionData,
  useOutletContext,
  useRouteError,
} from '@remix-run/react';
import { PERMISSIONS } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BtaskeeResponseError,
  Button,
  Card,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Grid,
  GridItem,
  Input,
  Label,
  MoneyInput,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  SelectBase,
  Separator,
  Typography,
  cn,
  toast,
  useBtaskeeFormController,
} from 'btaskee-ui';
import { formatNumberWithCommas } from 'btaskee-utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import { zodDepositBusinessTransactionIntoBPay } from '~/schemas/zodSchema';
import {
  depositBusinessTransactionIntoBPay,
  getBusinessCityAndBPayInfo,
  getInvoiceCodeDepositBusinessTransactionIntoBPay,
} from '~/services/deposit-business-transaction-into-bpay.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import { getAllUserName } from '~/services/settings.server';
import { sendNotification } from '~/services/utils.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const session = await getSession(request.headers.get('cookie'));
  const flashMessage = session.get('flashMessage');

  const [users, business, settingCountry] = await Promise.all([
    getAllUserName({ isoCode }),
    getBusinessCityAndBPayInfo({
      isoCode,
      businessId: params?.id || '',
    }),
    getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    }),
  ]);
  const invoiceCode = await getInvoiceCodeDepositBusinessTransactionIntoBPay({
    isoCode,
  });

  return json(
    {
      invoiceCode,
      bPay: business.bPay,
      businessName: business.businessName,
      currency: settingCountry?.currency,
      users,
      flashMessage,
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
});

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });
    const settingCountry = await getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    });

    const formData = await request.clone().formData();

    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');

    const invoiceNumber = formData.get('invoiceNumber')?.toString() || '';

    await depositBusinessTransactionIntoBPay({
      isoCode,
      data: {
        payment: {
          method: parsedData.payment.method,
          referenceCode: parsedData.payment.referenceCode,
          invoice: invoiceNumber,
        },
        amount: parsedData.amount,
        currency: settingCountry?.currency ?? { sign: '', code: '' },
        requester: parsedData.requester,
        businessId: params.id || '',
        reason: parsedData.reason,
        createdBy: username,
      },
    });

    if (parsedData.sendNotify) {
      await sendNotification({
        isSendNotificationId: true,
        isoCode,
        userIds: [params.id || ''],
        message: {
          title: {
            en: 'BPay Business Deposit Notification',
            vi: 'Thông Báo Nạp Tiền BPay Doanh Nghiệp',
            ko: 'BPay Business Deposit Notification',
            th: 'BPay Business Deposit Notification',
            id: 'BPay Business Deposit Notification',
            ms: 'BPay Business Deposit Notification',
          },
          body: {
            vi: `Tài khoản bPay doanh nghiệp của bạn đã được nạp thành công số tiền ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign}`,
            ko: `귀하의 bPay 비즈니스 계정에 ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign}  성공적으로 충전되었습니다.`,
            en: `Your bPay business account has been successfully topped up with ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign}.`,
            th: `บัญชีธุรกิจ bPay ของคุณได้เติมเงินจำนวน  ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign} สำเร็จแล้ว`,
            id: `Akun bPay bisnis Anda telah berhasil diisi dengan jumlah ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign}.`,
            ms: `Your bPay business account has been successfully topped up with ${formatNumberWithCommas(parsedData.amount)}${settingCountry?.currency?.sign}.`,
          },
        },
      });

      setInformationActionHistory({
        action: 'Send notification to business',
      });
    }

    const session = await getSession(request.headers.get('cookie'));
    session.flash('flashMessage', 'DEPOSIT_SUCCESS');
    const newSession = await commitSession(session);

    setInformationActionHistory({
      action: 'Deposit business transaction into BPay',
    });

    // go up one level in the URL hierarchy
    return redirect('..', {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_DEPOSIT_BUSINESS_TRANSACTION_INTO_BPAY,
);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function DepositDialog() {
  const Flag: Record<
    IsoCode,
    {
      name: string;
      flag: string;
    }
  > = {
    VN: {
      name: 'Việt Nam',
      flag: FlagVN,
    },
    TH: {
      name: 'Thailand',
      flag: FlagTH,
    },
    MY: {
      name: 'Malaysia',
      flag: FlagMY,
    },
    ID: {
      name: 'Indonesia',
      flag: FlagID,
    },
  };
  const isoCode = useGlobalStore(state => state.isoCode);

  const { t: tTransactionIntoBPay } = useTranslation(
    'deposit-business-transaction-into-bpay',
  );
  const {
    error: loaderError,
    invoiceCode,
    bPay,
    currency,
    businessName,
    users,
  } = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: tTransactionIntoBPay(actionData.error) });
    }
  }, [actionData, tTransactionIntoBPay]);

  const parentContext = useOutletContext<{
    openDialogDeposit: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
  }>();

  const { form: formDeposit, onSubmit } = useBtaskeeFormController<
    FormDepositBusinessTransactionIntoBPay & {
      payment: { invoiceNumber: string };
    }
  >({
    zodRaw: zodDepositBusinessTransactionIntoBPay(tTransactionIntoBPay),
    defaultValues: {
      payment: {
        method: 'BANK_TRANSFER',
        invoiceNumber: invoiceCode,
      },
    },
    confirmParams: {
      title: tTransactionIntoBPay('DEPOSIT_CONFIRMATION'),
      body: data => (
        <div>
          <Typography variant="p">
            {tTransactionIntoBPay(
              'ARE_YOU_SURE_DEPOSIT_FOR_THIS_BUSINESS_ACCOUNT',
            )}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('BUSINESS_ACCOUNT')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {businessName}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('PAYMENT_METHOD')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {tTransactionIntoBPay(data?.payment?.method)}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('VALUE')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {`${formatNumberWithCommas(data?.amount)}${currency?.sign}`}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('REFERENCE_CODE')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {data?.payment?.referenceCode || '-'}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('INVOICE')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {invoiceCode || '-'}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {tTransactionIntoBPay('REASON')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {data?.reason || '-'}
            </Typography>
          </Card>
        </div>
      ),
      actionButton: tTransactionIntoBPay('CONFIRM'),
      cancelButton: tTransactionIntoBPay('CANCEL'),
    },
    formDataProvided: data => {
      const formData = new FormData();

      formData.append('invoiceNumber', invoiceCode.toString());
      formData.append('data', JSON.stringify(data));

      return formData;
    },
  });

  const { control, handleSubmit } = formDeposit;

  return (
    <Dialog
      open={parentContext.openDialogDeposit}
      onOpenChange={open => {
        parentContext.onOpenChange(open);
        if (!open) parentContext.onClose();
      }}>
      <DialogContent className="max-w-[1064px]">
        <DialogHeader>
          <DialogTitle>{tTransactionIntoBPay('DEPOSIT_TITLE')}</DialogTitle>
        </DialogHeader>
        <Form {...formDeposit}>
          <form onSubmit={handleSubmit(onSubmit)} encType="multipart/form-data">
            <Grid className="gap-6 grid-cols-3">
              <GridItem>
                <Label className="text-gray-700">
                  {tTransactionIntoBPay('COUNTRY')}
                </Label>
                <div className="flex items-center gap-3">
                  <img
                    className="w-7"
                    src={Flag[isoCode as IsoCode]?.flag}
                    alt={`flag-${Flag[isoCode as IsoCode]?.name}`}
                  />
                  <Typography variant="h4" className="text-gray-600">
                    {Flag[isoCode as IsoCode]?.name}
                  </Typography>
                </div>
              </GridItem>
              <GridItem>
                <Label className="text-gray-700">
                  {tTransactionIntoBPay('BALANCE')}
                </Label>
                <Typography variant="h4" className="text-primary">
                  {`${formatNumberWithCommas(bPay || 0)}${currency?.sign}`}
                </Typography>
              </GridItem>
              <GridItem>
                <Label className="text-gray-700">
                  {tTransactionIntoBPay('INVOICE')}
                </Label>
                <Typography variant="h4" className="text-gray-600">
                  {invoiceCode}
                </Typography>
              </GridItem>
            </Grid>
            <div className="bg-primary-50 py-4 px-6 rounded-lg mt-6">
              <Label className="text-primary">
                {tTransactionIntoBPay('TOOLTIP_TITLE')}
              </Label>
            </div>

            <Separator className="my-6" />

            <Grid className="gap-6 grid-cols-2">
              <FormField
                name="payment.method"
                control={control}
                render={({ field: { onChange, value, ref } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tTransactionIntoBPay('PAYMENT_METHOD')}
                    </FormLabel>
                    <FormControl>
                      <SelectBase
                        selectTriggerRef={ref}
                        placeholder={tTransactionIntoBPay(
                          'SELECT_PAYMENT_METHOD',
                        )}
                        onValueChange={onChange}
                        defaultValue={value}
                        options={[
                          {
                            label: tTransactionIntoBPay('BANK_TRANSFER'),
                            value: 'BANK_TRANSFER',
                          },
                        ]}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="payment.referenceCode"
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tTransactionIntoBPay('REFERENCE_CODE')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={tTransactionIntoBPay(
                          'ENTER_REFERENCE_CODE',
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <MoneyInput
                form={formDeposit}
                name="amount"
                label={tTransactionIntoBPay('AMOUNT')}
                currency={currency?.code || ''}
                placeholder={tTransactionIntoBPay('ENTER_AMOUNT')}
              />
              <FormField
                name="reason"
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tTransactionIntoBPay('REASON')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={tTransactionIntoBPay('ENTER_REASON')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="requester"
                control={control}
                render={({ field: { onChange, value, ref } }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="text-gray-700">
                      {tTransactionIntoBPay('REQUESTER')}
                    </FormLabel>
                    <Popover modal>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between">
                            {value ? (
                              users.find(user => user === value)
                            ) : (
                              <span className="font-normal text-gray-400">
                                {tTransactionIntoBPay('SELECT_USERS')}
                              </span>
                            )}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput
                            placeholder={tTransactionIntoBPay('SEARCH_USERS')}
                          />
                          <CommandList>
                            <CommandEmpty>
                              {tTransactionIntoBPay('NO_USERS_FOUND')}
                            </CommandEmpty>
                            <CommandGroup>
                              {users.map(user => (
                                <CommandItem
                                  value={user}
                                  key={user}
                                  onSelect={onChange}>
                                  <Check
                                    className={cn(
                                      'mr-2 h-4 w-4',
                                      user === value
                                        ? 'opacity-100'
                                        : 'opacity-0',
                                    )}
                                  />
                                  {user}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="sendNotify"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <RadioGroup
                    onValueChange={onChange}
                    defaultValue={value ?? ''}
                    className="flex flex-col justify-center">
                    <FormItem className="flex items-center gap-4 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="sendNotify" />
                      </FormControl>
                      <FormLabel>
                        {tTransactionIntoBPay('SEND_NOTIFY')}
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                )}
              />
            </Grid>

            <DialogFooter className="flex justify-end gap-4">
              <DialogClose asChild>
                <Button
                  className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                  type="button"
                  variant="outline"
                  onClick={parentContext.onClose}>
                  {tTransactionIntoBPay('CANCEL')}
                </Button>
              </DialogClose>
              <Button variant="default" type="submit">
                {tTransactionIntoBPay('CONFIRM')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
