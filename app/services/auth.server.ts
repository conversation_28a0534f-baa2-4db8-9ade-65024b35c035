import { redirect } from '@remix-run/node';
import bcrypt from 'bcryptjs';
import {
  ERROR,
  EXPIRED_RESET_PASSWORD,
  EXPIRED_VERIFICATION_CODE,
  ROUTE_NAME,
  statusOriginal,
} from 'btaskee-constants';
import { getEnvBcrypt, getEnvMailServer, getEnvOriginal } from 'btaskee-dotenv';
import { getFutureTimeFromToday, momentTz } from 'btaskee-utils';
import { v4 as uuidv4 } from 'uuid';
import UsersModel from '~/services/model/users.server';
import { sendEmail } from '~/third-party/mail.server';

import GroupsModel from './model/groups.server';
import {
  addUserToGroupByEmail,
  getGroupsOfUser,
  removeUserFromGroup,
  updateStatusUserInGroup,
} from './role-base-access-control.server';

export function hashPassword(password: string) {
  return bcrypt.hashSync(
    `${getEnvBcrypt().BCRYPT_PLAIN_TEXT}${password}`,
    getEnvBcrypt().BCRYPT_SALT_ROUND,
  );
}

function compareHash({ password, hash }: { password: string; hash: string }) {
  return bcrypt.compare(getEnvBcrypt().BCRYPT_PLAIN_TEXT + password, hash);
}

export async function verifyAndSendCode({
  password,
  username,
}: {
  password: string;
  username: string;
}) {
  const user = await UsersModel.findOne({ username }).lean<Users>();
  const bcrypt = user?.services?.password?.bcrypt;

  if (user && user.status !== statusOriginal.ACTIVE) {
    throw new Error('ACCOUNT_INACTIVE');
  }

  // always display incorrect account error
  if (!user || !bcrypt) {
    throw new Error('INCORRECT_ACCOUNT');
  }
  const verified = await compareHash({
    password,
    hash: bcrypt,
  });

  if (!verified) throw new Error('INCORRECT_ACCOUNT');

  const verificationToken = await sendVerificationCode(
    user.email,
    user.name || username,
  );

  return { verificationToken, userId: user._id };
}

export async function sendVerificationCode(email: string, name: string) {
  // 6 numbers code
  const verificationCode = Math.floor(
    100000 + Math.random() * 900000,
  ).toString();
  const token = uuidv4();

  await UsersModel.updateOne(
    {
      email,
    },
    {
      $set: {
        verification: {
          code: verificationCode,
          expired: getFutureTimeFromToday(
            EXPIRED_VERIFICATION_CODE,
            'minutes',
          ).toDate(),
          token,
        },
      },
    },
  );

  await sendEmail({
    to: email,
    from: getEnvMailServer().MAIL_SERVER_USERNAME,
    subject: 'Mã xác thực bảo mật để đăng nhập vào hệ thống bTaskee',
    html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Mã Xác Thực bTaskee</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          background-color: #f4f4f4;
          padding: 20px;
        }
        .container {
          width: 100%;
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .code {
          font-size: 1.5em;
          font-weight: bold;
          color: #d9534f;
          text-align: center;
          margin: 20px 0;
        }
        .footer {
          margin-top: 20px;
          font-size: 0.9em;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <p>Chào <strong>${name}</strong>,</p>

        <p>Để đảm bảo an toàn cho tài khoản của bạn, chúng tôi yêu cầu bạn nhập mã xác thực sau khi đăng nhập vào hệ thống bTaskee.</p>

        <div class="code">${verificationCode}</div>

        <p><strong>Lưu ý:</strong> Mã xác thực này chỉ có hiệu lực trong <strong>${EXPIRED_VERIFICATION_CODE} phút</strong> và chỉ có thể sử dụng một lần.</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
          <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
      </div>
    </body>
    </html>
  `,
  });

  return token;
}

export async function isVerificationCodeExpired({ token }: { token: string }) {
  const account = await UsersModel.findOne({
    'verification.token': token,
    'verification.expired': { $gt: momentTz().toDate() },
  }).lean();

  return !account?._id;
}

export async function isResetPassExpired({ token }: { token: string }) {
  const account = await UsersModel.findOne({
    'resetPassword.token': token,
    'resetPassword.expired': { $gt: momentTz().toDate() },
  }).lean();

  return !account?._id;
}

export async function getUserByUserId({ userId }: { userId: string }) {
  const account = await UsersModel.findOne(
    {
      _id: userId,
    },
    {
      language: 1,
      isoCode: 1,
      username: 1,
      email: 1,
      cities: 1,
      avatarUrl: 1,
      status: 1,
      name: 1,
    },
  ).lean<Users>();

  return account;
}

export async function updateUser({
  name,
  username,
  cities,
  userId,
  groupIds,
  email,
  status,
}: Pick<Users, 'username' | 'cities' | 'email' | 'name' | 'status'> & {
  userId: string;
  groupIds: Array<string>;
}) {
  // Fetch the old groups of the user
  const oldGroups = await getGroupsOfUser({
    userId,
    projection: { _id: 1, name: 1 },
  });

  // Check if the username already exists for another user
  const existingUser = await UsersModel.findOne({
    _id: { $ne: userId },
    username,
  }).lean<Users>();

  if (existingUser) {
    throw new Error('USER_ALREADY_EXISTS');
  }

  // Extract the IDs of the old groups
  const oldGroupIds = oldGroups.map(group => group._id);

  // Determine the groups to be removed and added
  const removedGroups = oldGroups.filter(
    group => !groupIds.includes(group._id),
  );
  const addedGroupIds = groupIds.filter(group => !oldGroupIds.includes(group));

  // Add the user to the new groups
  await Promise.all(
    addedGroupIds.map(async groupId => {
      const group = await GroupsModel.findOne({ _id: groupId });
      if (group) {
        await addUserToGroupByEmail({ email, groupId, groupName: group.name });
      }
    }),
  );

  // Remove the user from the old groups
  await Promise.all(
    removedGroups.map(async group => {
      await removeUserFromGroup({ userId, groupId: group._id });
    }),
  );

  // Update user status in the groups
  await Promise.all(
    groupIds.map(async groupId => {
      await updateStatusUserInGroup({ userId, groupId, status });
    }),
  );

  // Update the user information
  return UsersModel.findOneAndUpdate(
    { _id: userId },
    {
      $set: { name, username, cities, status, updatedAt: momentTz().toDate() },
    },
  ).lean<Users>();
}

export async function resetPassword(email: string) {
  const resetToken = uuidv4();

  const account = await UsersModel.findOneAndUpdate(
    {
      email,
    },
    {
      $set: {
        resetPassword: {
          expired: getFutureTimeFromToday(
            EXPIRED_RESET_PASSWORD,
            'minutes',
          ).toDate(),
          token: resetToken,
        },
      },
    },
  );

  if (!account?._id) {
    throw new Error(ERROR.EMAIL_INCORRECT);
  }

  await sendEmail({
    to: email,
    from: getEnvMailServer().MAIL_SERVER_USERNAME,
    subject: 'Hướng dẫn đặt lại mật khẩu',
    html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Đặt Lại Mật Khẩu</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          padding: 20px;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: #28a745;
          color: white !important;
          text-decoration: none;
          font-weight: bold;
          border-radius: 5px;
          margin-top: 10px;
        }
        .footer {
          margin-top: 20px;
          font-size: 0.9em;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <p>Chào <strong>${account.name || account.username}</strong>,</p>

        <p>Chúng tôi đã nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. 
        Để tiếp tục quá trình, vui lòng nhấp vào liên kết dưới đây để tạo mật khẩu mới:</p>

        <p>Tên đăng nhập: <strong>${account.username}</strong></p>

        <a href="${getEnvOriginal().ORIGINAL_DOMAIN}${ROUTE_NAME.RESET_PASSWORD}/${resetToken}" class="button">Tạo Mật Khẩu Mới</a>

        <p>Nếu bạn cần hỗ trợ thêm, xin vui lòng liên hệ với chúng tôi qua email hoặc gọi đến đường dây hỗ trợ.</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
          <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
      </div>
    </body>
    </html>
  `,
  });
}

export async function changePassword({
  token,
  newPassword,
}: {
  token: string;
  newPassword: string;
}) {
  if (!newPassword || !token) throw new Error('UNKNOWN_ERROR');

  const hashedPassword = hashPassword(newPassword);

  const account = await UsersModel.findOneAndUpdate(
    {
      'resetPassword.expired': { $gt: momentTz().toDate() },
      'resetPassword.token': token,
    },
    {
      $set: {
        'services.password.bcrypt': hashedPassword,
        'resetPassword.expired': momentTz().toDate(),
      },
    },
  );

  // update failure, redirect into reset password page
  if (!account?._id) {
    return redirect(ROUTE_NAME.RESET_PASSWORD);
  }
  return account._id;
}
