import {
  BNPL_TRACKING_DAILY_TRANSACTION_REASON,
  IsoCode,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
  getBNPLTrackingDailyTransactions,
  getTotalBNPLTrackingDailyTransaction,
  getTotalDetectedAmountBNPLTrackingDailyTransaction,
} from '~/services/tracking-daily-transaction.server';

describe('BNPL Tracking Daily Transaction', () => {
  const isoCode = IsoCode.VN;
  const taskerBNPLTransactionIds = [
    'taskerBNPLTransactionId_1',
    'taskerBNPLTransactionId_2',
    'taskerBNPLTransactionId_3',
  ];
  const userIds = ['userId_1', 'userId_2', 'userId_3'];
  const serviceChannelIds = [
    'serviceChannelId_1',
    'serviceChannelId_2',
    'serviceChannelId_3',
  ];
  const serviceIds = ['serviceId_1', 'serviceId_2', 'serviceId_3'];
  const mockCities = ['H<PERSON> Chí <PERSON>', 'Hà Nội', 'Đà Nẵng'];

  const mockTaskerBNPLTransactions = taskerBNPLTransactionIds.map(
    (id, index) => ({
      _id: id,
      taskerId: userIds[index],
      source: {
        name: BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL,
        taskId: `taskId_${id}`,
      },
      amount: 1000,
      createdAt: momentTz().toDate(),
    }),
  );
  const mockUsers = userIds.map(id => ({
    _id: id,
    name: `Tasker_${id}`,
    phone: `090xxxxx_${id}`,
    isoCode,
    workingPlaces: mockCities.map(city => ({ city })),
    fAccountId: 'fAccountId_1',
    language: 'en',
    status: 'ACTIVE',
    createdAt: new Date(),
  }));
  const mockServices = serviceIds.map((id, index) => ({
    _id: id,
    name: `Service_${id}`,
    status: 'ACTIVE',
    icon: `icon_${id}`,
    text: {
      vi: `vi_${id}`,
      en: `en_${id}`,
      ko: `ko_${id}`,
      th: `th_${id}`,
      id: `id_${id}`,
    },
  }));
  const MOCK_TOTAL_DETECTED_AMOUNT = 3000;
  const mockServiceChannels = serviceChannelIds.map((id, index) => ({
    _id: id,
    serviceId: serviceIds[index],
    taskerList: userIds[index],
  }));

  beforeEach(async () => {
    await getModels(isoCode).taskerBNPLTransaction.insertMany(
      mockTaskerBNPLTransactions,
    );
    await getModels(isoCode).users.insertMany(mockUsers);
    await getModels(isoCode).serviceChannel.insertMany(mockServiceChannels);
    await getModels(isoCode).service.insertMany(mockServices);
  });

  afterEach(async () => {
    await getModels(isoCode).taskerBNPLTransaction.deleteMany({
      _id: {
        $in: taskerBNPLTransactionIds,
      },
    });
    await getModels(isoCode).users.deleteMany({
      _id: { $in: mockUsers.map(user => user._id) },
    });
    await getModels(isoCode).serviceChannel.deleteMany({
      _id: {
        $in: mockServiceChannels.map(serviceChannel => serviceChannel._id),
      },
    });
    await getModels(isoCode).service.deleteMany({
      _id: { $in: mockServices.map(service => service._id) },
    });
  });

  describe('getTotalBNPLTrackingDailyTransaction', () => {
    it('should return total BNPL tracking daily transaction', async () => {
      const params = {
        isoCode,
        rangeDate: {
          from: momentTz().subtract(1, 'day').toDate(),
          to: momentTz().add(1, 'day').toDate(),
        },
        filter: {
          reason: Object.values(BNPL_TRACKING_DAILY_TRANSACTION_REASON).join(
            ',',
          ),
          city: mockCities.join(','),
        },
      };

      const total = await getTotalBNPLTrackingDailyTransaction(params);

      const expectationTotal = mockTaskerBNPLTransactions.filter(
        transaction => {
          const transactionDate = momentTz(transaction.createdAt);
          const from = momentTz(params.rangeDate.from);
          const to = momentTz(params.rangeDate.to);

          return (
            transactionDate.isBetween(from, to) &&
            params.filter.reason.includes(transaction.source.name)
          );
        },
      ).length;

      expect(total).toBe(expectationTotal);
    });
  });
  describe('getBNPLTrackingDailyTransaction', () => {
    it('should return BNPL tracking daily transaction', async () => {
      const params = {
        isoCode,
        rangeDate: {
          from: momentTz().subtract(1, 'day').toDate(),
          to: momentTz().add(1, 'day').toDate(),
        },
        sort: {
          createdAt: -1,
        } as PipelineStage.Sort['$sort'],
        skip: 0,
        limit: 10,
        filter: {
          reason: Object.values(BNPL_TRACKING_DAILY_TRANSACTION_REASON).join(
            ',',
          ),
          city: mockCities.join(','),
        },
      };

      const transactions = await getBNPLTrackingDailyTransactions(params);

      const expectationResult = mockTaskerBNPLTransactions
        .filter(transaction => {
          const transactionDate = momentTz(transaction.createdAt);
          const from = momentTz(params.rangeDate.from);
          const to = momentTz(params.rangeDate.to);

          return (
            transactionDate.isBetween(from, to) &&
            params.filter.reason.includes(transaction.source.name)
          );
        })
        .slice(params.skip, params.limit);

      expect(transactions).toHaveLength(expectationResult.length);
    });
  });
  describe('getTotalDetectedAmountBNPLTrackingDailyTransaction', () => {
    it('Should return total detected amount BNPL tracking daily transaction', async () => {
      const params = {
        isoCode,
        rangeDate: {
          from: momentTz().subtract(1, 'day').toDate(),
          to: momentTz().add(1, 'day').toDate(),
        },
        filter: {
          reason: Object.values(BNPL_TRACKING_DAILY_TRANSACTION_REASON).join(
            ',',
          ),
          city: mockCities.join(','),
        },
      };

      const total =
        await getTotalDetectedAmountBNPLTrackingDailyTransaction(params);

      expect(total).toEqual(MOCK_TOTAL_DETECTED_AMOUNT);
    });
  });
});
