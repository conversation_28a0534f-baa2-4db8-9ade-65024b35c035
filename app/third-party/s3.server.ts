import { S3 } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import {
  type UploadHandlerPart,
  writeAsyncIterableToWritable,
} from '@remix-run/node';
import { getEnvS3 } from 'btaskee-dotenv';
import { PassThrough } from 'stream';

export const s3UploadHandler = async ({
  data,
  filename,
  contentType,
}: UploadHandlerPart) => {
  const writable = new PassThrough();
  await writeAsyncIterableToWritable(data, writable);

  const dotenv = getEnvS3();
  const file = await new Upload({
    client: new S3({
      credentials: {
        accessKeyId: dotenv.STORAGE_ACCESS_KEY,
        secretAccessKey: dotenv.STORAGE_SECRET,
      },
      region: dotenv.STORAGE_REGION,
    }),
    params: {
      Bucket: dotenv.STORAGE_BUCKET,
      Key: `${dotenv.STORAGE_PATH}/accounting/${Date.now()}_${filename || ''}`,
      ACL: 'public-read',
      Body: writable,
    },
  }).done();

  return file.Location;
};
