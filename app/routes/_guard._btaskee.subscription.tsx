import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useLoaderData,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useExportFile, useGlobalStore } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  momentTz,
} from 'btaskee-utils';
import { Upload } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  getListTaskOfSubscription,
  getTesterIds,
  getTotalListTaskOfSubscription,
} from '~/services/subscription.server';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.OTHER_SUBSCRIPTION}
        label="SUBSCRIPTION"
      />
    );
  },
  i18n: 'subscription',
};

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const search = url.searchParams.get('search') || '';
  const rangeMonth = url.searchParams.get('rangeMonth') || '';

  const { isoCode } = await getUserSession({
    headers: request.headers,
  });
  const testerIds = await getTesterIds({ isoCode });

  const filter = {
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeMonth),
    search,
  };

  const total = await getTotalListTaskOfSubscription({
    isoCode,
    testerIds,
    filter,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const [subscriptionList, settingCountry] = await Promise.all([
    getListTaskOfSubscription({
      isoCode,
      filter: {
        ...filter,
        sort: convertSortString({
          sortString: url.searchParams.get('sort') || '',
          defaultValue: { createdAt: -1 },
        }),
      },
      testIds: testerIds,
      limit,
      skip,
    }),
    getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
  ]);

  return json({
    subscriptionList,
    total,
    filter,
    currency: settingCountry?.currency?.sign || '',
  });
}, PERMISSIONS.READ_SUBSCRIPTION);

export default function SubscriptionListScreen() {
  const { t } = useTranslation('subscription');
  const [searchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const permissions = useGlobalStore(store => store.permissions);

  const fromDate = momentTz(loaderData?.filter?.rangeDate?.from);
  const toDate = momentTz(loaderData?.filter?.rangeDate?.to);

  const currency = loaderData?.currency || '';

  const { exportCsv: exportListTaskOfSubscription } = useExportFile();
  const confirmExport = useConfirm();

  const onExportExcel = async () => {
    const exportedExcelData = loaderData?.subscriptionList?.map(item => ({
      [t('ASKER_ID')]: item?.subscriptionId || '',
      [t('SUBSCRIPTION_ACTIVATED_DATE')]: momentTz(item?.createdAt).format(
        'HH:mm - DD/MM/YYYY',
      ),
      [t('SUBSCRIPTION_ID')]: item?.subscriptionId || '',
      [t('SUB_START_DATE')]: item?.start
        ? momentTz(item?.start).format('HH:mm - DD/MM/YYYY')
        : '',
      [t('SUB_END_DATE')]: item?.end
        ? momentTz(item?.end).format('HH:mm - DD/MM/YYYY')
        : '',
      [t('SUB_AMOUNT')]: item?.finalCost || 0,
      [t('TASK_ID')]: item?.taskId || '-',
      [t('TASK_DATE')]: item?.transactionDate
        ? momentTz(item?.transactionDate).format('HH:mm - DD/MM/YYYY')
        : '-',
      [t('TASK_AMOUNT')]: item?.moneyUsed || 0,
      [t('REMAINING_AMOUNT')]: item?.moneyRemain || 0,
      [t('BPAY_REFUND_AMOUNT')]: item?.moneyRefundbPay || 0,
    }));

    const fileName = `List task of subscription from ${fromDate.format('DD/MM/YYYY')} to ${toDate.format('DD/MM/YYYY')}`;

    const isConfirm = await confirmExport({
      title: t('EXPORT_TO_EXCEL'),
      body: (
        <div>
          <Typography variant="p">
            {t('ARE_YOU_SURE_EXPORT_TO_EXCEL')}
          </Typography>
          <Card className="bg-gray-50 border border-gray-200 rounded-[6px] p-4 mt-4">
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {t('TOTAL_RECORDS')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {loaderData?.total}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-[#A3A3A3] text-xs font-medium">
              {t('TIME')}
            </Label>
            <Typography
              affects="removePMargin"
              variant="p"
              className="text-gray-600 font-medium text-base">
              {fromDate.format('DD/MM/YYYY')} - {toDate.format('DD/MM/YYYY')}
            </Typography>
          </Card>
        </div>
      ),
      cancelButton: t('CANCEL'),
      actionButton: t('CONFIRM'),
    });

    if (isConfirm) {
      exportListTaskOfSubscription({
        fileName,
        arrayJson: exportedExcelData,
      });
    }
  };

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListTaskOfSubscription>[0]>
  >[] = [
    {
      accessorKey: 'askerId',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('ASKER_ID')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.userId || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'subscriptionActivatedDate',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={t('SUBSCRIPTION_ACTIVATED_DATE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {momentTz(row.original?.createdAt).format('HH:mm - DD/MM/YYYY')}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'subscriptionId',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('SUBSCRIPTION_ID')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.subscriptionId || ''}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'subStartDate',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('SUB_START_DATE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.start
            ? momentTz(row.original?.start).format('HH:mm - DD/MM/YYYY')
            : ''}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'subEndDate',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('SUB_END_DATE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.end
            ? momentTz(row.original?.end).format('HH:mm - DD/MM/YYYY')
            : ''}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'subAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('SUB_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{`${formatNumberWithCommas(row.original?.finalCost || 0)}${currency}`}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'taskId',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TASK_ID')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.taskId || ''}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'transactionDate',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TRANSACTION_DATE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.transactionDate
            ? momentTz(row.original?.transactionDate).format(
                'HH:mm - DD/MM/YYYY',
              )
            : ''}
        </Typography>
      ),
    },
    {
      accessorKey: 'taskAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TASK_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{`${formatNumberWithCommas(Number(row.original?.moneyUsed) || 0)}${currency}`}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'remainingAmount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('REMAINING_AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{`${formatNumberWithCommas(Number(row.original?.moneyRemain) || 0)}${currency}`}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'bpayRefundAmount',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={t('BPAY_REFUND_AMOUNT')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {`${formatNumberWithCommas(Number(row.original?.moneyRefundbPay) || 0)}${currency}`}
        </Typography>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{t('SUBSCRIPTION')}</Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          onClick={onExportExcel}
          disabled={
            !permissions?.includes(PERMISSIONS.EXPORT_LIST_TASK_OF_SUBSCRIPTION)
          }>
          <Upload className="w-5 h-5" />
          {t('EXPORT_TO_EXCEL')}
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        total={loaderData?.total || 0}
        data={loaderData?.subscriptionList || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="subscription"
        search={{
          defaultValue: loaderData?.filter?.search || '',
          name: 'search',
          placeholder: t('SEARCH_BY_SUB_ID'),
        }}
        filterDate={{
          name: 'rangeMonth',
          mode: 'range-month',
          defaultValue: {
            from: fromDate.startOf('day').toDate(),
            to: toDate.endOf('day').toDate(),
          },
        }}
      />
    </>
  );
}
