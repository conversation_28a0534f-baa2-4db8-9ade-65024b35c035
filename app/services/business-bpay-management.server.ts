import {
  BUSINESS_ACCOUNT_STATUS,
  BUSINESS_MEMBER_STATUS,
  BUSINESS_TRANSACTION_NAME,
  BUSINESS_TRANSACTION_TYPE,
} from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface BusinessTransactionProps {
  skip: PipelineStage.Skip['$skip'];
  limit: PipelineStage.Limit['$limit'];
  sort: PipelineStage.Sort['$sort'];
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
  };
  isoCode: string;
}

function getBPayTransactionFilterPipeline({
  filter,
  isoCode,
}: Pick<BusinessTransactionProps, 'filter' | 'isoCode'>): PipelineStage[] {
  const pipeline: PipelineStage[] = [
    {
      $match: {
        status: BUSINESS_ACCOUNT_STATUS.ACTIVE,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: '_id',
        foreignField: '_id',
        as: 'user_info',
      },
    },
    {
      $unwind: {
        path: '$user_info',
        preserveNullAndEmptyArrays: true,
      },
    },
    ...(filter.search
      ? [
          {
            $match: {
              $or: [
                {
                  'user_info.phone': {
                    $regex: filter.search,
                    $options: 'i',
                  },
                },
                {
                  name: {
                    $regex: filter.search,
                    $options: 'i',
                  },
                },
              ],
            },
          },
        ]
      : []),
  ];

  return pipeline;
}

const getListBPayOfBusinessAccountPipelines = ({
  filter,
  isoCode,
}: Pick<BusinessTransactionProps, 'filter' | 'isoCode'>) => {
  const pipelines = getBPayTransactionFilterPipeline({ filter, isoCode });

  return {
    commonPipelines: [
      ...pipelines,
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).businessTransaction,
          let: { businessId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$businessId', '$$businessId'] },
                    { $gte: ['$createdAt', filter.rangeDate.from] },
                    { $lte: ['$createdAt', filter.rangeDate.to] },
                  ],
                },
              },
            },
          ],
          as: 'businessTransactions',
        },
      },
      {
        $group: {
          _id: '$_id',
          totalTopUpBpayByBtaskee: {
            $sum: {
              $reduce: {
                input: '$businessTransactions',
                initialValue: 0,
                in: {
                  $cond: [
                    {
                      $eq: [
                        '$$this.name',
                        BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BTASKEE,
                      ],
                    },
                    { $add: ['$$value', '$$this.amount'] },
                    '$$value',
                  ],
                },
              },
            },
          },
          totalTopUpBpayMember: {
            $sum: {
              $reduce: {
                input: '$businessTransactions',
                initialValue: 0,
                in: {
                  $cond: [
                    {
                      $eq: [
                        '$$this.name',
                        BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_MEMBER,
                      ],
                    },
                    { $add: ['$$value', '$$this.amount'] },
                    '$$value',
                  ],
                },
              },
            },
          },
          totalRevokeBpayMember: {
            $sum: {
              $reduce: {
                input: '$businessTransactions',
                initialValue: 0,
                in: {
                  $cond: [
                    {
                      $eq: [
                        '$$this.name',
                        BUSINESS_TRANSACTION_NAME.REVOKE_BPAY_MEMBER,
                      ],
                    },
                    { $add: ['$$value', '$$this.amount'] },
                    '$$value',
                  ],
                },
              },
            },
          },
          businessName: { $first: '$name' },
          phone: { $first: '$user_info.phone' },
          address: { $first: '$user_info.address' },
        },
      },
    ],
    pipelinesAddingTotalMember: [
      {
        $lookup: {
          from: getCollectionNameByIsoCode(isoCode).businessMember,
          let: { businessMemberId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$businessId', '$$businessMemberId'] },
                    { $eq: ['$status', BUSINESS_MEMBER_STATUS.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: 'business_member_info',
        },
      },
      {
        $addFields: {
          totalMember: {
            $size: { $ifNull: ['$business_member_info', []] },
          },
        },
      },
    ],
  };
};

export async function getTotalBPayTransaction({
  filter,
  isoCode,
}: Pick<BusinessTransactionProps, 'filter' | 'isoCode'>): Promise<number> {
  const pipeline = getBPayTransactionFilterPipeline({ filter, isoCode });

  const result = await getModels(isoCode).business.aggregate([
    ...pipeline,
    { $count: 'total' },
  ]);

  return result[0]?.total || 0;
}

export async function getListBusinessTransaction({
  skip,
  limit,
  isoCode,
  filter,
  sort,
}: BusinessTransactionProps): Promise<{
  data: Array<
    BusinessTransaction &
      Pick<UserApp, 'phone'> & {
        businessName: AccountBusiness['name'];
        status: AccountBusiness['status'];
        phone: UserApp['phone'];
        address: UserApp['address'];
        totalRevokeBpayMember: BusinessTransaction['amount'];
        totalTopUpBpayByBtaskee: BusinessTransaction['amount'];
        totalTopUpBpayMember: BusinessTransaction['amount'];
        totalMember: number;
      }
  >;
  totalAmount: {
    totalTopUpBpayByBtaskeeCount: BusinessTransaction['amount'];
    totalTopUpBpayMemberCount: BusinessTransaction['amount'];
    totalRevokeBpayMemberCount: BusinessTransaction['amount'];
  }[];
}> {
  const pipelines = getListBPayOfBusinessAccountPipelines({
    filter,
    isoCode,
  });

  const result = await getModels(isoCode).business.aggregate([
    ...pipelines.commonPipelines,
    {
      $facet: {
        totalAmount: [
          {
            $group: {
              _id: null,
              totalTopUpBpayByBtaskeeCount: {
                $sum: '$totalTopUpBpayByBtaskee',
              },
              totalTopUpBpayMemberCount: { $sum: '$totalTopUpBpayMember' },
              totalRevokeBpayMemberCount: { $sum: '$totalRevokeBpayMember' },
            },
          },
        ],
        data: [
          { $sort: { ...sort, _id: -1 } },
          { $skip: skip },
          { $limit: limit },
          ...pipelines.pipelinesAddingTotalMember,
        ],
      },
    },
  ]);

  return result?.[0] || {};
}

export async function getDetailAccountBusinessById({
  isoCode,
  accountBusinessId,
}: {
  isoCode: IsoCode;
  accountBusinessId: UserApp['_id'];
}) {
  const accountBusiness = await getModels(isoCode)
    .business.findOne({
      _id: accountBusinessId,
    })
    .lean();

  if (!accountBusiness) {
    throw Error('ACCOUNT_NOT_FOUND');
  }

  const [user, totalMembers] = await Promise.all([
    getModels(isoCode)
      .users.findOne(
        {
          _id: accountBusinessId,
        },
        { phone: 1, address: 1, name: 1 },
      )
      .lean(),
    getModels(isoCode).businessMember.countDocuments({
      businessId: accountBusinessId,
      status: BUSINESS_ACCOUNT_STATUS.ACTIVE,
    }),
  ]);

  if (!user) {
    throw Error('USER_NOT_FOUND');
  }

  return {
    accountBusiness,
    askerData: {
      phone: user.phone,
      address: user.address,
      askerName: user.name,
    },
    totalMembers,
  };
}

export async function getBusinessLevels({
  isoCode,
  businessId,
}: {
  isoCode: string;
  businessId: string;
}) {
  const businessLevels = await getModels(isoCode)
    .businessLevel.find(
      {
        businessId,
      },
      { name: 1 },
    )
    .lean();

  return businessLevels || [];
}

interface BusinessMemberBPayProps extends BusinessTransactionProps {
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
    level: string;
  };
  businessId: string;
}

function getMemberBusinessBPayPipeline({
  filter,
  isoCode,
  businessId,
}: Pick<
  BusinessMemberBPayProps,
  'filter' | 'isoCode' | 'businessId'
>): PipelineStage[] {
  /*
  Deposit amount: type D and name TOP_UP_BPAY_BY_BUSINESS
  Usage amount: type C and name is not REVOKE_BPAY_BY_BUSINESS
  Reseted amount: type C and name REVOKE_BPAY_BY_BUSINESS
  */
  const pipeline: PipelineStage[] = [
    {
      $match: {
        createdAt: {
          $gte: filter.rangeDate.from,
          $lte: filter.rangeDate.to,
        },
        businessId,
      },
    },
    {
      $group: {
        _id: '$memberId',
        transactions: { $push: '$$ROOT' },
        depositedAmount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$type', BUSINESS_TRANSACTION_TYPE.D] },
                  {
                    $eq: [
                      '$name',
                      BUSINESS_TRANSACTION_NAME.TOP_UP_BPAY_BY_BUSINESS,
                    ],
                  },
                ],
              },
              '$amount',
              0,
            ],
          },
        },
        usageAmount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$type', BUSINESS_TRANSACTION_TYPE.C] },
                  {
                    $ne: [
                      '$name',
                      BUSINESS_TRANSACTION_NAME.REVOKE_BPAY_BY_BUSINESS,
                    ],
                  },
                ],
              },
              '$amount',
              0,
            ],
          },
        },
        resetedAmount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$type', BUSINESS_TRANSACTION_TYPE.C] },
                  {
                    $eq: [
                      '$name',
                      BUSINESS_TRANSACTION_NAME.REVOKE_BPAY_BY_BUSINESS,
                    ],
                  },
                ],
              },
              '$amount',
              0,
            ],
          },
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).businessMember,
        localField: '_id',
        foreignField: '_id',
        as: 'business_member_info',
      },
    },
    {
      $unwind: {
        path: '$business_member_info',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).businessLevel,
        localField: 'business_member_info.levelId',
        foreignField: '_id',
        as: 'business_level_info',
      },
    },
    {
      $unwind: {
        path: '$business_level_info',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'business_member_info.userId',
        foreignField: '_id',
        as: 'user_info',
      },
    },
    {
      $unwind: {
        path: '$user_info',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        ...(filter.level && {
          'business_member_info.levelId': { $in: filter.level.split(',') },
        }),
      },
    },
    ...(filter.search
      ? [
          {
            $match: {
              $or: [
                {
                  'user_info.phone': { $regex: filter.search, $options: 'i' },
                },
                {
                  'user_info.name': { $regex: filter.search, $options: 'i' },
                },
              ],
            },
          },
        ]
      : []),
  ];

  return pipeline;
}

export async function getTotalMemberBusinessBPay({
  filter,
  isoCode,
  businessId,
}: Pick<
  BusinessMemberBPayProps,
  'filter' | 'isoCode' | 'businessId'
>): Promise<number> {
  const pipeline = getMemberBusinessBPayPipeline({
    filter,
    isoCode,
    businessId,
  });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    { $count: 'total' },
  ]);

  return result[0]?.total || 0;
}

export async function getListMemberBusinessBPay({
  skip,
  sort,
  limit,
  isoCode,
  filter,
  businessId,
}: BusinessMemberBPayProps): Promise<{
  data: Array<
    Pick<BusinessTransaction, '_id'> & {
      name: UserApp['name'];
      phone: UserApp['phone'];
      level: BusinessLevel['name'];
      createdAt: BusinessTransaction['createdAt'];
      deposit: BusinessTransaction['amount'];
      depositedAmount: BusinessTransaction['amount'];
      usageAmount: BusinessTransaction['amount'];
      resetedAmount: BusinessTransaction['amount'];
    }
  >;
  totalAmount: Array<{
    totalDepositedAmount: BusinessTransaction['amount'];
    totalUsageAmount: BusinessTransaction['amount'];
    totalResetedAmount: BusinessTransaction['amount'];
  }>;
}> {
  const pipeline = getMemberBusinessBPayPipeline({
    filter,
    isoCode,
    businessId,
  });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    {
      $project: {
        name: '$user_info.name',
        phone: '$user_info.phone',
        level: '$business_level_info.name',
        createdAt: 1,
        depositedAmount: 1,
        usageAmount: 1,
        resetedAmount: 1,
      },
    },
    {
      $facet: {
        totalAmount: [
          {
            $group: {
              _id: null,
              totalDepositedAmount: {
                $sum: '$depositedAmount',
              },
              totalUsageAmount: { $sum: '$usageAmount' },
              totalResetedAmount: { $sum: '$resetedAmount' },
            },
          },
        ],
        data: [
          { $sort: { ...sort, _id: -1 } },
          { $skip: skip },
          { $limit: limit },
        ],
      },
    },
  ]);

  return result[0] || {};
}

export async function getUserInfo({
  isoCode,
  memberId,
}: {
  isoCode: string;
  memberId: string;
}): Promise<{
  name: UserApp['name'];
  phone: UserApp['phone'];
  level: BusinessLevel['name'];
  bPay: BusinessMember['bpay'];
}> {
  const memberInfo = await getModels(isoCode)
    .businessMember.findOne({ _id: memberId })
    .lean();

  const [userInfo, level] = await Promise.all([
    getModels(isoCode).users.findOne({
      _id: memberInfo?.userId,
    }),
    getModels(isoCode).businessLevel.findOne({
      _id: memberInfo?.levelId,
    }),
  ]);

  return {
    name: userInfo?.name || '',
    phone: userInfo?.phone || '',
    level: level?.name || '',
    bPay: memberInfo?.bPay || 0,
  };
}

interface MemberBPayHistoryProps extends BusinessTransactionProps {
  filter: {
    search: string;
    rangeDate: { from: Date; to: Date };
    name: string;
    type: string;
  };
  businessId: string;
  memberId: string;
}

function getMemberBPayTransactionsPipeline({
  filter,
  businessId,
  memberId,
  isoCode,
}: Pick<MemberBPayHistoryProps, 'filter' | 'isoCode' | 'businessId'> & {
  memberId: BusinessMember['_id'];
}): PipelineStage[] {
  const pipeline: PipelineStage[] = [
    {
      $match: {
        createdAt: {
          $gte: filter.rangeDate.from,
          $lte: filter.rangeDate.to,
        },
        ...(filter.name && { name: { $in: filter.name.split(',') } }),
        ...(filter.type && { type: { $in: filter.type.split(',') } }),
        ...(filter.search && { _id: filter.search }),
        businessId,
        memberId,
      },
    },
    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).business,
        localField: 'businessId',
        foreignField: '_id',
        as: 'business_info',
      },
    },
    {
      $unwind: { path: '$business_info', preserveNullAndEmptyArrays: true },
    },

    {
      $lookup: {
        from: getCollectionNameByIsoCode(isoCode).users,
        localField: 'business_info._id',
        foreignField: '_id',
        as: 'user_info',
      },
    },
    {
      $unwind: { path: '$user_info', preserveNullAndEmptyArrays: true },
    },
  ];

  return pipeline;
}

export async function getTotalMemberBPayTransactionsHistory({
  filter,
  isoCode,
  businessId,
  memberId,
}: Pick<
  MemberBPayHistoryProps,
  'filter' | 'isoCode' | 'businessId' | 'memberId'
>) {
  const pipeline = getMemberBPayTransactionsPipeline({
    filter,
    isoCode,
    businessId,
    memberId,
  });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    { $count: 'total' },
  ]);

  return result[0]?.total || 0;
}

export async function getListMemberBPayTransactionsHistory({
  isoCode,
  filter,
  businessId,
  memberId,
  skip,
  limit,
  sort,
}: MemberBPayHistoryProps & {
  projection?: PipelineStage.Project['$project'];
}): Promise<Array<BusinessTransaction>> {
  const pipeline = getMemberBPayTransactionsPipeline({
    businessId,
    memberId,
    filter,
    isoCode,
  });

  const result = await getModels(isoCode).businessMemberTransaction.aggregate([
    ...pipeline,
    { $sort: { ...sort, _id: 1 } },
    { $skip: skip },
    { $limit: limit },
    {
      $project: {
        name: 1,
        amount: 1,
        createdAt: 1,
        type: 1,
        reason: 1,
      },
    },
  ]);

  return result || [];
}

export async function getBPayOfBusinessAccountExportingExcelFile({
  filter,
  isoCode,
}: Pick<BusinessTransactionProps, 'filter' | 'isoCode'>) {
  const pipelines = getListBPayOfBusinessAccountPipelines({
    filter,
    isoCode,
  });

  const result = await getModels(isoCode).business.aggregate([
    ...pipelines.commonPipelines,
    ...pipelines.pipelinesAddingTotalMember,
  ]);

  return result ?? [];
}
