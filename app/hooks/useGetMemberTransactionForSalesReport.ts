import type { SerializeFrom } from '@remix-run/node';
import {
  BUSINESS_TRANSACTION_TYPE,
  SOURCE_NAME_FOR_FA_TRANSACTION,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';

type ReturnMemberTransactionFromQuery = Pick<
  BusinessMemberTransaction,
  '_id' | 'createdAt'
> & {
  task: Pick<
    Task,
    '_id' | 'costDetail' | 'newCostDetail' | 'promotion' | 'serviceText'
  >;
  faTransaction: Array<FATransaction>;
  business?: Pick<AccountBusiness, '_id' | 'name'>;
  businessUser?: Pick<UserApp, 'phone'>;
  asker: Pick<UserApp, 'name' | 'phone'>;
  tasker: Pick<UserApp, 'name' | 'phone'>;
};

interface UseGetMemberTransactionForSalesReport {
  transactions:
    | Array<ReturnMemberTransactionFromQuery>
    | SerializeFrom<Array<ReturnMemberTransactionFromQuery>>;
}

function useGetMemberTransactionForSalesReport({
  transactions,
}: UseGetMemberTransactionForSalesReport) {
  const getTaskPriceInfo = ({
    task,
    faTransaction,
  }: Pick<
    UseGetMemberTransactionForSalesReport['transactions'][0],
    'task' | 'faTransaction'
  >) => {
    const taskPrice = task?.newCostDetail?.cost ?? task?.costDetail?.cost ?? 0;

    const decreasedBtaskeePromotionReasonsOfTask =
      task?.costDetail?.decreasedReasons?.filter(
        decreasedReason =>
          decreasedReason.key === 'PROMOTION_CODE' &&
          decreasedReason.promotionBy === 'BTASKEE',
      ) ?? [];
    const promotionValue = decreasedBtaskeePromotionReasonsOfTask.reduce(
      (acc, currentVal) => acc + (currentVal?.value ?? 0),
      0,
    );

    const collectionFee =
      faTransaction?.reduce((acc, currentVal) => {
        if (
          currentVal?.source?.name === SOURCE_NAME_FOR_FA_TRANSACTION.TASK &&
          currentVal?.source?.value === task._id &&
          currentVal?.type === BUSINESS_TRANSACTION_TYPE.C
        ) {
          return acc + (currentVal?.amount ?? 0);
        }

        return acc;
      }, 0) ?? 0;

    const firstTransactionForTask = faTransaction.find(
      transaction =>
        transaction?.source?.name === SOURCE_NAME_FOR_FA_TRANSACTION.TASK &&
        transaction?.source?.value === task?._id &&
        transaction?.type === BUSINESS_TRANSACTION_TYPE.C,
    );

    if (!firstTransactionForTask?.date) {
      throw new Error(
        `[Transaction ID: ${firstTransactionForTask?._id}] Transaction date not found`,
      );
    }

    const mainAccountTasker = taskPrice - promotionValue;
    const customerPay = taskPrice - promotionValue;
    const promotionAccountTasker = promotionValue;

    return {
      taskPrice,
      promotionValue: Math.abs(promotionValue),
      /**
       * @note
       * The displayed time is one of the transaction timestamps
       * and may differ by a few seconds from data in other sources,
       * but this is acceptable for accounting purposes.
       */
      transactionDate: momentTz(firstTransactionForTask.date).toDate(),
      customerPay,
      collectionFee,
      mainAccountTasker,
      promotionAccountTasker: Math.abs(promotionAccountTasker),
    };
  };

  const formattedTransactions =
    transactions?.map(transaction => ({
      ...transaction,
      ...getTaskPriceInfo(transaction),
    })) ?? [];

  return {
    transactions: formattedTransactions,
  };
}

export default useGetMemberTransactionForSalesReport;
