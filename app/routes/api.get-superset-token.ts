import { json } from '@remix-run/node';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { SUPERSET_CONFIG } from '~/config/superset';

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // For now, return the configured guest token
    // In production, you might want to generate a fresh token from Superset API
    const token = SUPERSET_CONFIG.GUEST_TOKEN;
    
    if (!token) {
      throw new Error('Guest token not configured');
    }

    return json({ 
      token,
      success: true 
    });
  } catch (error) {
    console.error('Error fetching Superset guest token:', error);
    
    return json(
      { 
        error: 'Failed to fetch guest token',
        success: false 
      },
      { status: 500 }
    );
  }
}
