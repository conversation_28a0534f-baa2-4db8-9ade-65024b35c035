import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';
import {
  useLoaderPaymentManagement,
  willBecomeLoader,
} from '~/hooks/usePaymentManagement';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.PAYMENT_MANAGEMENT}
      label="PAYMENT_MANAGEMENT"
    />
  ),
};

export const loader = hocLoader(
  willBecomeLoader,
  PERMISSIONS.READ_PAYMENT_MANAGEMENT,
);

export default function PaymentManagementScreen() {
  const loaderData = useLoaderPaymentManagement();

  return <Outlet context={loaderData} />;
}
