import { DialogPortal } from '@radix-ui/react-dialog';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  redirect,
  useActionData,
  useNavigation,
  useRouteError,
} from '@remix-run/react';
import { ACTION_NAME, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BtaskeeResponseError,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  LoadingSpinner,
  MultiSelectAdvance,
  toast,
  useBtaskeeFormController,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useOutletCreateAndAddMemberIntoGroup } from '~/hooks/useGetGroupDetail';
import i18next from '~/i18next.server';
import { getCities, getUserSession } from '~/services/helpers.server';
import { addUserToGroupByEmail } from '~/services/role-base-access-control.server';
import { commitSession, getSession } from '~/services/session.server';
import { createNewUser } from '~/services/settings.server';

export const loader = hocLoader(async ({ request }: LoaderFunctionArgs) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const cities = await getCities(isoCode);

  return json({ cities });
});

export const action = hocAction(
  async ({ params, request }, { setInformationActionHistory }) => {
    const { isoCode } = await getUserSession({ headers: request.headers });
    const formData = await request.clone().formData();

    const username = formData.get('username')?.toString() || '';
    const name = formData.get('name')?.toString() || '';
    const email = formData.get('email')?.toString() || '';
    const cities = JSON.parse(formData.get('cities')?.toString() || '') || [];
    const groupName = formData.get('groupName')?.toString() || '';

    const newUser = await createNewUser({
      username,
      name,
      email,
      isoCode,
      cities,
    });

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_USER,
      dataRelated: { userId: newUser?._id },
    });

    await addUserToGroupByEmail({
      email,
      groupId: params.id || '',
      groupName,
    });

    setInformationActionHistory({
      action: ACTION_NAME.ADD_MEMBER_INTO_GROUP,
      dataRelated: {
        groupId: params.id,
        userId: params.userId || '',
      },
    });

    const session = await getSession(request.headers.get('cookie'));

    const tUserSetting = await i18next.getFixedT(request, 'user-settings');

    session.flash(
      'flashMessage',
      tUserSetting('NEW_MEMBER_SUCCESSFULLY_CREATED'),
    );

    const newSession = await commitSession(session);

    return redirect(
      `${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list/add-member-dialog`,
      {
        headers: {
          'Set-Cookie': newSession,
        },
      },
    );
  },
);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CreateAndAddMemberDialog() {
  const { t: tUserSettings } = useTranslation('user-settings');
  const transition = useNavigation();

  const { cities } = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const parentContext = useOutletCreateAndAddMemberIntoGroup();

  const { form, onSubmit } = useBtaskeeFormController({
    zodRaw: {
      username: z.string().min(1, tUserSettings('USERNAME_IS_REQUIRED')),
      name: z.string().min(1, tUserSettings('MEMBER_NAME_IS_REQUIRED')),
      email: z.string().email(tUserSettings('INVALID_EMAIL_FORMAT')),
      cities: z
        .array(z.any())
        .min(1, tUserSettings('MUST_HAVE_AT_LEAST_ONE_CITY')),
      groupName: z.string(),
    },
    defaultValues: {
      username: '',
      name: '',
      email: '',
      cities: [],
      groupName: parentContext.groupName,
    },
    confirmParams: {
      title: tUserSettings('CONFIRM'),
      body: tUserSettings('ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT'),
      actionButton: tUserSettings('SUBMIT'),
      cancelButton: tUserSettings('CANCEL'),
    },
    formDataProvided: data => {
      const formData = new FormData();

      formData.append('username', data.username);
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('cities', JSON.stringify(data.cities));
      formData.append('groupName', data.groupName);

      return formData;
    },
  });

  const { control, handleSubmit, formState } = form;

  return (
    <Dialog
      open={parentContext.openDialogAddAndCreateMember}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogPortal>
        <DialogContent className="max-h-[90vh] max-w-[548px] overflow-y-auto">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-2xl font-semibold tracking-tighter">
              {tUserSettings('ADD_MEMBER')}
            </DialogTitle>
            <DialogDescription>
              {tUserSettings('CREATE_AND_ADD_MEMBER_BY_EMAIL_DESCRIPTION')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3">
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-2">
                <FormField
                  name="username"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel>{tUserSettings('USERNAME')}</FormLabel>
                      <FormControl>
                        <Input
                          defaultValue={value}
                          onChange={onChange}
                          placeholder={tUserSettings('ENTER_USERNAME')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  name="name"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-[#0F172A]">
                        {tUserSettings('MEMBER_NAME')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          defaultValue={value}
                          onChange={onChange}
                          placeholder={tUserSettings('ENTER_MEMBER_NAME')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  name="email"
                  control={control}
                  rules={{
                    required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                    pattern: {
                      // Regular expression pattern to validate the email format
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: tUserSettings('INVALID_EMAIL_FORMAT'),
                    },
                  }}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-[#0F172A]">
                        {tUserSettings('EMAIL')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          defaultValue={value}
                          onChange={onChange}
                          placeholder={tUserSettings('ENTER_EMAIL')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  name="cities"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel>{tUserSettings('CITIES')}</FormLabel>
                      <FormControl>
                        <MultiSelectAdvance
                          options={cities?.map(city => ({
                            label: city,
                            value: city,
                          }))}
                          onValueChange={onChange}
                          defaultValue={value}
                          placeholder={tUserSettings('SELECT_CITY')}
                          variant="blue"
                          maxCount={7}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-4 pt-12">
                  <Button
                    className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                    type="button"
                    variant="outline"
                    onClick={parentContext.onClose}>
                    {tUserSettings('CANCEL')}
                  </Button>
                  <Button
                    variant="default"
                    type="submit"
                    disabled={
                      !formState.isValid || transition.state === 'submitting'
                    }>
                    {transition.state === 'submitting' ? (
                      <LoadingSpinner />
                    ) : (
                      tUserSettings('CONFIRM')
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
          <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
