rules:
  - id: no-fractional-cpu-limits
    patterns:
      - pattern-inside: |
          limits:
            ...
      - pattern: |
          cpu: $CPU_LIMIT
      - metavariable-regex:
          metavariable: $CPU_LIMIT
          regex: \d{0,3}m
    fix: "cpu: 1000m"
    message: When you set a fractional CPU limit on a container, the CPU cycles
      available will be throttled, even though most nodes can handle processes
      alternating between using 100% of the CPU.
    severity: WARNING
    languages:
      - yaml
    metadata:
      category: best-practice
      technology:
        - kubernetes
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: flask-debugging-enabled
    languages:
      - yaml
    severity: WARNING
    message: Do not set FLASK_ENV to "development" since that sets `debug=True` in
      Flask. Use "dev" or a similar term instead.
    metadata:
      owasp: A06:2017 - Security Misconfiguration
      cwe:
        - "CWE-489: Active Debug Code"
      references:
        - https://flask.palletsprojects.com/en/2.0.x/debugging/
        - https://flask.palletsprojects.com/en/2.0.x/config/#ENV
      category: security
      technology:
        - kubernetes
        - flask
      subcategory:
        - audit
      likelihood: LOW
      impact: MEDIUM
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Active Debug Code
    patterns:
      - pattern-inside: |
          env: [...]
      - pattern: |
          {name: FLASK_ENV, value: "development"}
    fix-regex:
      regex: development
      replacement: dev

  - id: exposing-docker-socket-hostpath
    patterns:
      - pattern-inside: |
          volumes:
            ...
      - pattern: |
          hostPath:
            ...
            path: /var/run/docker.sock
    message: Exposing host's Docker socket to containers via a volume. The owner of
      this socket is root. Giving someone access to it is equivalent to giving
      unrestricted root access to your host. Remove 'docker.sock' from hostpath
      to prevent this.
    metadata:
      cwe:
        - "CWE-250: Execution with Unnecessary Privileges"
      references:
        - https://kubernetes.io/docs/concepts/storage/volumes/#hostpath
        - https://kubernetes.io/docs/concepts/policy/pod-security-policy/#volumes-and-file-systems
        - https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
        - https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-1-do-not-expose-the-docker-daemon-socket-even-to-the-containers
      category: security
      technology:
        - kubernetes
      subcategory:
        - vuln
      likelihood: LOW
      impact: HIGH
      confidence: MEDIUM
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING

  - id: run-as-non-root
    patterns:
      - pattern-inside: |
          $SPEC:
            ...
            containers:
              ...
            ...
      - metavariable-regex:
          metavariable: $SPEC
          regex: ^(spec)$
      - pattern-not-inside: |
          spec:
            ...
            securityContext:
              ...
            ...
      - pattern-inside: |
          $SPEC:
            ...
            containers:
              ...
      - pattern-not-inside: |
          $SPEC:
            ...
            containers:
              ...
              - name: $NAME
                image: ...
                ...
                securityContext:
                  ...
                  runAsNonRoot: $VALUE
      - focus-metavariable: $SPEC
    fix: |
      $SPEC:
        securityContext:
          runAsNonRoot: true #
    message: When running containers in Kubernetes, it's important to ensure that
      they  are properly secured to prevent privilege escalation attacks.  One
      potential vulnerability is when a container is allowed to
      run  applications as the root user, which could allow an attacker to
      gain  access to sensitive resources. To mitigate this risk, it's
      recommended to  add a `securityContext` to the container, with the
      parameter `runAsNonRoot`  set to `true`. This will ensure that the
      container runs as a non-root user,  limiting the damage that could be
      caused by any potential attacks. By  adding a `securityContext` to the
      container in your Kubernetes pod, you can  help to ensure that your
      containerized applications are more secure and  less vulnerable to
      privilege escalation attacks.
    metadata:
      references:
        - https://kubernetes.io/blog/2016/08/security-best-practices-kubernetes-deployment/
        - https://kubernetes.io/docs/concepts/policy/pod-security-policy/
        - https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-2-set-a-user
      category: security
      cwe:
        - "CWE-250: Execution with Unnecessary Privileges"
      owasp:
        - A05:2021 - Security Misconfiguration
        - A06:2017 - Security Misconfiguration
      technology:
        - kubernetes
      subcategory:
        - audit
      likelihood: LOW
      impact: LOW
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: INFO

  - id: writable-filesystem-container
    patterns:
      - pattern-inside: |
          containers:
            ...
      - pattern-inside: |
          - name: $CONTAINER
            ...
      - pattern: |
          image: ...
          ...
      - pattern-not: |
          image: ...
          ...
          securityContext:
            ...
            readOnlyRootFilesystem: true
      - focus-metavariable: $CONTAINER
    message: "Container $CONTAINER is running with a writable root filesystem. This
      may allow malicious applications to download and run additional payloads,
      or modify container files. If an application inside a container has to
      save something temporarily consider using a tmpfs. Add
      'readOnlyRootFilesystem: true' to this container to prevent this."
    metadata:
      cwe:
        - "CWE-732: Incorrect Permission Assignment for Critical Resource"
      owasp:
        - A05:2021 - Security Misconfiguration
        - A06:2017 - Security Misconfiguration
      references:
        - https://kubernetes.io/docs/concepts/policy/pod-security-policy/#volumes-and-file-systems
        - https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
        - https://blog.atomist.com/security-of-docker-kubernetes/
        - https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only
      category: security
      technology:
        - kubernetes
      cwe2021-top25: true
      subcategory:
        - audit
      likelihood: LOW
      impact: MEDIUM
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING
