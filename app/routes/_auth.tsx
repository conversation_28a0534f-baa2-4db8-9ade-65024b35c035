import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { AccountantLoginLogo } from 'btaskee-ui';
import { authenticator } from '~/services/passports.server';

export async function loader({ request }: LoaderFunctionArgs) {
  return await authenticator.isAuthenticated(request, {
    successRedirect: '/',
  });
}

export default function Screen() {
  return (
    <div className="container relative flex h-screen flex-col justify-center bg-[url('@/images/login-background-mobile.svg')] bg-cover bg-center bg-no-repeat lg:grid lg:max-w-none lg:grid-cols-4 lg:items-center lg:bg-none lg:px-0 xl:grid-cols-6">
      <div className="absolute left-[50%] top-4 col-span-2 translate-x-[-50%] bg-cover bg-center bg-no-repeat p-6 text-white lg:relative lg:top-0 lg:flex lg:h-full lg:flex-col lg:bg-[url('@/images/login-background.svg')] xl:col-span-4">
        <div className="relative z-20 flex items-center text-lg font-medium w-[240px]">
          <AccountantLoginLogo />
        </div>
      </div>
      <div className="col-span-2 lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-8 sm:w-[350px]">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
