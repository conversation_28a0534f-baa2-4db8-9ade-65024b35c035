import { DialogPortal } from '@radix-ui/react-dialog';
import { HoverCardPortal } from '@radix-ui/react-hover-card';
import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  json,
  useNavigation,
  useOutletContext,
  useParams,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  ADD_MEMBER_TAB_FOR_GROUP,
  COMMON_LOCAL_STORAGE_KEY,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  ActivityLogo,
  BTaskeeTableV2,
  BtaskeeResponseError,
  Button,
  Checkbox,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  GraphLogo,
  Grid,
  HeartLogo,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
  LoadingSpinner,
  SendLogo,
  Separator,
  StarLogo,
  Tabs,
  TabsList,
  TabsTrigger,
  Typography,
  VectorEmptyDataTable,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
} from 'btaskee-utils';
import debounce from 'lodash/debounce.js';
import { Eye } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getListMemberAddingToGroup,
  getTotalMemberAddingToGroup,
} from '~/services/role-base-access-control.server';
import { getSession } from '~/services/session.server';

export const loader = hocLoader(async ({ request, params }) => {
  const searchParams = new URL(request.url).searchParams;
  const { isoCode } = await getUserSession({ headers: request.headers });

  const [{ searchText }, { pageIndex, pageSize }] = getValuesFromSearchParams(
    searchParams,
    {
      keysString: ['searchText'],
      keysNumber: ['pageIndex', 'pageSize'],
    },
  );

  const filteredMember = {
    searchText,
  };

  const totalMember = await getTotalMemberAddingToGroup({
    groupId: params?.id ?? '',
    isoCode,
    filter: filteredMember,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total: totalMember,
      pageSize,
      pageIndex,
    }),
  );

  const [listMember, session] = await Promise.all([
    getListMemberAddingToGroup({
      groupId: params?.id ?? '',
      isoCode,
      filter: filteredMember,
      limit,
      skip,
    }),
    getSession(request.headers.get('cookie')),
  ]);

  const flashMessage = await session.get('flashMessage');

  return json({
    filteredMember,
    totalMember,
    listMember,
    flashMessage,
  });
});

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function AddMemberDialog() {
  const { t: tAddMember } = useTranslation('add-member-into-system');
  const [searchParams] = useSearchParams();
  const submit = useSubmit();
  const confirm = useConfirm();
  const params = useParams();
  const transition = useNavigation();
  const { totalMember, filteredMember, listMember, flashMessage } =
    useLoaderDataSafely<typeof loader>();
  const [selectedUserIds, setSelectedUserIds] = useState<Array<Users['_id']>>(
    [],
  );
  const [selectedListTab, setSelectedListTab] = useState<string>();
  const [searchValue, setSearchValue] = useState<string>('');

  const updateLocalStorageForAddingMemberToGroup = (values: {
    selectedTab?: string;
    selectedUserIds?: string;
  }) => {
    const localStorageValue = JSON.parse(
      window.localStorage.getItem(
        COMMON_LOCAL_STORAGE_KEY.ADD_MEMBER_TO_GROUP_FOR_RBAC,
      ) ?? '{}',
    );

    window.localStorage.setItem(
      COMMON_LOCAL_STORAGE_KEY.ADD_MEMBER_TO_GROUP_FOR_RBAC,
      JSON.stringify({ ...localStorageValue, ...values }),
    );
  };

  const filteredListMember = useMemo(() => {
    const isSelectedMemberTab =
      selectedListTab === ADD_MEMBER_TAB_FOR_GROUP.SELECTED_MEMBER;

    return isSelectedMemberTab
      ? listMember.filter(member => selectedUserIds.includes(member?._id))
      : listMember;
  }, [listMember, selectedListTab, selectedUserIds]);

  useEffect(() => {
    const localStorageValue = JSON.parse(
      window.localStorage.getItem(
        COMMON_LOCAL_STORAGE_KEY.ADD_MEMBER_TO_GROUP_FOR_RBAC,
      ) ?? '{}',
    );

    if (localStorageValue?.selectedUserIds) {
      setSelectedUserIds(localStorageValue?.selectedUserIds?.split(','));
    }

    if (localStorageValue?.selectedTab) {
      setSelectedListTab(
        localStorageValue.selectedTab ?? ADD_MEMBER_TAB_FOR_GROUP.MEMBER_LIST,
      );
    }
  }, []);

  useEffect(() => {
    if (flashMessage) {
      toast({
        variant: 'success',
        description: flashMessage,
      });
    }
  }, [flashMessage]);

  const parentContext = useOutletContext<{
    openDialogAddMember: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
  }>();

  const iconType = useMemo(
    () => [
      <ActivityLogo key="activity" className="h-11 w-11" />,
      <GraphLogo key="graph" className="h-11 w-11" />,
      <HeartLogo key="heart" className="h-11 w-11" />,
      <SendLogo key="send" className="h-11 w-11" />,
      <StarLogo key="star" className="h-11 w-11" />,
    ],
    [],
  );

  const submitAddingMember = async () => {
    const isConfirm = await confirm({
      title: tAddMember('TITLE_CONFIRM_ADDING_MEMBER'),
      body: tAddMember('DESCRIPTION_CONFIRM_ADDING_MEMBER'),
      actionButton: tAddMember('CONFIRM'),
      cancelButton: tAddMember('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('userIds', selectedUserIds.join(','));
      formData.append('actionName', ACTION_NAME.ADD_MEMBER_INTO_GROUP);

      submit(formData, { method: 'post' });
    }
  };

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListMemberAddingToGroup>[0]
    >
  >[] = useMemo(
    () => [
      {
        id: 'select',
        header: () => (
          <Checkbox
            onCheckedChange={isChecked => {
              if (isChecked) {
                setSelectedUserIds(
                  filteredListMember.map(member => member._id),
                );
                updateLocalStorageForAddingMemberToGroup({
                  selectedUserIds: filteredListMember
                    .map(member => member._id)
                    ?.join(','),
                });
              } else {
                setSelectedUserIds([]);
                updateLocalStorageForAddingMemberToGroup({
                  selectedUserIds: '',
                });
              }
            }}
            checked={
              selectedUserIds?.length
                ? filteredListMember.every(member =>
                    selectedUserIds.includes(member._id),
                  )
                : false
            }
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        size: 32,
        cell: ({ row }) => (
          <Checkbox
            checked={selectedUserIds.includes(row.original?._id)}
            onCheckedChange={isChecked => {
              setSelectedUserIds(userIds => {
                const updatedUserIds = isChecked
                  ? [...userIds, row.original?._id]
                  : userIds.filter(userId => userId !== row.original?._id);

                updateLocalStorageForAddingMemberToGroup({
                  selectedUserIds: updatedUserIds.join(','),
                });

                return updatedUserIds;
              });
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={tAddMember('EMAIL')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p" className="whitespace-nowrap">
            {tAddMember(row.original?.email ?? '')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'memberName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tAddMember('MEMBER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p" className="whitespace-nowrap">
            {tAddMember(row.original?.name ?? '')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'group',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={tAddMember('GROUP')} />
        ),
        cell: ({ row }) =>
          row.original?.groups?.length ? (
            <HoverCard>
              <HoverCardTrigger asChild>
                <div className="!mt-4 flex w-fit items-center gap-2 rounded-md bg-gray-100 px-2 py-1">
                  <Eye width={14} height={14} />
                  <Typography variant="p" affects="removePMargin">
                    {tAddMember('MEMBER_HAS_PARTICIPATED')}
                    <span className="font-bold text-primary">
                      {row.original.groups.length}
                    </span>{' '}
                    {tAddMember('NUMBER_OF_GROUP')}
                  </Typography>
                </div>
              </HoverCardTrigger>
              <HoverCardPortal>
                <HoverCardContent className="w-80">
                  {row.original.groups.map((group, index) => (
                    <>
                      <div key={group?._id}>
                        <div className="flex items-center gap-2 rounded-md bg-gray-50 p-2">
                          <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
                            {group?.iconType
                              ? iconType[group?.iconType]
                              : iconType[index % iconType.length]}
                          </div>
                          <Typography
                            variant="p"
                            affects="removePMargin"
                            className="line-clamp-1 break-all text-base font-medium">
                            {group?.name}
                          </Typography>
                        </div>
                      </div>
                      {row.original.groups.length - 1 > index ? (
                        <Separator className="my-4 w-full" />
                      ) : null}
                    </>
                  ))}
                </HoverCardContent>
              </HoverCardPortal>
            </HoverCard>
          ) : null,
        enableSorting: false,
      },
    ],
    [filteredListMember, iconType, selectedUserIds, tAddMember],
  );

  return (
    <Dialog
      open={parentContext.openDialogAddMember}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogPortal>
        <DialogContent
          className="min-w-[1000px] overflow-y-auto"
          onInteractOutside={parentContext.onClose}>
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-2xl font-semibold tracking-tighter">
              {tAddMember('ADD_MEMBER')}
            </DialogTitle>
            <DialogDescription>
              {tAddMember('ADD_MEMBER_BY_EMAIL_DESCRIPTION')}
            </DialogDescription>
          </DialogHeader>

          {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
          {/* @ts-ignore */}
          <BTaskeeTableV2
            columns={columns}
            data={filteredListMember}
            total={totalMember ?? 0}
            translationKey="add-member-into-system"
            searchInput={{
              defaultValue: filteredMember?.searchText ?? '',
              name: 'searchText',
              placeholder: tAddMember('SEARCH_BY_EMAIL_OR_NAME'),
              onChange: debounce(event => {
                setSearchValue(event?.target?.value ?? '');
              }, 500),
            }}
            renderExtraComponent={[
              {
                position: 'top',
                component: (
                  <Tabs
                    value={
                      selectedListTab ?? ADD_MEMBER_TAB_FOR_GROUP.MEMBER_LIST
                    }
                    onValueChange={tabValue => {
                      setSelectedListTab(tabValue);

                      updateLocalStorageForAddingMemberToGroup({
                        selectedTab: tabValue,
                      });
                    }}>
                    <TabsList>
                      <TabsTrigger value={ADD_MEMBER_TAB_FOR_GROUP.MEMBER_LIST}>
                        {tAddMember('MEMBER_LIST_TAB')}
                      </TabsTrigger>
                      <TabsTrigger
                        value={ADD_MEMBER_TAB_FOR_GROUP.SELECTED_MEMBER}>
                        {tAddMember('SELECTED_TAB')}
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                ),
              },
            ]}
            emptyDataComponent={
              searchValue ? (
                <div className="my-10">
                  <div className="flex justify-center">
                    <VectorEmptyDataTable />
                  </div>
                  <Grid className="flex justify-center gap-1">
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-sm font-normal">
                      {tAddMember('ADD_NEW_MEMBER_DESCRIPTION')}
                    </Typography>
                    <Link
                      to={`${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list/create-and-add-member-dialog`}
                      className="font-medium text-primary underline">
                      {tAddMember('ADD_NEW_MEMBER_LINK')}
                    </Link>
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-sm font-normal">
                      {tAddMember('ADD_NEW_MEMBER_QUESTION')}
                    </Typography>
                  </Grid>
                </div>
              ) : undefined
            }
            pagination={getPageSizeAndPageIndex({
              total: totalMember ?? 0,
              pageSize: Number(searchParams.get('pageSize') ?? 10),
              pageIndex: Number(searchParams.get('pageIndex') ?? 0),
            })}
          />

          <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
          <div className="flex justify-end gap-4 pt-12">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={parentContext.onClose}>
              {tAddMember('CANCEL')}
            </Button>
            <Button
              variant="default"
              onClick={submitAddingMember}
              disabled={transition.state === 'submitting'}>
              {transition.state === 'submitting' ? (
                <LoadingSpinner />
              ) : (
                tAddMember('CONFIRM')
              )}
            </Button>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
