import {
  COMMON_MIN_PHONE_LENGTH,
  FA_TRANSACTION_TYPE,
  RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME,
  SOURCE_NAME_FOR_FA_TRANSACTION,
  TASK_STATUS,
} from 'btaskee-constants';
import { getAccountingTransactionClosingMongoFilter, momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';

const getRangeDateFromFilter = ({
  filterOption,
  date,
}: {
  filterOption: string;
  date: Date;
}) => {
  const inputDate = new Date(date);

  if (
    filterOption === RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH
  ) {
    const startOfMonth = new Date(
      inputDate.getFullYear(),
      inputDate.getMonth(),
      1,
    );
    const endOfMonth = new Date(
      inputDate.getFullYear(),
      inputDate.getMonth() + 1,
      0,
      23,
      59,
      59,
      999,
    );

    return {
      filteredFromDate: startOfMonth,
      filteredToDate: endOfMonth,
    };
  }

  if (
    filterOption === RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_YEAR
  ) {
    const startOfYear = new Date(inputDate.getFullYear(), 0, 1);
    const endOfYear = new Date(
      inputDate.getFullYear(),
      11,
      31,
      23,
      59,
      59,
      999,
    );

    return {
      filteredFromDate: startOfYear,
      filteredToDate: endOfYear,
    };
  }

  return {
    filteredFromDate: inputDate,
    filteredToDate: inputDate,
  };
};

export async function getTaskIncomeTaxTransactionsByTaskerPhone({
  taskerPhone,
  date,
  isoCode,
  filterOption,
}: {
  taskerPhone: string;
  date: Date;
  isoCode: IsoCode;
  filterOption: string;
}) {
  const defaultQueryingResponse = {
    taskerInfo: {
      name: '',
      phone: '',
      status: '',
    },
    reportingTasks: [],
    reportingCompensations: [],
    reportingReductionTransactions: [],
    warningMessage: '',
  };

  if (taskerPhone?.length < COMMON_MIN_PHONE_LENGTH) {
    return defaultQueryingResponse;
  }

  const settingSystem = await getModels(isoCode)
    .settingSystem.findOne({}, { tester: 1 })
    .lean<{ tester: string[] }>();

  if (settingSystem && settingSystem?.tester?.includes(taskerPhone)) {
    return {
      ...defaultQueryingResponse,
      warningMessage: 'THIS_IS_TESTER_ACCOUNT',
    };
  }

  const taskerFoundByPhone = await getModels(isoCode)
    .users.findOne({ phone: taskerPhone })
    .select({ _id: 1, name: 1, phone: 1, status: 1 })
    .lean();

  if (!date) {
    return defaultQueryingResponse;
  }

  if (!taskerFoundByPhone?._id) {
    return defaultQueryingResponse;
  }

  const transactions = await getModels(isoCode)
    .FATransaction.find({
      $or: [
        {
          'source.name': SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
          type: FA_TRANSACTION_TYPE.DEPOSIT,
        },
        {
          'source.name': {
            $in: [
              SOURCE_NAME_FOR_FA_TRANSACTION.INCOME_TAX,
              'TASKER_INCOME_REDUCTION',
            ],
          },
          type: FA_TRANSACTION_TYPE.CREDIT,
        },
      ],
          createdAt: getAccountingTransactionClosingMongoFilter(
        getRangeDateFromFilter({
          filterOption,
          date,
        }),
      ),
      userId: taskerFoundByPhone._id,
    })
    .lean();

  if (!transactions?.length) {
    return defaultQueryingResponse;
  }

  const reportingReductionTransactions = transactions.filter(
    transaction => transaction?.source?.name === 'TASKER_INCOME_REDUCTION',
  );

  const incomeTaxTransactions = transactions.filter(
    transaction =>
      transaction?.source?.name === SOURCE_NAME_FOR_FA_TRANSACTION.INCOME_TAX,
  );
  const incomeTaxableTransactions = await getModels(isoCode)
    .FATransaction.find({
      _id: incomeTaxTransactions.map(
        transaction => transaction?.source?.value ?? '',
      ),
    })
    .lean();

  const taskIds = transactions
    .filter(
      transaction =>
        transaction?.source?.taskId &&
        transaction?.source?.name === SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
    )
    .map(transaction => transaction?.source?.taskId ?? '');
  const doneTasks = await getModels(isoCode)
    .task.find({
      _id: { $in: taskIds },
      status: TASK_STATUS.DONE,
    })
    .lean();

  return {
    ...defaultQueryingResponse,
    taskerInfo: taskerFoundByPhone,
    reportingTasks:
      doneTasks
        ?.map(task => {
          const taskerIncomeIntask = task?.taskerIncome?.find(
            taskerIncome => taskerIncome?.taskerId === taskerFoundByPhone._id,
          );

          const taskerIncomeTaxableAmount = taskerIncomeIntask?.income ?? 0;
          const taskerIncomeTaxAmount = taskerIncomeIntask?.incomeTax ?? 0;

          return {
            taskId: task._id,
            doneTaskDate: task?.changesHistory?.find(
              change => change.key === 'DONE_TASK',
            )?.createdAt,
            taskerIncomeTaxableAmount,
            taskerIncomeTaxAmount,
            taskerActualIncomeAmount:
              taskerIncomeTaxableAmount - taskerIncomeTaxAmount,
          };
        })
        .sort((a, b) => {
          if (!a?.doneTaskDate || !b?.doneTaskDate) return 0;
          return (
            new Date(b.doneTaskDate).getTime() -
            new Date(a.doneTaskDate).getTime()
          );
        }) ?? [],
    reportingCompensations:
      incomeTaxableTransactions?.map(incomeTaxableTransaction => ({
        incomeTaxableTransaction,
        incomeTaxTransaction: incomeTaxTransactions.find(
          transaction =>
            transaction?.source?.value === incomeTaxableTransaction?._id,
        ),
      })) ?? [],
    reportingReductionTransactions,
  };
}

export async function getListTaskerIncomeTaxGeneralReport({
  date,
  filterOption,
  isoCode,
}: {
  date: Date;
  filterOption: string;
  isoCode: IsoCode;
}) {
  if (!date || !filterOption) {
    return [];
  }

  const reportMatcher: MustBeAny = {
    year: date.getFullYear(),
  };

  if (
    filterOption === RANGE_DATE_MODE_FOR_ACCOUNTING_MONTHLY_INCOME.RANGE_MONTH
  ) {
    reportMatcher.month = date.getUTCMonth() + 1;
  }

  const [reportTaskerIncomes, settingSystem] = await Promise.all([
    getModels(isoCode).reportTaskerIncomeTax.find(reportMatcher).lean(),
    getModels(isoCode)
      .settingSystem.find({})
      .select({ tester: 1 })
      .lean<{ tester: string[] }>(),
  ]);

  if (!reportTaskerIncomes?.length) {
    return [];
  }

  const taskerIds = reportTaskerIncomes.map(
    reportTaskerIncome => reportTaskerIncome?.userId ?? '',
  );

  const [taskerProfiles, reportedTaskers] = await Promise.all([
    getModels(isoCode)
      .taskerProfile.find({
        taskerId: { $in: taskerIds },
      })
      .lean(),
    getModels(isoCode)
      .users.find({
        _id: { $in: taskerIds },
        ...(settingSystem?.tester?.length
          ? { phone: { $nin: settingSystem?.tester } }
          : {}),
      })
      .lean(),
  ]);

  if (!reportedTaskers?.length) {
    return [];
  }

  return reportedTaskers.map(tasker => {
    const taskerProfile = taskerProfiles.find(
      profile => profile?.taskerId === tasker?._id,
    );
    const reportIncomeTax = reportTaskerIncomes.filter(
      report => report?.userId === tasker?._id,
    );

    const amountInfo = reportIncomeTax.reduce(
      (acc, currentVal) => {
        const currentIncome = currentVal?.income ?? 0;
        const currentReward = currentVal?.reward ?? 0;
        const currentIncomeDeduction = currentVal?.incomeReduction ?? 0;

        const totalIncomeAmount =
          currentIncome + currentReward - currentIncomeDeduction;

        return {
          totalIncomeAmount: acc.totalIncomeAmount + totalIncomeAmount,
          totalIncomeTaxAmount:
            acc.totalIncomeTaxAmount + (currentVal?.incomeTax ?? 0),
          totalIncomeAfterTax:
            acc.totalIncomeAfterTax +
            totalIncomeAmount -
            (currentVal?.incomeTax ?? 0),
        };
      },
      {
        totalIncomeAmount: 0,
        totalIncomeTaxAmount: 0,
        totalIncomeAfterTax: 0,
      },
    );

    return {
      name: taskerProfile?.vneID?.name ?? '',
      dob: taskerProfile?.vneID?.idDob ?? '',
      taskerId: tasker?._id ?? '',
      gender: tasker?.gender ?? '',
      country: taskerProfile?.vneID?.nationality ?? '',
      identityCardNumber: taskerProfile?.vneID?.idNumber ?? '',
      identityCardIssuedDate: taskerProfile?.vneID?.issueDate ?? '',
      identityCardIssuedPlace: taskerProfile?.vneID?.placeOfIssue ?? '',
      phoneNumberInIdentityCard: taskerProfile?.vneID?.idPhone ?? '',
      placeOfResidence: taskerProfile?.vneID?.placeOfResidence ?? '',
      temporaryAddress: taskerProfile?.vneID?.temporaryAddress ?? '',
      ethnicGroup: taskerProfile?.vneID?.ethnicGroup ?? '',
      contactPhone: tasker?.phone ?? '',
      email: tasker?.emails?.[0]?.address ?? '',
      ...amountInfo,
    };
  });
}
