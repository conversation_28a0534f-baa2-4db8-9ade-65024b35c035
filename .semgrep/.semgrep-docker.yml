rules:

  - id: dockerfile-source-not-pinned
    patterns:
      - pattern-either:
          - patterns:
              - pattern: FROM $IMAGE:$VERSION@$HASH
              - metavariable-regex:
                  metavariable: $HASH
                  regex: (?!sha256:)
          - patterns:
              - pattern: FROM $IMAGE
              - pattern: FROM $IMAGE:$VERSION
              - pattern-not-inside: FROM $IMAGE:$VERSION@$HASH
    message: To ensure reproducible builds, pin Dockerfile `FROM` commands to a
      specific hash. You can find the hash by running `docker pull $IMAGE` and
      then  specify it with `$IMAGE:$VERSION@sha256:<hash goes here>`
    languages:
      - dockerfile
    severity: INFO
    metadata:
      references:
        - https://stackoverflow.com/a/33511811/4965
      category: best-practice
      technology:
        - docker
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: missing-image-version
    patterns:
      - pattern-either:
          - pattern: FROM $IMAGE
      - pattern-not: FROM $IMAGE:$VERSION
      - pattern-not: FROM $IMAGE@$DIGEST
      - pattern-not: FROM $IMAGE:$VERSION@$DIGEST
      - pattern-not: FROM scratch
    message: Detected docker image with no explicit version attached. Images should
      be tagged with an explicit version to produce deterministic container
      images -- attach a version when using  `FROM <image>`.
    severity: WARNING
    languages:
      - dockerfile
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3006
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3006
      category: best-practice
      technology:
        - dockerfile
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
        
  - id: remove-package-cache
    patterns:
      - pattern-not-inside: RUN ... && apt-get clean ...
      - pattern: RUN ... apt-get update ...
      - pattern: apt-get update
    message: The package cache was not deleted after running 'apt-get update', which
      increases the size of the image. Remove the package cache by appending '&&
      apt-get clean' at the end of apt-get command chain.
    severity: WARNING
    languages:
      - dockerfile
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3009
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3009
      category: best-practice
      technology:
        - dockerfile
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: invalid-port
    message: Detected an invalid port number. Valid ports are 0 through 65535.
    severity: ERROR
    languages:
      - dockerfile
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3011
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3011
      category: correctness
      technology:
        - dockerfile
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    pattern-either:
      - patterns:
          - pattern: EXPOSE $PORT
          - metavariable-comparison:
              metavariable: $PORT
              comparison: int($PORT) > 65535

  - id: missing-user
    patterns:
      - pattern: |
          CMD $...VARS
      - pattern-not-inside: |
          USER $USER
          ...
    fix: |
      USER non-root
      CMD $...VARS
    message: By not specifying a USER, a program in the container may run as 'root'.
      This is a security hazard. If an attacker can control a process running as
      root, they may have control over the container. Ensure that the last USER
      in a Dockerfile is a USER other than 'root'.
    severity: ERROR
    languages:
      - dockerfile
    metadata:
      cwe:
        - "CWE-269: Improper Privilege Management"
      category: security
      technology:
        - dockerfile
      confidence: MEDIUM
      owasp:
        - A04:2021 - Insecure Design
      references:
        - https://owasp.org/Top10/A04_2021-Insecure_Design
      subcategory:
        - audit
      likelihood: LOW
      impact: MEDIUM
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization

  - id: no-sudo-in-dockerfile
    patterns:
      - pattern: |
          RUN sudo ...
    message: Avoid using sudo in Dockerfiles. Running processes as a non-root user
      can help  reduce the potential impact of configuration errors and security
      vulnerabilities.
    metadata:
      category: security
      technology:
        - dockerfile
      cwe:
        - "CWE-250: Execution with Unnecessary Privileges"
      owasp:
        - A05:2021 - Security Misconfiguration
      references:
        - https://cwe.mitre.org/data/definitions/250.html
        - https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user
      subcategory:
        - audit
      likelihood: LOW
      impact: LOW
      confidence: HIGH
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - dockerfile
    severity: WARNING

  - id: selinux-separation-disabled
    patterns:
      - pattern-inside: |
          version: ...
          ...
          services:
            ...
      - pattern: |
          $SERVICE:
            ...
            image: ...
            ...
            security_opt:
              - ...
              - label:disable
    message: Service '$SERVICE' is explicitly disabling SELinux separation. This
      runs the service as an unconfined type. Remove 'label:disable' to prevent
      this.
    metadata:
      cwe:
        - "CWE-284: Improper Access Control"
      references:
        - https://www.projectatomic.io/blog/2016/03/dwalsh_selinux_containers/
        - https://docs.docker.com/engine/reference/run/#security-configuration
      category: security
      technology:
        - docker-compose
      owasp:
        - A05:2017 - Broken Access Control
        - A01:2021 - Broken Access Control
      subcategory:
        - audit
      likelihood: LOW
      impact: HIGH
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING

  - id: seccomp-confinement-disabled
    patterns:
      - pattern-inside: |
          version: ...
          ...
          services:
            ...
      - pattern: |
          $SERVICE:
            ...
            image: ...
            ...
            security_opt:
              - ...
              - seccomp:unconfined
    message: Service '$SERVICE' is explicitly disabling seccomp confinement. This
      runs the service in an unrestricted state. Remove 'seccomp:unconfined' to
      prevent this.
    metadata:
      cwe:
        - "CWE-284: Improper Access Control"
      references:
        - https://docs.docker.com/engine/security/seccomp/
      category: security
      technology:
        - docker-compose
      owasp:
        - A05:2017 - Broken Access Control
        - A01:2021 - Broken Access Control
      subcategory:
        - audit
      likelihood: LOW
      impact: HIGH
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING

  - id: privileged-service
    patterns:
      - pattern-inside: |
          version: ...
          ...
          services:
            ...
            $SERVICE:
              ...
              privileged: $TRUE
      - focus-metavariable: $TRUE
      - metavariable-regex:
          metavariable: $TRUE
          regex: (true)
    fix: |
      false
    message: Service '$SERVICE' is running in privileged mode. This grants the
      container the equivalent of root capabilities on the host machine. This
      can lead to container escapes, privilege escalation, and other security
      concerns. Remove the 'privileged' key to disable this capability.
    metadata:
      cwe:
        - "CWE-250: Execution with Unnecessary Privileges"
      owasp:
        - A06:2017 - Security Misconfiguration
        - A05:2021 - Security Misconfiguration
      references:
        - https://www.trendmicro.com/en_us/research/19/l/why-running-a-privileged-container-in-docker-is-a-bad-idea.html
        - https://containerjournal.com/topics/container-security/why-running-a-privileged-container-is-not-a-good-idea/
      category: security
      technology:
        - docker-compose
      subcategory:
        - vuln
      likelihood: HIGH
      impact: HIGH
      confidence: HIGH
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING

  - id: no-new-privileges
    patterns:
      - pattern-inside: |
          version: ...
          ...
          services:
            ...
      - pattern: |
          $SERVICE:
            ...
            image: ...
      - pattern-not: |
          $SERVICE:
            ...
            image: ...
            ...
            security_opt:
              - ...
              - no-new-privileges:true
              - ...
      - focus-metavariable: $SERVICE
    message: Service '$SERVICE' allows for privilege escalation via setuid or setgid
      binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.
    metadata:
      cwe:
        - "CWE-732: Incorrect Permission Assignment for Critical Resource"
      owasp:
        - A05:2021 - Security Misconfiguration
        - A06:2017 - Security Misconfiguration
      references:
        - https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/
        - https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt
        - https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag
      category: security
      technology:
        - docker-compose
      cwe2021-top25: true
      subcategory:
        - audit
      likelihood: LOW
      impact: HIGH
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Improper Authorization
    languages:
      - yaml
    severity: WARNING

  - id: container-privileged
    message: Found container command with extended privileges
    languages: [generic]
    severity: WARNING
    metadata:
      category: security
      subcategory: [audit]
      technology: [shell]
      cwe: "CWE-250: Execution with Unnecessary Privileges"
      confidence: MEDIUM
      likelihood: MEDIUM
      impact: HIGH
      references:
        - https://docs.docker.com/engine/reference/commandline/run/
    pattern-either:
      - pattern: docker ... --privileged
      - pattern: docker ... --cap-add=ALL

  - id: container-user-root
    message: Found container command running as root
    languages: [generic]
    severity: WARNING
    metadata:
      category: security
      subcategory: [audit]
      technology: [shell]
      cwe: "CWE-250: Execution with Unnecessary Privileges"
      confidence: MEDIUM
      likelihood: MEDIUM
      impact: HIGH
      references:
        - https://docs.docker.com/engine/reference/commandline/run/
    pattern-either:
      - pattern: docker ... -u root
      - pattern: docker ... -u 0
      - pattern: docker ... --user root

  - id: port-all-interfaces
    message: Service port is exposed on all interfaces
    languages: [yaml]
    severity: WARNING
    metadata:
      category: security
      cwe: "CWE-1327: Binding to an Unrestricted IP Address"
      subcategory: [audit]
      technology: [docker, compose]
      confidence: LOW
      likelihood: LOW
      impact: LOW
      references:
        - https://docs.docker.com/compose/compose-file/compose-file-v3/#ports
    patterns:
      - pattern-inside: |
          services:
            ...
      - pattern: |
          ports:
            - ...
            - "$PORT"
            - ...
      - focus-metavariable: $PORT
      - metavariable-regex:
          metavariable: $PORT
          regex: '^(?!127.\d{1,3}.\d{1,3}.\d{1,3}:).+'