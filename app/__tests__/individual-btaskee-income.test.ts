import {
  FA_TRANSACTION_TYPE,
  PAYMENT_METHOD,
  SOURCE_NAME_FOR_FA_TRANSACTION,
  TASK_STATUS,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import { getListBtaskeeIncome } from '~/services/individual-btaskee-income.server';

describe('getListBtaskeeIncome', () => {
  const MOCK_ISO_CODE = 'VN';
  const MOCK_TASKER_ID = 'mock-user-id';
  const MOCK_TASKER_ID_2 = 'mock-user-id-2';
  const MOCK_TASK_ID = 'mock-task-id';
  const MOCK_TASK_NOT_HAVE_AMOUNT_IN_TRANSACTION_ID =
    'mock-task-not-have-amount-id';
  const MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER_ID =
    'mock-task-id-not-accepted-tasker';

  const MOCK_TASK_DONE = {
    _id: MOCK_TASK_ID,
    status: TASK_STATUS.DONE,
    date: momentTz().toDate(),
    payment: {
      method: PAYMENT_METHOD.CASH,
    },
    acceptedTasker: [{ taskerId: MOCK_TASKER_ID }],
    duration: 2,
    changesHistory: [
      {
        key: 'UPDATE_DATE_TIME',
        createdAt: momentTz().add(5, 'days').toDate(),
      },
      {
        key: 'DONE_TASK',
        createdAt: momentTz().add(1, 'days').toDate(),
      },
    ],
  };

  const MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER = {
    _id: MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER_ID,
    status: TASK_STATUS.DONE,
    date: momentTz().toDate(),
    payment: {
      method: PAYMENT_METHOD.CASH,
    },
    duration: 2,
  };

  const MOCK_TASK_DONE_NOT_AMOUNT = {
    _id: MOCK_TASK_NOT_HAVE_AMOUNT_IN_TRANSACTION_ID,
    status: TASK_STATUS.DONE,
    date: momentTz().toDate(),
    payment: {
      method: PAYMENT_METHOD.CASH,
    },
    acceptedTasker: [{ taskerId: MOCK_TASKER_ID_2 }],
    duration: 2,
    changesHistory: [
      {
        key: 'UPDATE_DATE_TIME',
        createdAt: momentTz().add(5, 'days').toDate(),
      },
      {
        key: 'DONE_TASK',
        createdAt: momentTz().add(5, 'days').toDate(),
      },
    ],
  };

  const MOCK_TRANSACTION = {
    _id: 'mock-transaction-id',
    userId: MOCK_TASKER_ID,
    type: FA_TRANSACTION_TYPE.CREDIT,
    amount: 100000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      value: MOCK_TASK_ID,
    },
    date: momentTz().add(2, 'days').toDate(),
    createdAt: momentTz().toDate(),
  };
  const MOCK_TRANSACTION_NOT_AMOUNT = {
    _id: 'mock-transaction-not-amount-id',
    userId: MOCK_TASKER_ID_2,
    type: FA_TRANSACTION_TYPE.CREDIT,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      value: MOCK_TASK_NOT_HAVE_AMOUNT_IN_TRANSACTION_ID,
    },
    date: momentTz().add(4, 'days').toDate(),
    createdAt: momentTz().toDate(),
  };
  const MOCK_TRANSACTION_NOT_ACCEPTED_TASKER_FOR_TASK = {
    _id: 'mock-transaction-id-not-accepted-tasker',
    userId: 'tasker-id',
    type: FA_TRANSACTION_TYPE.CREDIT,
    amount: 100000,
    source: {
      name: SOURCE_NAME_FOR_FA_TRANSACTION.TASK,
      value: MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER_ID,
    },
    date: momentTz().subtract(3, 'days').toDate(),
    createdAt: momentTz().toDate(),
  };

  beforeAll(async () => {
    await Promise.all([
      getModels(MOCK_ISO_CODE).task.insertMany([
        MOCK_TASK_DONE,
        MOCK_TASK_DONE_NOT_AMOUNT,
        MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER,
      ]),
      getModels(MOCK_ISO_CODE).FATransaction.insertMany([
        MOCK_TRANSACTION,
        MOCK_TRANSACTION_NOT_AMOUNT,
        MOCK_TRANSACTION_NOT_ACCEPTED_TASKER_FOR_TASK,
      ]),
    ]);
  });

  afterAll(async () => {
    await Promise.all([
      getModels(MOCK_ISO_CODE).task.deleteMany({
        _id: {
          $in: [
            MOCK_TASK_ID,
            MOCK_TASK_NOT_HAVE_AMOUNT_IN_TRANSACTION_ID,
            MOCK_TASK_DONE_NOT_HAVE_ACCEPTED_TASKER_ID,
          ],
        },
      }),
      getModels(MOCK_ISO_CODE).FATransaction.deleteMany({
        _id: {
          $in: [
            MOCK_TRANSACTION._id,
            MOCK_TRANSACTION_NOT_AMOUNT._id,
            MOCK_TRANSACTION_NOT_ACCEPTED_TASKER_FOR_TASK._id,
          ],
        },
      }),
    ]);
  });

  it('should return empty array if filter dates are missing', async () => {
    const result = await getListBtaskeeIncome({
      isoCode: MOCK_ISO_CODE,
      filter: {
        rangeDate: {},
      },
    });
    expect(result).toEqual([]);
  });
  it('should return empty array if transaction not found', async () => {
    const result = await getListBtaskeeIncome({
      isoCode: MOCK_ISO_CODE,
      filter: {
        rangeDate: {
          from: momentTz().add(20, 'days').toDate(),
          to: momentTz().add(25, 'days').toDate(),
        },
      },
    });
    expect(result).toEqual([]);
  });

  it('should return aggregation results', async () => {
    const result = await getListBtaskeeIncome({
      isoCode: MOCK_ISO_CODE,
      filter: {
        rangeDate: {
          from: momentTz().toDate(),
          to: momentTz().add(3, 'days').toDate(),
        },
      },
    });

    const MOCK_TOTAL = 1;
    const MOCK_RESULT = [
      {
        taskId: MOCK_TASK_DONE._id,
        amount: MOCK_TRANSACTION.amount,
        transactionDate: MOCK_TRANSACTION.date,
      },
    ];

    expect(Array.isArray(result)).toBe(true);
    expect(result).toHaveLength(MOCK_TOTAL);
    expect(result).toStrictEqual(MOCK_RESULT);
  });
  it('Should throw error when amount not found in transaction', async () => {
    await expect(
      getListBtaskeeIncome({
        isoCode: MOCK_ISO_CODE,
        filter: {
          rangeDate: {
            from: momentTz().add(4, 'days').toDate(),
            to: momentTz().add(6, 'days').toDate(),
          },
        },
      }),
    ).rejects.toThrow();
  });
  it('Should throw error message when task DONE not have acceptedTasker', async () => {
    await expect(
      getListBtaskeeIncome({
        isoCode: MOCK_ISO_CODE,
        filter: {
          rangeDate: {
            from: momentTz().subtract(4, 'days').toDate(),
            to: momentTz().subtract(1, 'days').toDate(),
          },
        },
      }),
    ).rejects.toThrow();
  });
});
